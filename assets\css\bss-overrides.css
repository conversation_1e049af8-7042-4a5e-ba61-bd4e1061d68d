:root, [data-bs-theme=light] {
  --bs-primary: #ffd4d6;
  --bs-primary-rgb: 255,212,214;
  --bs-primary-text-emphasis: #665556;
  --bs-primary-bg-subtle: #FFF6F7;
  --bs-primary-border-subtle: #FFEEEF;
  --bs-secondary: #ffeaeb;
  --bs-secondary-rgb: 255,234,235;
  --bs-secondary-text-emphasis: #665E5E;
  --bs-secondary-bg-subtle: #FFFBFB;
  --bs-secondary-border-subtle: #FFF7F7;
  --bs-success: #006400;
  --bs-success-rgb: 0,100,0;
  --bs-success-text-emphasis: #002800;
  --bs-success-bg-subtle: #CCE0CC;
  --bs-success-border-subtle: #99C199;
  --bs-body-font-family: 'Open Sans', sans-serif;
}

.btn-primary {
  --bs-btn-color: #000000;
  --bs-btn-bg: #ffd4d6;
  --bs-btn-border-color: #ffd4d6;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #FFDADC;
  --bs-btn-hover-border-color: #FFD8DA;
  --bs-btn-focus-shadow-rgb: 38,32,32;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #FFDDDE;
  --bs-btn-active-border-color: #FFD8DA;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #ffd4d6;
  --bs-btn-disabled-border-color: #ffd4d6;
}

.btn-outline-primary {
  --bs-btn-color: #ffd4d6;
  --bs-btn-border-color: #ffd4d6;
  --bs-btn-focus-shadow-rgb: 255,212,214;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ffd4d6;
  --bs-btn-hover-border-color: #ffd4d6;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ffd4d6;
  --bs-btn-active-border-color: #ffd4d6;
  --bs-btn-disabled-color: #ffd4d6;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffd4d6;
}

.btn-secondary {
  --bs-btn-color: #000000;
  --bs-btn-bg: #ffeaeb;
  --bs-btn-border-color: #ffeaeb;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #FFEDEE;
  --bs-btn-hover-border-color: #FFECED;
  --bs-btn-focus-shadow-rgb: 38,35,35;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #FFEEEF;
  --bs-btn-active-border-color: #FFECED;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #ffeaeb;
  --bs-btn-disabled-border-color: #ffeaeb;
}

.btn-outline-secondary {
  --bs-btn-color: #ffeaeb;
  --bs-btn-border-color: #ffeaeb;
  --bs-btn-focus-shadow-rgb: 255,234,235;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ffeaeb;
  --bs-btn-hover-border-color: #ffeaeb;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ffeaeb;
  --bs-btn-active-border-color: #ffeaeb;
  --bs-btn-disabled-color: #ffeaeb;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffeaeb;
}

.btn-success {
  --bs-btn-color: #fff;
  --bs-btn-bg: #006400;
  --bs-btn-border-color: #006400;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #005500;
  --bs-btn-hover-border-color: #005000;
  --bs-btn-focus-shadow-rgb: 217,232,217;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #005000;
  --bs-btn-active-border-color: #004B00;
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #006400;
  --bs-btn-disabled-border-color: #006400;
}

.btn-outline-success {
  --bs-btn-color: #006400;
  --bs-btn-border-color: #006400;
  --bs-btn-focus-shadow-rgb: 0,100,0;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #006400;
  --bs-btn-hover-border-color: #006400;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #006400;
  --bs-btn-active-border-color: #006400;
  --bs-btn-disabled-color: #006400;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #006400;
}

.m-3 {
  margin: 1rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-2 {
  margin-top: .5rem !important;
  margin-bottom: .5rem !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-5 {
  margin-top: 3rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-4 {
  margin-right: 1.5rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-2 {
  margin-bottom: .5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.ms-2 {
  margin-left: .5rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.py-2 {
  padding-top: .5rem !important;
  padding-bottom: .5rem !important;
}

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.pt-2 {
  padding-top: .5rem !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pt-4 {
  padding-top: 1.5rem !important;
}

.pt-5 {
  padding-top: 3rem !important;
}

.pb-2 {
  padding-bottom: .5rem !important;
}

.pb-4 {
  padding-bottom: 1.5rem !important;
}

@media (min-width: 768px) {
  .px-md-5 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
}

@media (min-width: 992px) {
  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }
}

@media (min-width: 992px) {
  .p-lg-5 {
    padding: 3rem !important;
  }
}

@media (min-width: 992px) {
  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
}

@media (min-width: 992px) {
  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

@media (min-width: 1200px) {
  .p-xl-4 {
    padding: 1.5rem !important;
  }
}

@media (min-width: 1200px) {
  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

