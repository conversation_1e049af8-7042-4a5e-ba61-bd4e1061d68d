#!/usr/bin/env python3
"""
Test script to verify the updated get_robust_unique_visitor_count function
"""

import sys
import os
from datetime import datetime
from sqlalchemy import func

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.models import db, Analytics
from modules.analytics_process import get_robust_unique_visitor_count, get_conservative_unique_visitor_count
from flask import <PERSON>lask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    db.init_app(app)
    
    return app

def test_updated_function(target_date_str):
    """
    Test the updated get_robust_unique_visitor_count function
    """
    try:
        # Parse the target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d')
        
        # Set start and end of the day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        print(f"Testing updated function for {target_date_str}")
        print(f"Date range: {start_date} to {end_date}")
        
        # Test the updated get_robust_unique_visitor_count function
        # This should now return IP-based count (conservative approach)
        robust_count = db.session.query(
            get_robust_unique_visitor_count(include_date_in_key=True)
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        # Test the conservative function for comparison
        filters = [
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ]
        
        conservative_count = get_conservative_unique_visitor_count(db.session, filters)
        
        # Get individual counts for transparency
        unique_ips = db.session.query(
            func.count(Analytics.ip_address.distinct())
        ).filter(*filters).scalar()
        
        unique_sessions = db.session.query(
            func.count(Analytics.sessionid.distinct())
        ).filter(*filters).scalar()
        
        print(f"\n=== RESULTS ===")
        print(f"Unique IPs: {unique_ips}")
        print(f"Unique Sessions: {unique_sessions}")
        print(f"Updated get_robust_unique_visitor_count(): {robust_count}")
        print(f"get_conservative_unique_visitor_count(): {conservative_count}")
        
        # Verify the logic
        expected_conservative = min(unique_ips, unique_sessions)
        print(f"Expected conservative (MIN): {expected_conservative}")
        
        print(f"\n=== VERIFICATION ===")
        print(f"✓ Robust function returns IP count: {robust_count == unique_ips}")
        print(f"✓ Conservative function returns MIN: {conservative_count == expected_conservative}")
        
        if robust_count == unique_ips and conservative_count == expected_conservative:
            print(f"✅ All functions working correctly!")
            print(f"✅ Conservative unique visitors for {target_date_str}: {conservative_count}")
        else:
            print(f"❌ Something is wrong with the function logic")
        
        return {
            'unique_ips': unique_ips,
            'unique_sessions': unique_sessions,
            'robust_count': robust_count,
            'conservative_count': conservative_count
        }
        
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python test_updated_function.py YYYY-MM-DD")
        print("Example: python test_updated_function.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run test
    app = create_app()
    
    with app.app_context():
        results = test_updated_function(target_date)
        
        if results is None:
            print("Failed to test updated function.")
            sys.exit(1)