// Cookie Categories Configuration
const cookieCategories = {
    essensielle: {
        name: '<PERSON><PERSON>sie<PERSON>',
        description: 'Essensielle cookies håndterer brukersesioner og personverninnstillinger og kan ikke deaktiveres. De er avgjørende for at nettstedet skal fungere korrekt.',
        required: true
    },
    funksjonelle: {
        name: '<PERSON>sjon<PERSON>',
        description: 'Cookies som er nødvendig for at handlekurven og betalingsløsninger fra Stripe og deres partnere skal fungere. Uten disse tjenestene vil du ikke kunne handle på dette nettstedet.',
        required: false
    },
    statistikk: {
        name: 'Statistik<PERSON>',
        description: 'Cookies som hjelper oss å forstå hvordan besøkende bruker nettstedet.',
        required: false
    },
    markedsføring: {
        name: '<PERSON><PERSON>fø<PERSON>',
        description: 'Cookies som brukes til markedsføringsformål.',
        required: false
    }
};

// Cookie Configuration
const cookieConfig = [
    {
        name: 'Session cookie',
        category: 'essensielle',
        url: null, // Built-in cookie, no external URL needed
        cookieList: [
            {
                name: 'ketolabben_session',
                description: ''
            }
        ]
    },
    {
        name: 'Privacy consent cookie',
        category: 'essensielle',
        url: null, // Built-in cookie, no external URL needed
        cookieList: [
            {
                name: 'userConsent',
                description: ''
            }
        ]
    },
    {
        name: 'Stripe betalingsløsninger',
        category: 'funksjonelle',
        url: 'https://js.stripe.com/v3/',
        cookieList: [
            {
                name: '__stripe_mid',
                description: ''
            },
            {
                name: '__stripe_sid',
                description: ''
            },
            {
                name: 'allowed_payment_methods.https://b.stripecdn.com',
                description: ''
            },
            {
                name: 'pf.keys',
                description: ''
            },
            {
                name: 'NID',
                description: ''
            },
            {
                name: '1',
                description: ''
            },
            {
                name: '_ab',
                description: ''
            },
            {
                name: '_mf',
                description: ''
            },
            {
                name: 'id',
                description: ''
            },
            {
                name: 'm',
                description: ''
            },
            {
                name: 'datadome',
                description: ''
            },
            {
                name: '__cflb',
                description: ''
            }
        ]
    }
    // Add more cookies as needed
];

// Create and inject the banner HTML
function createBanner() {
    const banner = document.createElement('div');
    banner.id = 'consent-banner';
    banner.innerHTML = `
        <div class="consent-content">
            <div class="consent-text">
                Vi bruker cookies for å huske dine personvern-innstillinger, håndtere brukersesioner og sikre trygg betaling. 
                For at handlekurven skal fungere, må du godta cookies fra vår betalingsleverandør Stripe. 
                Vi deler IKKE data med annonsøraktører som Google Ads, Microsoft eller Meta.
            </div>
            <div class="consent-actions">
                <button class="consent-btn btn-accept" onclick="CookieConsent.acceptAll()">Godta alle</button>
                <button class="consent-btn btn-reject" onclick="CookieConsent.rejectAll()">Avvis alle</button>
                <button class="consent-btn btn-settings" onclick="CookieConsent.showSettings()">Innstillinger</button>
            </div>
        </div>
    `;
    document.body.appendChild(banner);
}

// Create and inject the settings modal HTML
function createSettingsModal() {
    const modal = document.createElement('div');
    modal.id = 'settings-modal';
    const activeCategories = CookieConsent.getActiveCategories();

    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>Velg innstillinger</h5>
                    <button type="button" class="btn-close" onclick="CookieConsent.hideSettings()">&times;</button>
                </div>
                <div class="modal-body">
                    ${Object.entries(cookieCategories)
                        .filter(([key]) => activeCategories.has(key) || cookieCategories[key].required)
                        .map(([key, category]) => `
                            <div class="category-item">
                                <div class="category-header">
                                    <strong>${category.name}</strong>
                                    <label class="toggle-switch">
                                        <input type="checkbox" 
                                            name="category-${key}"
                                            title="${category.name}"
                                            placeholder="x"
                                            id="category-${key}" 
                                            ${category.required ? 'checked disabled' : ''}
                                            onchange="CookieConsent.updateCategory('${key}', this.checked)">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="category-description">${category.description}</div>
                                ${cookieConfig
                                    .filter(cookie => cookie.category === key && cookie.cookieList)
                                    .map(cookie => `
                                        <div class="cookie-list">
                                            <div class="cookie-provider">${cookie.name}</div>
                                            <div class="cookie-details">
                                                ${cookie.cookieList.map(cookieItem => `
                                                    <small>${cookieItem.name}: ${cookieItem.description}</small>
                                                `).join('<br>')}
                                            </div>
                                        </div>
                                    `).join('')}
                            </div>
                        `).join('')}
                </div>
                <div class="modal-footer">
                    <button class="consent-btn btn-settings" onclick="CookieConsent.hideSettings()">Lukk</button>
                    <button class="consent-btn btn-accept" onclick="CookieConsent.saveSettings()">Lagre innstillinger</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

// Main Cookie Consent Manager
const CookieConsent = {
    getActiveCategories() {
        const activeCategories = new Set();
        cookieConfig.forEach(cookie => {
            if (cookie.cookieList && cookie.cookieList.length > 0) {
                activeCategories.add(cookie.category);
            }
        });
        return activeCategories;
    },

    init() {
        createBanner();
        createSettingsModal();
        
        // Check for existing consent
        const consent = this.getConsent();
        if (!consent) {
            this.showBanner();
        } else {
            this.applyConsent(consent);
        }
    },

    showBanner() {
        document.getElementById('consent-banner').style.display = 'block';
    },

    hideBanner() {
        document.getElementById('consent-banner').style.display = 'none';
    },

    showSettings() {
        document.getElementById('settings-modal').style.display = 'block';
        
        // Update checkboxes based on current consent
        const consent = this.getConsent() || {};
        const activeCategories = this.getActiveCategories();
        Object.keys(cookieCategories).forEach(category => {
            const checkbox = document.getElementById(`category-${category}`);
            if (checkbox && !cookieCategories[category].required) {
                checkbox.checked = consent[category] || false;
            }
        });

        // Initialize drag functionality
        const modal = document.querySelector('.modal-dialog');
        const header = modal.querySelector('.modal-header');
        
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;

        const dragStart = (e) => {
            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX - modal.offsetLeft;
                initialY = e.touches[0].clientY - modal.offsetTop;
            } else {
                initialX = e.clientX - modal.offsetLeft;
                initialY = e.clientY - modal.offsetTop;
            }
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
            }
        };

        const dragEnd = () => {
            isDragging = false;
        };

        const drag = (e) => {
            if (isDragging) {
                e.preventDefault();
                
                let newX, newY;
                
                if (e.type === "touchmove") {
                    newX = e.touches[0].clientX - initialX;
                    newY = e.touches[0].clientY - initialY;
                } else {
                    newX = e.clientX - initialX;
                    newY = e.clientY - initialY;
                }

                // Get viewport and modal dimensions
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const modalRect = modal.getBoundingClientRect();

                // Keep modal within viewport bounds
                newX = Math.min(Math.max(0, newX), viewportWidth - modalRect.width);
                newY = Math.min(Math.max(0, newY), viewportHeight - modalRect.height);

                modal.style.transform = 'none';
                modal.style.left = newX + 'px';
                modal.style.top = newY + 'px';
            }
        };

        // Remove any existing transform and set initial position if not already set
        if (!modal.style.left) {
            const rect = modal.getBoundingClientRect();
            modal.style.transform = 'none';
            modal.style.left = (window.innerWidth - rect.width) / 2 + 'px';
            modal.style.top = '20%';
        }

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        header.addEventListener('touchstart', dragStart);
        document.addEventListener('touchmove', drag);
        document.addEventListener('touchend', dragEnd);
    },

    hideSettings() {
        document.getElementById('settings-modal').style.display = 'none';
    },

    acceptAll() {
        const consent = {};
        const activeCategories = this.getActiveCategories();
        Object.keys(cookieCategories).forEach(category => {
            consent[category] = activeCategories.has(category) || cookieCategories[category].required;
        });
        this.saveConsent(consent);
    },

    rejectAll() {
        const consent = {};
        Object.keys(cookieCategories).forEach(category => {
            consent[category] = cookieCategories[category].required;
        });
        this.saveConsent(consent);
    },

    updateCategory(category, value) {
        const consent = this.getConsent() || {};
        if (this.getActiveCategories().has(category) || cookieCategories[category].required) {
            consent[category] = value;
        }
        return consent;
    },

    saveSettings() {
        const consent = {};
        const activeCategories = this.getActiveCategories();
        Object.keys(cookieCategories).forEach(category => {
            const checkbox = document.getElementById(`category-${category}`);
            if (activeCategories.has(category) || cookieCategories[category].required) {
                consent[category] = checkbox ? checkbox.checked : cookieCategories[category].required;
            }
        });
        this.saveConsent(consent);
        this.hideSettings();
    },

    getConsent() {
        const consent = localStorage.getItem('userConsent');
        return consent ? JSON.parse(consent) : null;
    },

    saveConsent(consentData) {
        // Filter out inactive categories
        const activeCategories = this.getActiveCategories();
        const filteredConsent = {};
        
        Object.entries(consentData).forEach(([category, value]) => {
            if (activeCategories.has(category) || cookieCategories[category].required) {
                filteredConsent[category] = value;
            }
        });

        const consent = {
            ...filteredConsent,
            timestamp: new Date().toISOString(),
            userConsentId: this.getUserSession()
        };
        
        // Save to localStorage
        localStorage.setItem('userConsent', JSON.stringify(consent));

        // Save to server
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/cookie-consent', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('Accept', 'application/json');
        
        xhr.onload = () => {
            try {
                const response = JSON.parse(xhr.responseText);
            } catch (parseError) {
            }
        };
        
        xhr.onerror = (error) => {
        };
        
        xhr.ontimeout = () => {
        };
        
        xhr.send(JSON.stringify(consent));

        this.applyConsent(consent);
        this.hideBanner();
    },

    getUserSession() {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith('ketolabben_session='))
            ?.split('=')[1];
        return cookieValue || null;
    },

    applyConsent(consent) {
        // Load approved external scripts based on consent
        cookieConfig.forEach(cookie => {
            if (consent[cookie.category] && cookie.url) {
                this.loadExternalScript(cookie.url);
            }
        });
    },

    loadExternalScript(url) {
        if (!document.querySelector(`script[src="${url}"]`)) {
            const script = document.createElement('script');
            script.src = url;
            document.head.appendChild(script);
        }
    }
};

// Expose CookieConsent methods globally
window.CookieConsent = {
    showSettings: () => {
        if (typeof CookieConsent.showSettings === 'function') {
            CookieConsent.showSettings();
        }
    },
    hideSettings: () => {
        if (typeof CookieConsent.hideSettings === 'function') {
            CookieConsent.hideSettings();
        }
    },
    acceptAll: () => {
        if (typeof CookieConsent.acceptAll === 'function') {
            CookieConsent.acceptAll();
        }
    },
    rejectAll: () => {
        if (typeof CookieConsent.rejectAll === 'function') {
            CookieConsent.rejectAll();
        }
    },
    updateCategory: (category, value) => {
        if (typeof CookieConsent.updateCategory === 'function') {
            CookieConsent.updateCategory(category, value);
        }
    },
    saveSettings: () => {
        if (typeof CookieConsent.saveSettings === 'function') {
            CookieConsent.saveSettings();
        }
    },
    init: CookieConsent.init,
    getActiveCategories: CookieConsent.getActiveCategories
};

// Initialize the cookie consent manager when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (typeof CookieConsent.init === 'function') {
        CookieConsent.init();
    }
});
