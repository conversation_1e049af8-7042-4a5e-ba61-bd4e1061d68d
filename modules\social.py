# Social Sharing Utility Functions
import urllib.parse

def generate_facebook_share_url(url, title):
    """
    Generate a Facebook share URL for the given post URL and title.
    
    Args:
        url (str): The URL of the post to share
        title (str): The title of the post
    
    Returns:
        str: Facebook share URL
    """
    base_url = "https://www.facebook.com/sharer/sharer.php"
    params = {
        "u": url,
        "quote": title
    }
    return f"{base_url}?{urllib.parse.urlencode(params)}"

def generate_twitter_share_url(url, title):
    """
    Generate a Twitter share URL for the given post URL and title.
    
    Args:
        url (str): The URL of the post to share
        title (str): The title of the post
    
    Returns:
        str: Twitter share URL
    """
    base_url = "https://twitter.com/intent/tweet"
    params = {
        "text": title,
        "url": url
    }
    return f"{base_url}?{urllib.parse.urlencode(params)}"

def generate_linkedin_share_url(url, title):
    """
    Generate a LinkedIn share URL for the given post URL and title.
    
    Args:
        url (str): The URL of the post to share
        title (str): The title of the post
    
    Returns:
        str: LinkedIn share URL
    """
    base_url = "https://www.linkedin.com/sharing/share-offsite/"
    params = {
        "url": url,
        "title": title
    }
    return f"{base_url}?{urllib.parse.urlencode(params)}"

# Web Share API Utility Functions

def is_web_share_supported():
    """
    Check if the Web Share API is supported in the current browser.
    
    Returns:
        bool: True if Web Share API is supported, False otherwise
    """
    return "share" in dir(navigator)

def validate_share_data(title=None, text=None, url=None):
    """
    Validate share data according to Web Share API specifications.
    
    Args:
        title (str, optional): Title of the content to share
        text (str, optional): Description or text to share
        url (str, optional): URL to share
    
    Returns:
        dict: Validated share data dictionary
    """
    share_data = {}
    
    if title:
        share_data['title'] = str(title)
    
    if text:
        share_data['text'] = str(text)
    
    if url:
        share_data['url'] = str(url)
    
    return share_data