import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from flask import Flask, render_template_string
from modules.structured_data import inject_structured_data, generate_organization_schema, generate_website_schema

# Create a test app
app = Flask(__name__)

@app.route('/')
def test_page():
    """Test page for structured data injection"""
    # Create a simple HTML template
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Structured Data Test</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>Structured Data Test</h1>
        <p>This page tests the injection of structured data.</p>
    </body>
    </html>
    """
    
    # Generate structured data
    structured_data = [
        generate_organization_schema(),
        generate_website_schema()
    ]
    
    # Inject structured data
    processed = inject_structured_data(html, structured_data)
    
    # Return the processed HTML
    return processed

if __name__ == '__main__':
    print("Starting test server at http://127.0.0.1:5000")
    app.run(debug=True)