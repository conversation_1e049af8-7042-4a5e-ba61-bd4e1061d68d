#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze IP address duplicates among unique visitors for a specific date
"""

import sys
import os
from datetime import datetime
from sqlalchemy import func, case
from collections import Counter

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.models import db, Analytics
from modules.analytics_process import get_robust_unique_visitor_count
from flask import Flask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    db.init_app(app)
    
    return app

def analyze_ip_duplicates_for_date(target_date_str):
    """
    Analyze IP address duplicates among visitors for a specific date
    
    Args:
        target_date_str (str): Date in format YYYY-MM-DD
    
    Returns:
        dict: Analysis results
    """
    try:
        # Parse the target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d')
        
        # Set start and end of the day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        print(f"Analyzing IP address patterns for {target_date_str}")
        print(f"Date range: {start_date} to {end_date}")
        
        # Get all unique sessions (non-bot) for the date with their IP addresses
        unique_sessions = db.session.query(
            Analytics.sessionid,
            Analytics.ip_address,
            func.min(Analytics.timestamp).label('first_visit'),
            func.count(Analytics.id).label('page_views')
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).group_by(Analytics.sessionid).all()
        
        print(f"Total unique sessions: {len(unique_sessions)}")
        
        # Count IP addresses
        ip_counter = Counter()
        session_by_ip = {}
        
        for session in unique_sessions:
            ip = session.ip_address
            if ip:
                ip_counter[ip] += 1
                if ip not in session_by_ip:
                    session_by_ip[ip] = []
                session_by_ip[ip].append({
                    'session': session.sessionid,
                    'first_visit': session.first_visit,
                    'page_views': session.page_views
                })
        
        # Find duplicate IPs
        duplicate_ips = {ip: count for ip, count in ip_counter.items() if count > 1}
        
        print(f"\n=== IP ADDRESS ANALYSIS ===")
        print(f"Total unique IP addresses: {len(ip_counter)}")
        print(f"IP addresses with multiple sessions: {len(duplicate_ips)}")
        print(f"Total sessions from duplicate IPs: {sum(duplicate_ips.values())}")
        
        if duplicate_ips:
            print(f"\n=== DUPLICATE IP ADDRESSES ===")
            # Sort by number of sessions (descending)
            sorted_duplicates = sorted(duplicate_ips.items(), key=lambda x: x[1], reverse=True)
            
            for ip, session_count in sorted_duplicates[:10]:  # Show top 10
                print(f"\nIP: {ip} ({session_count} sessions)")
                sessions = session_by_ip[ip]
                for i, session_info in enumerate(sessions, 1):
                    session_id = session_info['session'][:8] + "..."
                    first_visit = session_info['first_visit']
                    page_views = session_info['page_views']
                    print(f"  {i}. Session: {session_id}, First visit: {first_visit}, Page views: {page_views}")
        
        # Calculate the robust unique visitor count for comparison
        robust_unique_visitors = db.session.query(
            get_robust_unique_visitor_count(include_date_in_key=False)
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"\n=== COMPARISON ===")
        print(f"Unique sessions (session-based): {len(unique_sessions)}")
        print(f"Unique visitors (robust method): {robust_unique_visitors}")
        print(f"Unique IP addresses: {len(ip_counter)}")
        
        # Show statistics about sessions per IP
        sessions_per_ip = list(ip_counter.values())
        avg_sessions_per_ip = sum(sessions_per_ip) / len(sessions_per_ip)
        max_sessions_per_ip = max(sessions_per_ip)
        
        print(f"\n=== IP STATISTICS ===")
        print(f"Average sessions per IP: {avg_sessions_per_ip:.2f}")
        print(f"Maximum sessions from single IP: {max_sessions_per_ip}")
        print(f"IPs with only 1 session: {len([x for x in sessions_per_ip if x == 1])}")
        print(f"IPs with 2+ sessions: {len([x for x in sessions_per_ip if x > 1])}")
        
        return {
            'total_unique_sessions': len(unique_sessions),
            'total_unique_ips': len(ip_counter),
            'duplicate_ips': len(duplicate_ips),
            'robust_unique_visitors': robust_unique_visitors
        }
        
    except ValueError as e:
        print(f"Error parsing date: {e}")
        print("Please use format YYYY-MM-DD (e.g., 2025-06-16)")
        return None
    except Exception as e:
        print(f"Error querying database: {e}")
        return None

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python analyze_ip_duplicates.py YYYY-MM-DD")
        print("Example: python analyze_ip_duplicates.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run analysis
    app = create_app()
    
    with app.app_context():
        results = analyze_ip_duplicates_for_date(target_date)
        
        if results is None:
            print("Failed to analyze IP duplicates.")
            sys.exit(1)