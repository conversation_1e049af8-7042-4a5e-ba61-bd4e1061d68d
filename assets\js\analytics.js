// Analytics Dashboard JavaScript
// Handles chart creation, data loading, and dashboard updates

// Chart Configuration Factory
const chartConfig = {
    createBaseOptions: (title, yAxisLabel) => ({
        responsive: true,
        maintainAspectRatio: true,
        layout: { padding: { top: 10, bottom: 10, left: 10, right: 10 } },
        scales: {
            y: {
                beginAtZero: true,
                title: { display: true, text: yAxisLabel },
                ticks: {
                    precision: 0,
                    callback: value => Number.isInteger(value) ? value : ''
                }
            },
            x: {
                title: { display: true, text: title }
            }
        },
        plugins: {
            legend: { display: true },
            tooltip: { mode: 'index', intersect: false }
        }
    }),

    calculateYAxisMax: (data, padding = 1.2) => 
        Math.max(5, Math.ceil(Math.max(...(data || [])) * padding))
};

// Utility Functions
const utils = {
    // HTML escaping function
    escapeHtml: (unsafe) => {
        if (unsafe == null) return '';
        return unsafe
            .toString()
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    },

    formatDuration: (seconds) => {
        if (!seconds || seconds < 0) return '0s';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        
        const parts = [];
        if (hours > 0) parts.push(`${hours}h`);
        if (minutes > 0) parts.push(`${minutes}m`);
        if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}s`);
        
        return parts.join(' ');
    },

    safeDestroyChart: (chartVar) => {
        try {
            if (chartVar?.destroy) chartVar.destroy();
        } catch (error) {
            console.warn('Error destroying chart:', error);
        }
    },

    updateElement: (id, value, suffix = '') => {
        const element = document.getElementById(id);
        if (element) element.textContent = value + suffix;
    },

    updateList: (elementId, data, template) => {
        const element = document.getElementById(elementId);
        if (!element || !data) return;

        element.innerHTML = data.map(item => template(item)).join('');
    },

    sortTableData: (data, column, ascending = true) => {
        return [...data].sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];
            
            // Convert percentage strings to numbers
            if (typeof aVal === 'string' && aVal.endsWith('%')) {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            }
            
            // Convert string numbers to actual numbers
            if (typeof aVal === 'string' && !isNaN(aVal)) {
                aVal = parseFloat(aVal);
                bVal = parseFloat(aVal);
            }
            
            if (aVal < bVal) return ascending ? -1 : 1;
            if (aVal > bVal) return ascending ? 1 : -1;
            return 0;
        });
    },

    shouldSortAscending: (columnName) => {
        // Numeric columns default to descending
        const numericColumns = ['visits', 'unique_visitors', 'percentage', 'count', 'total_visits'];
        return !numericColumns.includes(columnName.toLowerCase());
    },

    updateTable: (tableId, { header, data }) => {
        // Validate input parameters
        if (!tableId || !data || !Array.isArray(data) || data.length === 0) {
            console.warn(`Invalid or empty data for table ${tableId}`);
            return;
        }

        if (!header || !Array.isArray(header)) {
            // Try to generate header from first data item if header is not provided
            if (data[0] && typeof data[0] === 'object') {
                header = Object.keys(data[0]).map(key => ({ name: key }));
            } else {
                console.warn(`Invalid header for table ${tableId}`);
                return;
            }
        }

        const table = document.getElementById(tableId);
        if (!table) {
            console.warn(`Table element not found: ${tableId}`);
            return;
        }

        // Store the original data for sorting
        table.originalData = data;
        
        // Clear existing content
        table.innerHTML = '';
        
        // Add header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        header.forEach((col, index) => {
            const th = document.createElement('th');
            th.textContent = col.name || col;
            th.style.cursor = 'pointer';
            
            // Set initial sort direction based on column type
            const firstDataItem = data[0];
            const columnKey = firstDataItem ? Object.keys(firstDataItem)[index] : null;
            const initialAscending = columnKey ? utils.shouldSortAscending(columnKey) : true;
            th.dataset.sortDirection = 'none';
            
            th.dataset.columnIndex = index;
            
            // Add click handler for sorting
            th.addEventListener('click', (e) => {
                const allHeaders = headerRow.querySelectorAll('th');
                allHeaders.forEach(h => {
                    if (h !== e.target) h.dataset.sortDirection = 'none';
                });
                
                const currentDirection = e.target.dataset.sortDirection;
                const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
                e.target.dataset.sortDirection = newDirection;
                
                // Sort the data
                const sortedData = utils.sortTableData(
                    table.originalData,
                    columnKey,
                    newDirection === 'asc'
                );
                
                // Update table body with sorted data
                const tbody = table.querySelector('tbody');
                tbody.innerHTML = '';
                sortedData.forEach(row => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = Object.values(row).map(val => `<td>${val}</td>`).join('');
                    tbody.appendChild(tr);
                });
            });
            
            // If it's a numeric column, trigger initial descending sort
            if (!initialAscending && index === 0) {
                setTimeout(() => th.click(), 0);
            }
            
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Add data rows
        const tbody = document.createElement('tbody');
        data.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = Object.values(row).map(val => `<td>${val}</td>`).join('');
            tbody.appendChild(tr);
        });
        table.appendChild(tbody);
    },

    updateReferrerTable: (referrerData) => {
        utils.updateTable('referrerTable', {
            header: [
                { name: 'Source', key: 'source' },
                { name: 'Total Visits', key: 'total_visits', numeric: true },
                { name: 'Unique Visitors', key: 'unique_visitors', numeric: true },
                { name: 'Percentage', key: 'percentage', numeric: true }
            ],
            data: referrerData.map(item => ({
                source: item.source || 'direct',
                total_visits: item.total_visits || 0,
                unique_visitors: item.unique_visitors || 0,
                percentage: item.percentage || 0
            }))
        });
    },

    updateStatusCodeReportTable: (statusCodeReport) => {
        const tableBody = document.querySelector('#statusCodeReportTableBody');
        if (!tableBody) {
            console.warn('Status Code Report table body not found');
            return;
        }

        // Clear existing rows
        tableBody.innerHTML = '';

        // Validate input
        if (!statusCodeReport || !Array.isArray(statusCodeReport)) {
            console.warn('Invalid status code report data: expected an array but got', typeof statusCodeReport);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="6" class="text-center text-muted">
                    No status code data available
                </td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // If empty array, show a message
        if (statusCodeReport.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="6" class="text-center text-muted">
                    No status code data available
                </td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // Add CSS for sort indicators if not already added
        if (!document.getElementById('tableSortStyles')) {
            const style = document.createElement('style');
            style.id = 'tableSortStyles';
            style.textContent = `
                th[data-sort-direction]:after {
                    content: '';
                    float: right;
                    margin-top: 7px;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                }
                th[data-sort-direction=asc]:after {
                    border-bottom: 5px solid #666;
                }
                th[data-sort-direction=desc]:after {
                    border-top: 5px solid #666;
                }
                th[data-sort-direction=none]:after {
                    border-top: 5px solid #ddd;
                }
                th[data-sort-direction] {
                    padding-right: 18px;
                }
            `;
            document.head.appendChild(style);
        }

        // Function to render table rows
        const renderRows = (data) => {
            tableBody.innerHTML = '';
            data.forEach(entry => {
                try {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${entry?.status_code || 'Unknown'}</td>
                        <td>${(entry?.count || 0).toLocaleString()}</td>
                        <td>${(entry?.unique_visitors || 0).toLocaleString()}</td>
                        <td>${(entry?.percentage || 0).toFixed(2)}%</td>
                        <td>
                            <details>
                                <summary>Top URLs (${(entry?.top_urls || []).length})</summary>
                                <ul class="list-unstyled mb-0">
                                    ${(entry?.top_urls || []).map(url => 
                                        `<li>${utils.escapeHtml(url?.url || 'Unknown')} (${(url?.count || 0).toLocaleString()} times)</li>`
                                    ).join('')}
                                </ul>
                            </details>
                        </td>
                        <td>
                            <details>
                                <summary>Top Referrers (${(entry?.top_referrers || []).length})</summary>
                                <ul class="list-unstyled mb-0">
                                    ${(entry?.top_referrers || []).map(referrer => 
                                        `<li>${utils.escapeHtml(referrer?.referrer || 'Direct')} (${(referrer?.count || 0).toLocaleString()} times)</li>`
                                    ).join('')}
                                </ul>
                            </details>
                        </td>
                    `;
                    tableBody.appendChild(row);
                } catch (error) {
                    console.error('Error processing status code entry:', error, entry);
                }
            });
        };

        // Initial render
        renderRows(statusCodeReport);

        // Add sorting functionality
        const table = document.getElementById('statusCodeReportTable');
        if (table) {
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                if (index > 3) return; // Don't add sorting to the details columns

                header.style.cursor = 'pointer';
                header.dataset.sortDirection = 'none';

                header.addEventListener('click', () => {
                    try {
                        const ascending = header.dataset.sortDirection !== 'asc';
                        
                        // Remove sort classes from all headers
                        headers.forEach(h => {
                            h.dataset.sortDirection = 'none';
                        });
                        
                        // Add sort class to clicked header
                        header.dataset.sortDirection = ascending ? 'asc' : 'desc';
                        
                        // Sort the data
                        const sortedData = [...statusCodeReport].sort((a, b) => {
                            let aVal, bVal;
                            
                            // Safely get values with fallbacks
                            switch(index) {
                                case 0: // Status Code
                                    aVal = a?.status_code || 0;
                                    bVal = b?.status_code || 0;
                                    break;
                                case 1: // Count
                                    aVal = a?.count || 0;
                                    bVal = b?.count || 0;
                                    break;
                                case 2: // Unique Visitors
                                    aVal = a?.unique_visitors || 0;
                                    bVal = b?.unique_visitors || 0;
                                    break;
                                case 3: // Percentage
                                    aVal = a?.percentage || 0;
                                    bVal = b?.percentage || 0;
                                    break;
                                default:
                                    aVal = 0;
                                    bVal = 0;
                            }
                            
                            // Handle string vs number comparison
                            if (typeof aVal === 'string' && typeof bVal === 'string') {
                                return ascending ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                            }
                            
                            // Convert to numbers for numerical comparison
                            aVal = Number(aVal);
                            bVal = Number(bVal);
                            
                            // Handle NaN values
                            if (isNaN(aVal)) aVal = 0;
                            if (isNaN(bVal)) bVal = 0;
                            
                            return ascending ? aVal - bVal : bVal - aVal;
                        });
                        
                        // Re-render table with sorted data
                        renderRows(sortedData);
                    } catch (error) {
                        console.error('Error sorting table:', error);
                    }
                });
            });
        }
    },

    updateDevicesAndBrowsers: (data) => {
        if (!data) {
            console.warn('No visitor demographics data available');
            return;
        }

        const { device_report, browser_report, os_report } = data;

        // Update Device Types
        const deviceReport = document.getElementById('deviceReport');
        if (deviceReport) {
            if (device_report && Array.isArray(device_report)) {
                deviceReport.innerHTML = device_report
                    .sort((a, b) => b.percentage - a.percentage)
                    .map(item => `
                        <tr>
                            <td>
                                <span class="text-capitalize">${item.device}</span>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar" role="progressbar" style="width: ${item.percentage}%"></div>
                                </div>
                            </td>
                            <td class="text-end">${item.percentage.toFixed(2)}%</td>
                        </tr>
                    `).join('');
            } else {
                deviceReport.innerHTML = '<tr><td colspan="2" class="text-center text-muted">No device data available</td></tr>';
            }
        }

        // Update Browsers
        const browserReport = document.getElementById('browserReport');
        if (browserReport) {
            if (browser_report && Array.isArray(browser_report)) {
                browserReport.innerHTML = browser_report
                    .sort((a, b) => b.percentage - a.percentage)
                    .map(item => `
                        <tr>
                            <td>
                                <span class="text-capitalize">${item.browser}</span>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar" role="progressbar" style="width: ${item.percentage}%"></div>
                                </div>
                            </td>
                            <td class="text-end">${item.percentage.toFixed(2)}%</td>
                        </tr>
                    `).join('');
            } else {
                browserReport.innerHTML = '<tr><td colspan="2" class="text-center text-muted">No browser data available</td></tr>';
            }
        }

        // Update Operating Systems
        const osReport = document.getElementById('osReport');
        if (osReport) {
            if (os_report && Array.isArray(os_report)) {
                osReport.innerHTML = os_report
                    .sort((a, b) => b.percentage - a.percentage)
                    .map(item => `
                        <tr>
                            <td>
                                <span class="text-capitalize">${item.os}</span>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar" role="progressbar" style="width: ${item.percentage}%"></div>
                                </div>
                            </td>
                            <td class="text-end">${item.percentage.toFixed(2)}%</td>
                        </tr>
                    `).join('');
            } else {
                osReport.innerHTML = '<tr><td colspan="2" class="text-center text-muted">No OS data available</td></tr>';
            }
        }
    },

    populateUTMSourcesTable: (utmSources) => {
        // Validate input
        if (!utmSources || !Array.isArray(utmSources)) {
            console.warn('Invalid UTM sources data: expected an array but got', typeof utmSources);
            // Create empty table to show structure
            utils.updateTable('utmSourcesTable', {
                header: [
                    { name: 'Source', key: 'source' },
                    { name: 'Total Visits', key: 'total_visits' },
                    { name: 'Unique Visitors', key: 'unique_visitors' },
                    { name: 'Percentage', key: 'percentage' }
                ],
                data: []
            });
            return;
        }

        utils.updateTable('utmSourcesTable', {
            header: [
                { name: 'Source', key: 'source' },
                { name: 'Total Visits', key: 'total_visits' },
                { name: 'Unique Visitors', key: 'unique_visitors' },
                { name: 'Percentage', key: 'percentage' }
            ],
            data: utmSources.map(source => ({
                source: source?.source || 'Unknown',
                total_visits: source?.total_visits || 0,
                unique_visitors: source?.unique_visitors || 0,
                percentage: source?.percentage || 0
            }))
        });
    },

    populateAllPagesTable: (allPages) => {
        utils.updateTable('allPagesTable', {
            header: [
                { name: 'URL', key: 'url' },
                { name: 'Total Hits', key: 'hits' },
                { name: 'Unique Visitors', key: 'unique_visitors' },
                { name: 'Percentage', key: 'percentage' }
            ],
            data: allPages.map(page => ({
                url: page.url,
                hits: page.hits,
                unique_visitors: page.unique_visitors,
                percentage: page.percentage
            }))
        });
    },

    populateEntryPagesTable: (entryPages) => {
        utils.updateTable('entryPagesTable', {
            header: [
                { name: 'URL', key: 'url' },
                { name: 'Total Visits', key: 'total_visits' },
                { name: 'Unique Visitors', key: 'unique_visitors' },
                { name: 'Percentage', key: 'percentage' }
            ],
            data: entryPages
        });
    },

    populateExitPagesTable: (exitPages) => {
        utils.updateTable('exitPagesTable', {
            header: [
                { name: 'URL', key: 'url' },
                { name: 'Total Visits', key: 'total_visits' },
                { name: 'Unique Visitors', key: 'unique_visitors' },
                { name: 'Percentage', key: 'percentage' }
            ],
            data: exitPages
        });
    },

    updateOverviewStats: (data) => {
        if (!data) {
            console.warn('No overview data available');
            return;
        }

        // Update overview metrics using utility function
        utils.updateElement('totalPageviews', data.total_pageviews || 0);
        utils.updateElement('uniqueVisitors', data.unique_visitors || 0);
        utils.updateElement('pagesPerSession', (data.pageviews_per_session || data.pages_per_session || 0).toFixed(2));
        utils.updateElement('avgVisitDuration', data.avg_visit_duration ? data.avg_visit_duration.toFixed(2) : 0, ' min');
        utils.updateElement('bounceRate', (data.bounce_rate || 0).toFixed(1), '%');
        utils.updateElement('totalDetections', data.bot_visits || 0);

        // Log the overview data for debugging
        console.log('Overview data:', {
            pageviews: data.total_pageviews,
            visitors: data.unique_visitors,
            pagesPerSession: data.pageviews_per_session || data.pages_per_session,
            duration: data.avg_visit_duration,
            bounceRate: data.bounce_rate
        });

        // Update bot details table
        const botTableBody = document.getElementById('botDetailsList');
        if (botTableBody) {
            if (data.bot_details && data.bot_details.length > 0) {
                const rows = data.bot_details.map(bot => `
                    <tr>
                        <td>${utils.escapeHtml(bot.user_agent)}</td>
                        <td class="text-end">${bot.count}</td>
                        <td class="text-end">${new Date(bot.last_seen).toLocaleDateString()}</td>
                    </tr>
                `).join('');
                botTableBody.innerHTML = rows;

                // Initialize sorting with proper column types
                utils.updateTable('botDetailsTable', {
                    header: [
                        { name: 'Bot Name/User Agent', key: 'user_agent' },
                        { name: 'Detections', key: 'count', numeric: true },
                        { name: 'Last Seen', key: 'last_seen', date: true }
                    ],
                    data: data.bot_details.map(bot => ({
                        'user_agent': utils.escapeHtml(bot.user_agent),
                        'count': bot.count,
                        'last_seen': new Date(bot.last_seen).toLocaleDateString()
                    }))
                });
            } else {
                botTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No bot activity detected</td></tr>';
            }
        }

        // Update sparkline charts
        const charts = document.querySelectorAll('.sparkline-chart');
        charts.forEach((canvas, index) => {
            const ctx = canvas.getContext('2d');
            const trend = (data.trends && data.trends[index]) || [];
            
            // Clear previous chart
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (trend.length > 0) {
                const values = trend.map(point => point.value || 0);
                const max = Math.max(...values);
                const min = Math.min(...values);
                const range = max - min;
                
                // Start path
                ctx.beginPath();
                ctx.strokeStyle = '#0d6efd';
                ctx.lineWidth = 2;
                
                // Plot points
                values.forEach((value, i) => {
                    const x = (i / (values.length - 1)) * canvas.width;
                    const normalizedY = range === 0 ? 0.5 : (value - min) / range;
                    const y = canvas.height - (normalizedY * canvas.height);
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                
                // Draw line
                ctx.stroke();
            }
        });
    },
};

// Chart Creation Functions
class AnalyticsCharts {
    static createTimeSeriesChart(ctx, data, config) {
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: config.label,
                    data: data.data || [],
                    backgroundColor: config.bgColor,
                    borderColor: config.borderColor,
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                ...chartConfig.createBaseOptions(config.xTitle, config.yTitle),
                scales: {
                    ...chartConfig.createBaseOptions(config.xTitle, config.yTitle).scales,
                    y: {
                        ...chartConfig.createBaseOptions(config.xTitle, config.yTitle).scales.y,
                        max: chartConfig.calculateYAxisMax(data.data)
                    }
                }
            }
        });
    }

    static createDistributionChart(ctx, data, config) {
        return new Chart(ctx, {
            type: 'bar',
            data: {
                labels: config.labels,
                datasets: [{
                    label: config.label,
                    data: data || [],
                    backgroundColor: config.bgColor,
                    borderColor: config.borderColor,
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                ...chartConfig.createBaseOptions(config.xTitle, config.yTitle),
                scales: {
                    ...chartConfig.createBaseOptions(config.xTitle, config.yTitle).scales,
                    y: {
                        ...chartConfig.createBaseOptions(config.xTitle, config.yTitle).scales.y,
                        max: chartConfig.calculateYAxisMax(data)
                    }
                }
            }
        });
    }
}

// Dashboard Update Functions
class AnalyticsDashboard {
    static updateCharts(data) {
        // Validate Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js library is not loaded');
            return;
        }

        if (!data?.time_series) {
            console.error('No time series data found');
            return;
        }

        // Detailed logging for debugging
        console.log('Received data for charts:', data);
        console.log('Pageviews labels:', data.time_series.pageviews_over_time?.labels);
        console.log('Pageviews data:', data.time_series.pageviews_over_time?.data);
        console.log('Unique visitors labels:', data.time_series.unique_visitors_over_time?.labels);
        console.log('Unique visitors data:', data.time_series.unique_visitors_over_time?.data);

        const charts = ['pageviews', 'uniqueVisitors', 'weeklyTraffic', 'hourlyTraffic'];
        charts.forEach(chart => utils.safeDestroyChart(window[`${chart}Chart`]));

        const contexts = {};
        charts.forEach(chart => {
            const ctx = document.getElementById(`${chart}Chart`);
            if (!ctx) {
                console.error(`${chart} chart canvas not found`);
                return;
            }
            contexts[chart] = ctx.getContext('2d');
        });

        try {
            // Pageviews Chart
            window.pageviewsChart = AnalyticsCharts.createTimeSeriesChart(
                contexts.pageviews,
                data.time_series.pageviews_over_time,
                {
                    label: 'Pageviews',
                    bgColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    xTitle: 'Date',
                    yTitle: 'Number of Pageviews'
                }
            );

            // Unique Visitors Chart
            window.uniqueVisitorsChart = AnalyticsCharts.createTimeSeriesChart(
                contexts.uniqueVisitors,
                data.time_series.unique_visitors_over_time,
                {
                    label: 'Unique Visitors',
                    bgColor: 'rgba(153, 102, 255, 0.2)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    xTitle: 'Date',
                    yTitle: 'Number of Unique Visitors'
                }
            );

            // Weekly Traffic Chart
            window.weeklyTrafficChart = AnalyticsCharts.createDistributionChart(
                contexts.weeklyTraffic,
                data.weekly_traffic_distribution,
                {
                    label: 'Weekly Traffic (pageviews)',
                    bgColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    labels: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
                    xTitle: 'Day of the Week',
                    yTitle: 'Number of Pageviews'
                }
            );

            // Hourly Traffic Chart
            window.hourlyTrafficChart = AnalyticsCharts.createDistributionChart(
                contexts.hourlyTraffic,
                data.hourly_traffic_distribution,
                {
                    label: 'Hourly Traffic (pageviews)',
                    bgColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                    xTitle: 'Hour of the Day',
                    yTitle: 'Number of Pageviews'
                }
            );
        } catch (error) {
            console.error('Error creating charts:', error);
        }
    }

    static updateMetrics(data) {
        console.log('Received dashboard data:', data);
        const { performance = {}, overview = {} } = data;

        // Performance Metrics
        ['avg', 'min', 'max'].forEach(type => {
            utils.updateElement(
                `${type}ProcessingTime`,
                (performance[`${type}_processing_time`] || 0).toFixed(3),
                ' ms'
            );
        });

        // Overview Metrics
        utils.updateOverviewStats(overview);
    }

    static updateTables(data) {
        if (!data) {
            console.warn('No data provided to updateTables');
            return;
        }

        const { traffic_sources, page_analytics, visitor_demographics } = data;

        // Update Referrer Report Table
        if (traffic_sources?.referrer_report) {
            utils.updateReferrerTable(traffic_sources.referrer_report.map(item => ({
                source: item.domain || 'Unknown',
                total_visits: item.total_visits || 0,
                unique_visitors: item.unique_visitors || 0,
                percentage: item.percentage || 0,
                urls: item.urls || []
            })));
        }

        // All Pages
        if (page_analytics?.all_pages) {
            utils.populateAllPagesTable(page_analytics.all_pages);
        }

        // Entry Pages
        if (page_analytics?.entry_pages) {
            utils.updateTable('entryPagesTable', {
                header: ['URL', 'Total Visits', 'Unique Visitors', 'Percentage'],
                data: page_analytics.entry_pages.map(item => ({
                    url: item.url || '',
                    total_visits: item.total_visits || 0,
                    unique_visitors: item.unique_visitors || 0,
                    percentage: item.percentage || 0
                }))
            });
        }
        
        // Exit Pages
        if (page_analytics?.exit_pages) {
            utils.updateTable('exitPagesTable', {
                header: ['URL', 'Total Visits', 'Unique Visitors', 'Percentage'],
                data: page_analytics.exit_pages.map(item => ({
                    url: item.url || '',
                    total_visits: item.total_visits || 0,
                    unique_visitors: item.unique_visitors || 0,
                    percentage: item.percentage || 0
                }))
            });
        }

        // Demographics
        if (visitor_demographics?.country_report) {
            utils.updateTable('countryTable', {
                header: ['Country', 'Total Visits', 'Unique Visitors', 'Percentage'],
                data: visitor_demographics.country_report.map(item => ({
                    country: item.country || 'Unknown',
                    total_visits: item.total_visits || 0,
                    unique_visitors: item.unique_visitors || 0,
                    percentage: item.percentage || 0
                }))
            });
        }

        // Device, Browser, and OS Reports
        if (visitor_demographics) {
            utils.updateDevicesAndBrowsers(visitor_demographics);
        }

        // UTM Sources
        const utmSources = traffic_sources?.utm_sources;
        if (utmSources && Array.isArray(utmSources)) {
            utils.populateUTMSourcesTable(utmSources);
        } else {
            console.warn('UTM sources data is not in the expected array format:', utmSources);
            utils.populateUTMSourcesTable([]);
        }

        // Status Code Report
        if (page_analytics?.status_code_report) {
            utils.updateStatusCodeReportTable(page_analytics.status_code_report);
        }
    }
}

// Main Application Logic
document.addEventListener('DOMContentLoaded', () => {
    const loadAnalyticsData = () => {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        fetch(`/analytics.html/data?start_date=${startDate}&end_date=${endDate}`)
            .then(response => response.json())
            .then(data => {
                AnalyticsDashboard.updateMetrics(data);
                AnalyticsDashboard.updateTables(data);
                AnalyticsDashboard.updateCharts(data);
            })
            .catch(error => console.error('Error loading analytics data:', error));
    };

    // Initial load
    loadAnalyticsData();

    // Setup event listeners
    document.getElementById('dateRangeForm').addEventListener('submit', e => {
        e.preventDefault();
        loadAnalyticsData();
    });
});