from flask import request, current_app, jsonify, Blueprint
from .models import db, GDPR
import hashlib
import json
import logging
from datetime import datetime
import traceback

# Create blueprint
cookie_bp = Blueprint('cookie', __name__)

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

def hash_ip(ip_address):
    """
    Create a secure hash of the IP address
    """
    salt = current_app.config.get('SECRET_KEY', '')
    return hashlib.sha256((ip_address + salt).encode()).hexdigest()

@cookie_bp.route('/api/cookie-consent', methods=['POST'])
def save_cookie_consent_route():
    """Save cookie consent preferences to the database"""
    try:
        # Detailed logging of request
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request method: {request.method}")
        logger.debug(f"Request content type: {request.content_type}")

        # Log raw request data for debugging
        raw_data = request.get_data(as_text=True)
        logger.debug(f"Raw request data: {raw_data}")

        # Try parsing JSON with error handling
        try:
            consent_data = request.get_json(force=True)
        except Exception as json_error:
            logger.error(f"JSON parsing error: {json_error}")
            logger.error(f"Raw data that caused error: {raw_data}")
            return jsonify({
                'error': 'Invalid JSON', 
                'details': str(json_error)
            }), 400

        logger.debug(f"Parsed consent data: {consent_data}")

        # Validate consent data
        if not consent_data or 'userConsentId' not in consent_data:
            logger.error("Invalid consent data: Missing userConsentId")
            return jsonify({
                'error': 'Invalid consent data', 
                'details': 'Missing userConsentId'
            }), 400

        # Extract the consent ID and remove it from the data to be stored
        consent_id = consent_data.pop('userConsentId')
        
        # Save the consent
        result = save_cookie_consent(consent_data, consent_id)
        
        if result:
            logger.info(f"Cookie consent saved successfully for consent_id: {consent_id}")
            return jsonify({
                'status': 'success', 
                'consent_id': consent_id
            }), 200
        else:
            logger.error(f"Failed to save cookie consent for consent_id: {consent_id}")
            return jsonify({
                'error': 'Failed to save consent', 
                'details': 'Database save failed'
            }), 500
    except Exception as e:
        # Comprehensive error logging
        logger.error("Unexpected error in save_cookie_consent_route")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': 'Internal server error', 
            'details': str(e)
        }), 500

@cookie_bp.route('/api/cookie-consent/<consent_id>', methods=['DELETE'])
def revoke_cookie_consent_route(consent_id):
    """Revoke cookie consent preferences in the database"""
    try:
        logger.info(f"Revoking cookie consent for consent_id: {consent_id}")
        success = revoke_cookie_consent(consent_id)
        if success:
            logger.info(f"Cookie consent revoked successfully for consent_id: {consent_id}")
            return jsonify({
                'status': 'success'
            }), 200
        logger.error(f"Failed to revoke cookie consent for consent_id: {consent_id}")
        return jsonify({
            'error': 'Consent not found', 
            'details': 'Consent not found in database'
        }), 404
    except Exception as e:
        # Comprehensive error logging
        logger.error("Unexpected error in revoke_cookie_consent_route")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': 'Internal server error', 
            'details': str(e)
        }), 500

def save_cookie_consent(consent_data, consent_id):
    """
    Save or update cookie consent preferences in the database
    
    Args:
        consent_data (dict): Dictionary containing consent preferences
        consent_id (str): Unique identifier for the consent
        
    Returns:
        GDPR: Created or updated GDPR entry
    """
    try:
        # Get client IP and hash it
        ip_address = request.remote_addr or '0.0.0.0'
        ip_hash = hash_ip(ip_address)
        
        logger.debug(f"IP Address: {ip_address}, IP Hash: {ip_hash}")
        
        # Check if consent already exists
        existing_consent = GDPR.query.filter_by(consent_id=consent_id, revoked=None).first()
        
        # Serialize consent data to JSON
        preferences_json = json.dumps(consent_data)
        logger.debug(f"Serialized preferences: {preferences_json}")
        
        if existing_consent:
            # Update existing consent
            existing_consent.preferences = preferences_json
            existing_consent.timestamp = datetime.now()
            existing_consent.ip_hash = ip_hash
            db.session.commit()
            logger.info(f"Updated existing consent for consent_id: {consent_id}")
            return existing_consent
        
        # Create new consent entry
        new_consent = GDPR(
            consent_id=consent_id,
            ip_hash=ip_hash,
            preferences=preferences_json,
            timestamp=datetime.now(),
            revoked=None
        )
        
        db.session.add(new_consent)
        db.session.commit()
        logger.info(f"Created new consent entry for consent_id: {consent_id}")
        return new_consent
        
    except Exception as e:
        db.session.rollback()
        logger.error("Error in save_cookie_consent")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def get_cookie_consent(consent_id):
    """
    Retrieve cookie consent preferences from the database
    
    Args:
        consent_id (str): Unique identifier for the consent
        
    Returns:
        dict: Cookie consent preferences or None if not found or revoked
    """
    try:
        logger.info(f"Retrieving cookie consent for consent_id: {consent_id}")
        consent = GDPR.query.filter_by(consent_id=consent_id, revoked=None).first()
        if consent and consent.preferences:
            logger.debug(f"Found consent preferences for consent_id: {consent_id}")
            return json.loads(consent.preferences)
        logger.error(f"Failed to retrieve cookie consent for consent_id: {consent_id}")
        return None
    except Exception as e:
        # Comprehensive error logging
        logger.error("Unexpected error in get_cookie_consent")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def revoke_cookie_consent(consent_id):
    """
    Revoke cookie consent preferences in the database
    
    Args:
        consent_id (str): Unique identifier for the consent
        
    Returns:
        bool: True if revoked successfully, False otherwise
    """
    try:
        logger.info(f"Revoking cookie consent for consent_id: {consent_id}")
        consent = GDPR.query.filter_by(consent_id=consent_id, revoked=None).first()
        if consent:
            consent.revoked = datetime.now()
            db.session.commit()
            logger.info(f"Cookie consent revoked successfully for consent_id: {consent_id}")
            return True
        logger.error(f"Failed to revoke cookie consent for consent_id: {consent_id}")
        return False
    except Exception as e:
        db.session.rollback()
        # Comprehensive error logging
        logger.error("Unexpected error in revoke_cookie_consent")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {str(e)}")
        logger.error(traceback.format_exc())
        return False
