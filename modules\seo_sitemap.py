from flask import request, make_response
import xml.etree.ElementTree as ET
from datetime import datetime
from modules.models import CMS, db

def init_sitemap_routes(app):
    """
    Initialize sitemap routes for the Flask application.
    
    Args:
        app (Flask): The Flask application instance
    """
    @app.route('/sitemap.xml')
    def generate_sitemap():
        """
        Generate a dynamic sitemap.xml for SEO purposes.
        
        Includes:
        - Static pages
        - CMS posts
        
        Returns XML sitemap with appropriate priorities and change frequencies.
        """
        # Base URL for the website
        base_url = request.host_url.rstrip('/')
        
        # Create sitemap root
        sitemap = ET.Element('urlset', xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
        
        # Static pages with custom priorities
        static_pages = [
            ('/', 1.0, 'daily'),
            ('/kostplan.html', 0.9, 'daily'),
            ('/cms-index.html?filter=oppskrift', 0.5, 'daily'),
            ('/cms-index.html?filter=blogg', 0.5, 'daily'),
            ('/omtaler.html', 0.5, 'daily'),
            ('/nyhetsbrev.html', 0.3, 'daily'),
            ('/om.html', 0.2, 'weekly'),
            ('/kontakt.html', 0.2, 'weekly'),
            ('/vilkar.html', 0.1, 'weekly'),
            ('/gdpr.html', 0.1, 'weekly'),        
            ('/handlekurv.html', 0.2, 'daily')
        ]

        # Add static pages to sitemap
        for path, priority, freq in static_pages:
            url_elem = ET.SubElement(sitemap, 'url')
            ET.SubElement(url_elem, 'loc').text = f"{base_url}{path}"
            ET.SubElement(url_elem, 'priority').text = str(priority)
            ET.SubElement(url_elem, 'changefreq').text = freq
            ET.SubElement(url_elem, 'lastmod').text = datetime.utcnow().strftime('%Y-%m-%d')
        
        # Add published CMS posts
        with app.app_context():
            published_posts = CMS.query.filter_by(draft=False).all()
            for post in published_posts:
                url_elem = ET.SubElement(sitemap, 'url')
                ET.SubElement(url_elem, 'loc').text = f"{base_url}/{post.url_slug}"
                ET.SubElement(url_elem, 'priority').text = '0.6'
                ET.SubElement(url_elem, 'changefreq').text = 'daily'
                ET.SubElement(url_elem, 'lastmod').text = (post.updated or datetime.utcnow()).strftime('%Y-%m-%d')
        
        # Convert sitemap to string and return with XML content type
        sitemap_str = ET.tostring(sitemap, encoding='unicode')
        return sitemap_str, 200, {'Content-Type': 'application/xml'}