#!/usr/bin/env python3
"""
Test webhook with proper Stripe signature
"""
import os
import sys
import json
import requests
import hmac
import hashlib
import time
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def generate_stripe_signature(payload, secret):
    """Generate a Stripe webhook signature"""
    timestamp = str(int(time.time()))
    signed_payload = f"{timestamp}.{payload}"
    signature = hmac.new(
        secret.encode('utf-8'),
        signed_payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return f"t={timestamp},v1={signature}"

def test_webhook_with_signature():
    """Test webhook with proper Stripe signature"""
    print("Testing webhook with Stripe signature...")
    
    # Get the endpoint secret from environment
    endpoint_secret = os.getenv('STRIPE_ENDPOINT_SECRET')
    if not endpoint_secret:
        print("❌ STRIPE_ENDPOINT_SECRET not found in environment")
        return False
    
    print(f"Using endpoint secret: {endpoint_secret[:10]}...")
    
    # For manual HMAC generation, we need the FULL secret including 'whsec_' prefix
    # This is different from what some documentation suggests
    full_secret = endpoint_secret
    
    # Test webhook payload
    test_payload = {
        "id": "evt_test_webhook",
        "object": "event",
        "api_version": "2020-08-27",
        "created": int(datetime.now().timestamp()),
        "data": {
            "object": {
                "id": "cs_test_session_123",
                "object": "checkout.session",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Test Customer"
                }
            }
        },
        "livemode": False,
        "pending_webhooks": 1,
        "request": {
            "id": None,
            "idempotency_key": None
        },
        "type": "checkout.session.completed"
    }
    
    # Convert to JSON string
    payload_json = json.dumps(test_payload, separators=(',', ':'))
    
    # Generate signature using full secret (including whsec_ prefix)
    signature = generate_stripe_signature(payload_json, full_secret)
    
    print(f"Payload size: {len(payload_json)} bytes")
    print(f"Signature: {signature[:50]}...")
    
    try:
        # Test local webhook endpoint
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=payload_json,  # Send as raw data, not JSON
            headers={
                'Content-Type': 'application/json',
                'stripe-signature': signature
            },
            timeout=10,
            verify=False  # Skip SSL verification for local testing
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            try:
                json_response = response.json()
                if json_response.get('success'):
                    print("✅ Webhook with signature test passed!")
                    return True
                else:
                    print("⚠️ Webhook responded but success=false")
                    return False
            except:
                print("⚠️ Webhook responded but not with JSON")
                return False
        else:
            print(f"❌ Webhook returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to local server. Is it running on https://127.0.0.1:8282?")
        return False
    except Exception as e:
        print(f"❌ Error testing webhook: {str(e)}")
        return False

def test_webhook_without_signature():
    """Test webhook without signature (should work when endpoint_secret is not set)"""
    print("\nTesting webhook without signature...")
    
    test_payload = {
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": "cs_test_no_sig",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Test Customer"
                }
            }
        }
    }
    
    try:
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10,
            verify=False  # Skip SSL verification for local testing
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Webhook without signature test passed!")
            return True
        else:
            print(f"❌ Webhook returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Webhook Signature Test")
    print("=" * 40)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    sig_test = test_webhook_with_signature()
    no_sig_test = test_webhook_without_signature()
    
    if sig_test or no_sig_test:
        print("\n✅ At least one webhook test passed!")
        if sig_test:
            print("✅ Signature verification is working")
        if no_sig_test:
            print("✅ Basic webhook functionality is working")
    else:
        print("\n❌ All webhook tests failed")
        print("Check if the server is running and the webhook route is registered")