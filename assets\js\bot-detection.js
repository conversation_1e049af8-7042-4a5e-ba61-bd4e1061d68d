/**
 * Bot Detection - Browser Capability Testing
 * Tests various browser capabilities that are commonly missing in bots
 */

(function() {
    // Only run in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        return;
    }

    // Avoid running multiple times
    if (window.botDetectionRun) {
        return;
    }
    window.botDetectionRun = true;

    /**
     * Test canvas fingerprinting support
     * @returns {boolean} True if canvas is supported and works as expected
     */
    function testCanvasFingerprinting() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                return false;
            }

            // Draw some text with specific styling
            canvas.width = 200;
            canvas.height = 50;
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#123456';
            ctx.fillText('Canvas Test 👍', 2, 2);

            // Add a different colored rectangle
            ctx.fillStyle = '#654321';
            ctx.fillRect(100, 25, 50, 10);

            // Get the canvas data as an image
            const dataURL = canvas.toDataURL();

            // Check if we got valid data
            return dataURL.length > 50 && dataURL.indexOf('data:image/png') === 0;
        } catch (e) {
            console.log('Canvas test failed:', e);
            return false;
        }
    }

    /**
     * Test WebGL support
     * @returns {boolean} True if WebGL is supported
     */
    function testWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            return !!(
                window.WebGLRenderingContext &&
                (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
            );
        } catch (e) {
            return false;
        }
    }

    /**
     * Test Audio Context support
     * @returns {boolean} True if Audio Context is supported
     */
    function testAudioContext() {
        try {
            return !!(window.AudioContext || window.webkitAudioContext);
        } catch (e) {
            return false;
        }
    }

    /**
     * Test CSS feature support
     * @returns {boolean} True if modern CSS features are supported
     */
    function testCSSSupport() {
        const div = document.createElement('div');

        // Test for flexbox support
        if ('flexBasis' in div.style || 'webkitFlexBasis' in div.style || 'mozFlexBasis' in div.style) {
            return true;
        }

        // Test for grid support
        if ('grid' in div.style || 'msGrid' in div.style) {
            return true;
        }

        // Test for transforms
        if ('transform' in div.style || 'webkitTransform' in div.style || 'mozTransform' in div.style) {
            return true;
        }

        return false;
    }

    /**
     * Test storage access (localStorage/sessionStorage)
     * @returns {boolean} True if storage is accessible
     */
    function testStorageAccess() {
        try {
            const testKey = '_bot_detection_test_';
            localStorage.setItem(testKey, 'test');
            const result = localStorage.getItem(testKey) === 'test';
            localStorage.removeItem(testKey);
            return result;
        } catch (e) {
            return false;
        }
    }

    /**
     * Track mouse movement
     * @returns {Promise} Resolves to true if mouse movement detected within timeout
     */
    function testMouseMovement(timeoutMs = 3000) {
        return new Promise(resolve => {
            let moved = false;

            function handleMouseMove() {
                moved = true;
                document.removeEventListener('mousemove', handleMouseMove);
                resolve(true);
            }

            document.addEventListener('mousemove', handleMouseMove);

            // Set timeout to resolve even if no movement
            setTimeout(() => {
                document.removeEventListener('mousemove', handleMouseMove);
                if (!moved) {
                    resolve(false);
                }
            }, timeoutMs);
        });
    }

    /**
     * Run all browser capability tests
     * @returns {Promise<Object>} Test results and score
     */
    async function testBrowserCapabilities() {
        const capabilities = {
            canvas: testCanvasFingerprinting(),
            webgl: testWebGLSupport(),
            audio: testAudioContext(),
            css: testCSSSupport(),
            storage: testStorageAccess(),
            mouseMovement: false // Will be updated after timeout
        };

        // Wait for mouse movement test
        capabilities.mouseMovement = await testMouseMovement();

        // Calculate score (percentage of passed tests)
        const passedTests = Object.values(capabilities).filter(Boolean).length;
        const totalTests = Object.keys(capabilities).length;
        const score = Math.round((passedTests / totalTests) * 100);

        return {
            score: score,
            details: capabilities
        };
    }

    /**
     * Send capability results to server
     * @param {Object} results Test results
     */
    function sendResultsToServer(results) {
        try {
            // Log what we're sending for debugging
            console.log('Sending browser capabilities:', results);

            // Ensure we have a valid object with the expected structure
            if (!results || typeof results !== 'object') {
                console.error('Invalid results object:', results);
                return;
            }

            // Ensure we have the expected properties
            if (typeof results.score !== 'number' || !results.details) {
                console.error('Results missing required properties:', results);
                return;
            }

            // Create a properly formatted payload
            const payload = {
                score: results.score,
                details: results.details
            };

            // Send the request with better error handling
            fetch('/api/browser-capabilities', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload),
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error(`Server responded with ${response.status}: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('Browser capability data sent successfully:', data);
            })
            .catch(error => {
                console.error('Error sending browser capability data:', error);
            });
        } catch (e) {
            console.error('Failed to send capability results:', e);
        }
    }

    // Run tests and send results
    window.addEventListener('load', () => {
        // First attempt with a short delay
        setTimeout(async () => {
            try {
                const results = await testBrowserCapabilities();
                sendResultsToServer(results);
                console.log('Browser capability tests completed with score:', results.score);

                // Store in sessionStorage to avoid running tests multiple times in the same session
                sessionStorage.setItem('bot_detection_run', 'true');
                sessionStorage.setItem('bot_detection_score', results.score.toString());
            } catch (e) {
                console.error('Error in initial browser capability test:', e);
            }
        }, 1000); // Slight delay to not block page load

        // Second attempt with a longer delay to ensure tests run properly
        // This helps with pages that have many resources or slow connections
        setTimeout(async () => {
            try {
                // Only run if first attempt didn't complete successfully
                if (!sessionStorage.getItem('bot_detection_run')) {
                    console.log('Running delayed browser capability tests');
                    const results = await testBrowserCapabilities();
                    sendResultsToServer(results);
                    console.log('Delayed browser capability tests completed with score:', results.score);
                    sessionStorage.setItem('bot_detection_run', 'true');
                    sessionStorage.setItem('bot_detection_score', results.score.toString());
                }
            } catch (e) {
                console.error('Error in delayed browser capability test:', e);
            }
        }, 3000); // Longer delay as backup
    });
})();
