#!/usr/bin/env python3
"""
Debug webhook to understand what's happening
"""
import os
import sys
import json
import requests
import hmac
import hashlib
import time
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_webhook():
    """Test webhook with minimal payload to see what happens"""
    print("Testing simple webhook...")
    
    # Very simple test payload
    test_payload = {
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": "cs_test_simple",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Test Customer"
                }
            }
        }
    }
    
    try:
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10,
            verify=False
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_webhook_with_debug():
    """Test webhook and check debug files"""
    print("\nTesting webhook with debug file check...")
    
    # Clear any existing debug files first
    debug_dir = "instance/webhook_debug"
    if os.path.exists(debug_dir):
        for file in os.listdir(debug_dir):
            if file.endswith('.json'):
                os.remove(os.path.join(debug_dir, file))
                print(f"Removed old debug file: {file}")
    
    # Test payload
    test_payload = {
        "id": "evt_test_debug",
        "object": "event",
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": "cs_test_debug",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Debug Customer"
                }
            }
        }
    }
    
    try:
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10,
            verify=False
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        # Check if debug file was created
        if os.path.exists(debug_dir):
            debug_files = [f for f in os.listdir(debug_dir) if f.endswith('.json')]
            if debug_files:
                print(f"Debug files created: {debug_files}")
                # Read the latest debug file
                latest_file = max(debug_files)
                with open(os.path.join(debug_dir, latest_file), 'r') as f:
                    debug_data = json.load(f)
                print(f"Debug data: {json.dumps(debug_data, indent=2)}")
            else:
                print("No debug files created - webhook might be failing early")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Webhook Debug Test")
    print("=" * 40)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test simple webhook first
    simple_test = test_simple_webhook()
    
    # Test with debug file check
    debug_test = test_webhook_with_debug()
    
    if simple_test or debug_test:
        print("\n✅ At least one test passed!")
    else:
        print("\n❌ All tests failed")