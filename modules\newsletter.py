import os
import secrets
import traceback
import logging
from flask import Blueprint, request, jsonify, render_template, current_app, url_for, flash, redirect
from flask_mail import Message
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from .models import Newsletter, get_db, db
from .mail import init_mail, send_newsletter_verification_email
from .security import (
    sanitize_input, 
    validate_email, 
    is_potential_spam
)
from .forms import NewsletterSubscribeForm, NewsletterUnsubscribeForm

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

newsletter_bp = Blueprint('newsletter', __name__, url_prefix='/')

def generate_verification_token():
    """
    Generate a secure random verification token
    """
    return secrets.token_urlsafe(32)

def send_verification_email(app, email: str, verification_token: str):
    """
    Wrapper function to send newsletter verification email
    
    Args:
        app (Flask): Flask application context
        email (str): Recipient email address
        verification_token (str): Unique verification token
    """
    from flask_mail import Mail
    mail = Mail(app)
    send_newsletter_verification_email(mail, app, email, verification_token)

@newsletter_bp.route('/nyhetsbrev.html', methods=['GET', 'POST'])
def nyhetsbrev():
    """
    Render the newsletter signup page and handle form submission
    """
    form = NewsletterSubscribeForm()
    
    if request.method == 'POST' and form.validate_on_submit():
        # Check honeypot field - if it's checked, it's likely a bot
        if form.checkbox.data:
            logger.warning("Bot detected - honeypot checkbox was checked")
            flash('Ugyldig skjemainnsending.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))
            
        # Check IP reputation
        client_ip = request.headers.get("X-Forwarded-For", request.remote_addr)
        is_spam, abuse_score, error = is_potential_spam(client_ip, spam_threshold=99)
        
        if is_spam:
            logger.warning(f"Spam detected from IP {client_ip} with score {abuse_score}")
            flash('Beklager, vi kan ikke behandle denne forespørselen akkurat nå.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))
            
        # Sanitize inputs
        name = sanitize_input(
            form.name.data,
            input_type=str,
            max_length=100,
            allow_html=False  # Explicitly deny HTML
        )
        email = validate_email(form.email.data)

        if not name or not email:
            flash('Vennligst fyll ut både navn og e-post korrekt.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))

        try:
            verification_token = generate_verification_token()
            
            # Create new subscriber with sanitized data
            subscriber = Newsletter(
                name=name,
                email=email,
                verification_token=verification_token,
                is_verified=False
            )
            
            db.session.add(subscriber)
            db.session.commit()
            
            # Send verification email
            send_verification_email(current_app._get_current_object(), email, verification_token)
            
            flash('Takk! Vennligst sjekk e-posten din for å bekrefte påmeldingen.', 'success')
            return redirect(url_for('newsletter.nyhetsbrev'))
            
        except IntegrityError:
            db.session.rollback()
            flash('Denne e-postadressen er allerede registrert.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error in newsletter signup: {str(e)}")
            logger.error(traceback.format_exc())
            flash('En feil oppstod. Vennligst prøv igjen senere.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))

    return render_template('nyhetsbrev.html', form=form)

@newsletter_bp.route('/subscribe', methods=['POST'])
def subscribe():
    """
    Handle newsletter subscription
    """
    try:
        form = NewsletterSubscribeForm(request.form)
        if not form.validate():
            return jsonify({
                'success': False,
                'message': 'Vennligst fyll ut både navn og e-post korrekt.'
            }), 400

        # Check honeypot field - if it's checked, it's likely a bot
        if form.checkbox.data:
            logger.warning("Bot detected - honeypot checkbox was checked")
            return jsonify({
                'success': False,
                'message': 'Ugyldig skjemainnsending.'
            }), 400

        # Check IP reputation
        client_ip = request.headers.get("X-Forwarded-For", request.remote_addr)
        is_spam, abuse_score, error = is_potential_spam(client_ip, spam_threshold=99)
        
        if is_spam:
            logger.warning(f"Spam detected from IP {client_ip} with score {abuse_score}")
            return jsonify({
                'success': False,
                'message': 'Beklager, vi kan ikke behandle denne forespørselen akkurat nå.'
            }), 400

        # Sanitize inputs with explicit settings
        name = sanitize_input(
            form.name.data,
            input_type=str,
            max_length=100,
            allow_html=False  # Explicitly deny HTML
        )
        email = validate_email(form.email.data)

        try:
            # Subscribe or reactivate
            subscriber, is_new = Newsletter.subscribe(db.session, name, email)
            
            if subscriber is None:
                return jsonify({'error': 'Email already subscribed'}), 400

            # Send verification email
            send_verification_email(current_app, email, subscriber.verification_token)

            return jsonify({
                'message': 'Verification email sent', 
                'is_new_subscription': is_new
            }), 200

        except SQLAlchemyError as e:
            # Handle SQLAlchemy-specific errors
            logger.error(f"Database error: {e}")
            logger.error(traceback.format_exc())
            return jsonify({'error': 'An unexpected error occurred'}), 500

    except Exception as e:
        logger.error(f'Subscription error: {str(e)}')
        logger.error(traceback.format_exc())
        return jsonify({'error': 'An unexpected error occurred'}), 500

@newsletter_bp.route('/verify/<token>', methods=['GET'])
def verify_email(token):
    """
    Verify email using verification token
    """
    try:
        try:
            # Verify email
            subscriber = Newsletter.verify_email(db.session, token)
            
            if not subscriber:
                return 'Ugyldig eller utløpt verifiseringslenke', 400

            return 'E-posten er bekreftet! Du er påmeldt vårt nyhetsbrev.', 200

        except SQLAlchemyError as e:
            # Handle SQLAlchemy-specific errors
            logger.error(f"Database error: {e}")
            logger.error(traceback.format_exc())
            return 'An unexpected error occurred', 500

    except Exception as e:
        logger.error(f'Verification error: {str(e)}')
        logger.error(traceback.format_exc())
        return 'An unexpected error occurred', 500

@newsletter_bp.route('/unsubscribe', methods=['POST'])
def unsubscribe():
    """
    Handle newsletter unsubscription
    """
    try:
        form = NewsletterUnsubscribeForm(request.form)
        if not form.validate():
            return jsonify({'error': 'Email is required'}), 400
        
        # Sanitize input
        email = validate_email(form.email.data)

        try:
            # Unsubscribe
            success = Newsletter.unsubscribe(db.session, email)
            
            if not success:
                return jsonify({'error': 'Email not found in our newsletter list'}), 404

            return jsonify({'message': 'You have been unsubscribed'}), 200

        except SQLAlchemyError as e:
            # Handle SQLAlchemy-specific errors
            logger.error(f"Database error: {e}")
            logger.error(traceback.format_exc())
            return jsonify({'error': 'An unexpected error occurred'}), 500

    except Exception as e:
        logger.error(f'Unsubscription error: {str(e)}')
        logger.error(traceback.format_exc())
        return jsonify({'error': 'An unexpected error occurred'}), 500

@newsletter_bp.route('/unsubscribe.html', methods=['GET'])
def unsubscribe_page():
    """
    Handle newsletter unsubscription via GET request with email parameter
    """
    try:
        email = request.args.get('email')
        if not email:
            flash('E-postadresse mangler.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))
        
        # Sanitize and validate email
        try:
            email = validate_email(email)
        except ValueError:
            flash('Ugyldig e-postadresse.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))

        # Unsubscribe
        success = Newsletter.unsubscribe(db.session, email)
        
        if not success:
            flash('E-postadressen ble ikke funnet i vår nyhetsbrevliste.', 'error')
            return redirect(url_for('newsletter.nyhetsbrev'))

        return render_template('unsubscribe.html')

    except Exception as e:
        logger.error(f"Error in unsubscribe_page: {e}")
        flash('Det oppstod en feil. Vennligst prøv igjen senere.', 'error')
        return redirect(url_for('newsletter.nyhetsbrev'))