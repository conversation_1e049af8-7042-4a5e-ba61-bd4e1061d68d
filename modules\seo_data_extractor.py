import re
from flask import request, g, current_app, has_request_context
from urllib.parse import urlparse
from .models import db, CMS

def get_current_endpoint():
    """
    Get the current Flask endpoint
    
    Returns:
        String with the current endpoint or None if not in a request context
    """
    if not has_request_context():
        return None
    
    return request.endpoint

def get_current_template():
    """
    Try to determine the template being rendered
    This is a best-effort function as Flask doesn't track this directly
    
    Returns:
        String with likely template name or None
    """
    endpoint = get_current_endpoint()
    
    if not endpoint:
        return None
    
    # Common endpoint to template mappings
    endpoint_templates = {
        'index': 'index.html',
        'blog_post': 'cms-post.html',
        'cms_index_page': 'cms-index.html',
        'omtaler': 'omtaler.html'
    }
    
    return endpoint_templates.get(endpoint)

def get_current_post():
    """
    Get the current CMS post from various sources
    
    Returns:
        CMS post object or None
    """
    # First check Flask g object
    if hasattr(g, 'post') and g.post:
        return g.post
    
    # Try to extract from template context if available
    # This is not directly accessible, so we use other methods
    
    # Try to extract post ID from URL
    url_path = request.path
    match = re.search(r'cms-post-(\d+)\.html', url_path)
    if match:
        post_id = match.group(1)
        post = CMS.query.get(post_id)
        if post:
            return post
    
    # Try to extract post from URL slug
    parsed_path = urlparse(url_path).path
    slug = parsed_path.strip('/').split('/')[-1]
    if slug:
        post = CMS.query.filter_by(url_slug=slug).first()
        if post:
            return post
    
    return None

def is_recipe_page():
    """
    Check if the current page is a recipe page using multiple detection methods
    
    Returns:
        Boolean indicating if it's a recipe page
    """
    # Method 1: Check if we're in a CMS post with recipe type
    post = get_current_post()
    if post and post.post_type == 'oppskrift':
        return True
    
    # Method 2: Check the endpoint
    endpoint = get_current_endpoint()
    if endpoint and ('recipe' in endpoint or 'oppskrift' in endpoint):
        return True
    
    # Method 3: Check URL pattern as fallback
    url_path = request.path
    recipe_patterns = [
        r'^/oppskrift/',
        r'^/oppskrifter/',
        r'^/cms-post-\d+\.html$'  # CMS post URL pattern
    ]
    
    for pattern in recipe_patterns:
        if re.match(pattern, url_path):
            # If it matches a recipe pattern, do an additional check
            # to see if there's a post and what type it is
            if post:
                # If we have a post but it's not a recipe, return False
                return post.post_type == 'oppskrift'
            # If no post found but URL matches, assume it's a recipe
            return True
    
    return False

def is_article_page():
    """
    Check if the current page is an article page using multiple detection methods
    
    Returns:
        Boolean indicating if it's an article page
    """
    # Method 1: Check if we're in a CMS post with article type
    post = get_current_post()
    if post:
        # If we have a post, it must be of type 'artikkel' to be an article page
        if post.post_type == 'artikkel':
            return True
        # If it's a different post type, it's not an article
        return False
    
    # Method 2: Check the endpoint
    endpoint = get_current_endpoint()
    if endpoint and ('article' in endpoint or 'artikkel' in endpoint or 'blog' in endpoint or 'blogg' in endpoint):
        # If it's a blog_post endpoint but we don't have a post object,
        # we can't determine the type, so assume it might be an article
        return True
    
    # Method 3: Check URL pattern as fallback
    url_path = request.path
    article_patterns = [
        r'^/artikkel/',
        r'^/artikler/',
        r'^/blogg/',
        r'^/cms-post-\d+\.html$'  # CMS post URL pattern
    ]
    
    for pattern in article_patterns:
        if re.match(pattern, url_path):
            # If it matches an article pattern and we don't have a post object,
            # assume it's an article
            return True
    
    return False

def is_product_page():
    """
    Check if the current page is a product page using multiple detection methods
    
    Returns:
        Boolean indicating if it's a product page
    """
    # Method 1: Check the endpoint
    endpoint = get_current_endpoint()
    if endpoint and ('product' in endpoint or 'produkt' in endpoint):
        return True
    
    # Method 2: Check URL pattern
    url_path = request.path
    product_patterns = [
        r'^/produkt/',
        r'^/produkter/'
    ]
    
    for pattern in product_patterns:
        if re.match(pattern, url_path):
            return True
    
    # Method 3: Check if it's a book page but not specifically marked as such
    if is_book_page() and not endpoint:
        return True
    
    return False

def is_book_page():
    """
    Check if the current page is a book page using multiple detection methods
    
    Returns:
        Boolean indicating if it's a book page
    """
    # Method 1: Check the endpoint
    endpoint = get_current_endpoint()
    if endpoint and ('book' in endpoint or 'bok' in endpoint):
        return True
    
    # Method 2: Check URL pattern
    url_path = request.path
    book_patterns = [
        r'^/bok/',
        r'^/boker/'
    ]
    
    for pattern in book_patterns:
        if re.match(pattern, url_path):
            return True
    
    return False

def is_review_page():
    """
    Check if the current page is a review page using multiple detection methods
    
    Returns:
        Boolean indicating if it's a review page
    """
    # Method 1: Check the endpoint
    endpoint = get_current_endpoint()
    if endpoint and ('review' in endpoint or 'omtale' in endpoint):
        return True
    
    # Method 2: Check URL pattern
    url_path = request.path
    if url_path == '/omtaler.html' or url_path.startswith('/omtaler/'):
        return True
    
    return False

def extract_cms_post_data():
    """
    Extract data from the current CMS post
    
    Returns:
        Dictionary with post data or None if not a CMS post
    """
    # Use the get_current_post helper function
    return get_current_post()

def extract_reviews_data():
    """
    Extract reviews data from the current page
    
    Returns:
        List of reviews or None if not available
    """
    # This is a placeholder. In a real implementation, you would
    # extract reviews from your database or other source.
    # For now, we'll return a sample review for demonstration.
    
    if is_review_page(request.path):
        return [
            {
                'author': 'Kunde',
                'rating': 5,
                'content': 'Veldig fornøyd med produktet!'
            }
        ]
    
    return None

def extract_book_data():
    """
    Extract book data from the current page
    
    Returns:
        Dictionary with book data or None if not a book page
    """
    # This is a placeholder. In a real implementation, you would
    # extract book data from your database or other source.
    # For now, we'll return sample data for demonstration.
    
    if is_book_page(request.path):
        return {
            'title': 'Ketolabben Kokebok',
            'author': 'Ketolabben',
            'description': 'En komplett guide til ketogen kosthold med deilige oppskrifter.',
            'isbn': '9788293780014',
            'price': '349.00',
            'featured_image': '/assets/img/book-cover.jpg',
            'reviews': [
                {
                    'author': 'Leser',
                    'rating': 5,
                    'content': 'Fantastisk kokebok med enkle og smakfulle oppskrifter!'
                }
            ]
        }
    
    return None

def get_page_type():
    """
    Determine the type of the current page using improved detection methods
    
    Returns:
        String indicating the page type ('recipe', 'article', 'product', 'book', 'review', 'home', 'other')
    """
    # Check if we're on the home page
    url_path = request.path
    if url_path == '/' or url_path == '/index.html':
        return 'home'
    
    # Check for specific page types using our improved detection methods
    if is_recipe_page():
        return 'recipe'
    elif is_article_page():
        return 'article'
    elif is_book_page():
        return 'book'
    elif is_product_page():
        return 'product'
    elif is_review_page():
        return 'review'
    
    # If we have a CMS post but couldn't determine the type from above methods,
    # try to determine from the post_type attribute
    post = get_current_post()
    if post:
        post_type = getattr(post, 'post_type', '').lower()
        if post_type == 'oppskrift':
            return 'recipe'
        elif post_type == 'artikkel':
            return 'article'
        elif post_type in ['bok', 'book']:
            return 'book'
        elif post_type in ['produkt', 'product']:
            return 'product'
        elif post_type in ['omtale', 'review']:
            return 'review'
    
    # Check the endpoint as a fallback
    endpoint = get_current_endpoint()
    if endpoint:
        if 'index' in endpoint or endpoint == 'index':
            return 'home'
        elif any(term in endpoint for term in ['recipe', 'oppskrift']):
            return 'recipe'
        elif any(term in endpoint for term in ['article', 'artikkel', 'blog', 'blogg']):
            return 'article'
        elif any(term in endpoint for term in ['book', 'bok']):
            return 'book'
        elif any(term in endpoint for term in ['product', 'produkt']):
            return 'product'
        elif any(term in endpoint for term in ['review', 'omtale']):
            return 'review'
    
    # Default to 'other' if we couldn't determine the page type
    return 'other'