import {
    ClassicEditor,
    Alignment,
    AutoImage,
    AutoLink,
    Autosave,
    Bold,
    Essentials,
    FindAndReplace,
    GeneralHtmlSupport,
    Heading,
    HtmlComment,
    HtmlEmbed,
    ImageBlock,
    ImageCaption,
    ImageInline,
    ImageInsert,
    ImageInsertViaUrl,
    ImageResize,
    ImageStyle,
    ImageTextAlternative,
    ImageToolbar,
    ImageUpload,
    Indent,
    IndentBlock,
    Italic,
    Link,
    LinkImage,
    List,
    ListProperties,
    MediaEmbed,
    Paragraph,
    PasteFromOffice,
    RemoveFormat,
    SimpleUploadAdapter,
    SourceEditing,
    SpecialCharacters,
    SpecialCharactersArrows,
    SpecialCharactersCurrency,
    SpecialCharactersEssentials,
    SpecialCharactersLatin,
    SpecialCharactersMathematical,
    SpecialCharactersText,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TodoList,
    Underline
} from 'ckeditor5';

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Find all textareas with the ckeditor-enable class
    const editors = document.querySelectorAll('textarea.ckeditor-enable');
    
    // Configuration for CKEditor
    const editorConfig = {
        plugins: [
            Essentials,
            Paragraph,
            Heading,
            Bold,
            Italic,
            Underline,
            Link,
            List,
            Alignment,
            Table,
            TableToolbar,
            TableProperties,
            TableCellProperties,
            MediaEmbed,
            ImageBlock,
            ImageCaption,
            ImageStyle,
            ImageToolbar,
            ImageUpload,
            ImageResize,
            Indent,
            FindAndReplace,
            SourceEditing,
            RemoveFormat,
            SpecialCharacters,
            SpecialCharactersEssentials,
            AutoImage,
            AutoLink,
            Autosave,
            GeneralHtmlSupport,
            HtmlComment,
            HtmlEmbed,
            ImageInline,
            ImageInsert,
            ImageInsertViaUrl,
            ImageTextAlternative,
            IndentBlock,
            LinkImage,
            ListProperties,
            PasteFromOffice,
            SimpleUploadAdapter,
            SpecialCharactersArrows,
            SpecialCharactersCurrency,
            SpecialCharactersLatin,
            SpecialCharactersMathematical,
            SpecialCharactersText,
            TableCaption,
            TableColumnResize,
            TodoList
        ],
        toolbar: {
            items: [
                'sourceEditing',
                '|',
                'heading',
                '|',
                'bold',
                'italic',
                'underline',
                '|',
                'link',
                'insertImage',
                'insertTable',
                '|',
                'alignment',
                '|',
                'bulletedList',
                'numberedList',
                'todoList',
                'outdent',
                'indent'
            ],
            shouldNotGroupWhenFull: true
        },
        image: {
            toolbar: [
                'imageTextAlternative',
                'toggleImageCaption',
                'imageStyle:inline',
                'imageStyle:block',
                'imageStyle:side',
                'linkImage'
            ]
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells',
                'tableProperties',
                'tableCellProperties',
                'toggleTableCaption'
            ]
        }
    };

    // Initialize CKEditor for each textarea
    editors.forEach(function(textarea) {
        ClassicEditor
            .create(textarea, editorConfig)
            .then(editor => {
                console.log('CKEditor initialized successfully:', editor);
            })
            .catch(error => {
                console.error('CKEditor initialization failed:', error);
                console.error('Error details:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });
            });
    });
});