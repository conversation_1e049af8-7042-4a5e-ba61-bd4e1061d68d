import os
from flask import request, g
from modules import create_app
from modules.imgresize import process_images
from modules.logger import log_performance, setup_logger
from modules.structured_data import (
    generate_recipe_schema, generate_article_schema, 
    generate_product_schema, generate_book_schema, 
    generate_review_schema, generate_organization_schema,
    generate_website_schema, inject_structured_data
)
from modules.seo_data_extractor import (
    get_page_type, extract_cms_post_data, 
    extract_reviews_data, extract_book_data
)

# Create and run the application
app = create_app()

# Set up logging
logger = setup_logger(app)

# Set production mode
app.config['ENV'] = os.getenv('FLASK_ENV', 'production')
app.config['DEBUG'] = os.getenv('FLASK_DEBUG', False)

# Register the process_images filter
app.jinja_env.filters['process_images'] = process_images

@log_performance(logger)
def process_html_response(content):
    return process_images(content)

@log_performance(logger)
def add_structured_data(content):
    """
    Add structured data to HTML content based on page type
    
    Args:
        content: HTML content
        
    Returns:
        HTML content with structured data
    """
    # Initialize structured data list
    structured_data = []
    
    # Add organization schema to all pages
    structured_data.append(generate_organization_schema())
    
    # Add website schema to all pages
    structured_data.append(generate_website_schema())
    
    try:
        # Determine page type using improved detection
        page_type = get_page_type()
        logger.info(f"Detected page type: {page_type}")
        
        # Add page-specific structured data
        if page_type == 'recipe':
            post_data = extract_cms_post_data()
            if post_data:
                logger.info(f"Adding recipe schema for: {post_data.title if hasattr(post_data, 'title') else 'Unknown'}")
                structured_data.append(generate_recipe_schema(post_data))
        
        elif page_type == 'article':
            post_data = extract_cms_post_data()
            if post_data:
                logger.info(f"Adding article schema for: {post_data.title if hasattr(post_data, 'title') else 'Unknown'}")
                structured_data.append(generate_article_schema(post_data))
        
        elif page_type == 'product':
            # For products, you would extract product data from your database
            post_data = extract_cms_post_data()
            if post_data:
                logger.info(f"Adding product schema for: {post_data.title if hasattr(post_data, 'title') else 'Unknown'}")
                structured_data.append(generate_product_schema(post_data))
        
        elif page_type == 'book':
            book_data = extract_book_data()
            if book_data:
                logger.info(f"Adding book schema for: {book_data.get('title', 'Unknown')}")
                structured_data.append(generate_book_schema(book_data))
        
        elif page_type == 'review':
            reviews = extract_reviews_data()
            if reviews:
                logger.info(f"Adding review schema with {len(reviews)} reviews")
                structured_data.append(generate_review_schema(reviews, "Ketolabben"))
    
    except Exception as e:
        logger.error(f"Error generating structured data: {str(e)}")
    
    # Inject structured data into HTML
    return inject_structured_data(content, structured_data)

# Add middleware to process all HTML responses
@app.after_request
def process_response(response):
    try:
        if response.content_type and response.content_type.startswith('text/html'):
            # Get the HTML content
            content = response.get_data(as_text=True)
            
            # Skip processing if content is empty
            if not content:
                return response
            
            # Check if content is valid HTML
            if '<html' not in content.lower() and '<!doctype html' not in content.lower():
                logger.warning("Response doesn't appear to be HTML. Skipping structured data injection.")
                return response
            
            # Check if content is already escaped
            if '&lt;html' in content.lower() or '&lt;!doctype html' in content.lower():
                logger.warning("HTML content appears to be escaped. Structured data may not be properly injected.")
                # We'll still try to process it, but it might not work correctly
            
            # Process images first
            processed = process_html_response(content)
            
            # Then add structured data
            processed = add_structured_data(processed)
            
            # Check if structured data was injected
            if '<script type="application/ld+json">' not in processed:
                logger.warning("Structured data was not injected into the HTML. Check the HTML structure.")
            
            # Set the processed content back to the response
            response.set_data(processed)
            
            # Ensure the content type is set correctly
            response.content_type = 'text/html; charset=utf-8'
            
            # Log success
            logger.debug("Successfully processed HTML response")
    except Exception as e:
        # Log any errors but don't break the response
        logger.error(f"Error processing response: {str(e)}")
    
    return response

if __name__ == '__main__':
    logger.info("Starting application server")
    try:
        app.run(host='0.0.0.0', port=8282, ssl_context='adhoc')
    except Exception as e:
        logger.error(f"Server failed to start: {str(e)}")
    finally:
        # monitor.stop_monitoring()
        pass