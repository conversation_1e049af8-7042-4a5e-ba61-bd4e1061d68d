from flask import Blueprint, current_app
from wtforms import TextAreaField
from wtforms.widgets import TextArea

class CKEditorField(TextAreaField):
    """
    Custom field class for CKEditor integration
    """
    def __init__(self, label=None, validators=None, **kwargs):
        kwargs.setdefault('render_kw', {})
        # Ensure the ckeditor-enable class is added
        classes = kwargs['render_kw'].get('class', '').split()
        if 'ckeditor-enable' not in classes:
            classes.append('ckeditor-enable')
        kwargs['render_kw']['class'] = ' '.join(classes)
        kwargs['render_kw'].setdefault('rows', 10)
        super(CKEditorField, self).__init__(label, validators, **kwargs)

# Create Blueprint
ckeditor = Blueprint('ckeditor', __name__)

@ckeditor.before_app_request
def register_ckeditor():
    """
    Register CKEditor assets and configuration
    """
    # You can add any initialization code here if needed
    pass