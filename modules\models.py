from flask_sqlalchemy import SQLAlchemy
from flask import request, jsonify, current_app
import os
from datetime import datetime
import secrets
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from flask_login import UserMixin
from flask_caching import Cache

# Initialize SQLAlchemy and Cache
db = SQLAlchemy()
cache = Cache(config={
    'CACHE_TYPE': 'SimpleCache',
    'CACHE_DEFAULT_TIMEOUT': 300
})

# Initialize SQLAlchemy
# db = SQLAlchemy()

class Newsletter(db.Model):
    """
    Model for newsletter subscribers
    """
    __tablename__ = 'newsletter'

    # Add an explicit index for the email column
    __table_args__ = (db.Index('idx_newsletter_email', 'email', unique=True),)

    id = db.Column(db.Integer, primary_key=True, unique=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    verification_token = db.Column(db.String(255), nullable=True)
    is_verified = db.Column(db.Bo<PERSON>, default=False)
    unsubscribed = db.Column(db.Boolean, default=False)  # New column to track unsubscribed status
    created_at = db.Column(db.DateTime, default=datetime.now)

    def __repr__(self):
        return f'<Subscriber {self.email}>'

    @classmethod
    def generate_verification_token(cls):
        """
        Generate a secure random verification token

        Returns:
            str: Unique verification token
        """
        return secrets.token_urlsafe(32)

    @classmethod
    def subscribe(cls, db_session, name, email):
        """
        Subscribe a new user or reactivate an existing subscription

        Args:
            db_session (Session): Database session
            name (str): Subscriber's name
            email (str): Subscriber's email

        Returns:
            tuple: (Newsletter instance, bool) indicating if it's a new subscription
        """
        try:
            # Check if email already exists
            existing_subscriber = db_session.query(cls).filter_by(email=email).first()

            if existing_subscriber:
                if existing_subscriber.unsubscribed:
                    # Reactivate subscription
                    existing_subscriber.unsubscribed = False
                    existing_subscriber.is_verified = False
                    existing_subscriber.verification_token = cls.generate_verification_token()
                    db_session.commit()
                    return existing_subscriber, False

                # Email already subscribed
                return None, False

            # Create new subscriber
            verification_token = cls.generate_verification_token()
            new_subscriber = cls(
                name=name,
                email=email,
                verification_token=verification_token,
                is_verified=False,
                unsubscribed=False
            )

            db_session.add(new_subscriber)
            db_session.commit()
            return new_subscriber, True

        except IntegrityError:
            db_session.rollback()
            return None, False
        except SQLAlchemyError:
            db_session.rollback()
            raise

    @classmethod
    def verify_email(cls, db_session, token):
        """
        Verify subscriber's email using verification token

        Args:
            db_session (Session): Database session
            token (str): Verification token

        Returns:
            Newsletter or None: Verified subscriber or None
        """
        try:
            # Find subscriber with matching token
            subscriber = db_session.query(cls).filter_by(verification_token=token).first()

            if not subscriber:
                return None

            # Mark as verified
            subscriber.is_verified = True
            subscriber.verification_token = None
            db_session.commit()

            return subscriber

        except SQLAlchemyError:
            db_session.rollback()
            raise

    @classmethod
    def unsubscribe(cls, db_session, email):
        """
        Unsubscribe a user from the newsletter

        Args:
            db_session (Session): Database session
            email (str): Subscriber's email

        Returns:
            bool: True if unsubscribed successfully, False otherwise
        """
        try:
            # Find subscriber
            subscriber = db_session.query(cls).filter_by(email=email).first()

            if not subscriber:
                return False

            # Mark as unsubscribed
            subscriber.unsubscribed = True
            subscriber.is_verified = False
            db_session.commit()

            return True

        except SQLAlchemyError:
            db_session.rollback()
            raise

class Auth(UserMixin, db.Model):
    """
    Model for user authentication
    """
    __tablename__ = 'auth'
    __table_args__ = (
        db.Index('idx_auth_username', 'username', unique=True),
    )

    id = db.Column(db.Integer, primary_key=True, unique=True, autoincrement=True)
    alias = db.Column(db.String(50), nullable=False)
    username = db.Column(db.String(120), nullable=False, unique=True)
    password_hash = db.Column(db.String(255), nullable=False)
    salt = db.Column(db.String(32), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)

    def __repr__(self):
        return f'<User {self.username}>'

    @staticmethod
    def verify_password(db_session, username, password):
        """
        Verify a user's password
        
        Args:
            db_session: Database session
            username (str): Username to verify
            password (str): Password to verify
            
        Returns:
            bool: True if password is correct, False otherwise
        """
        import hashlib
        
        try:
            # Find user
            user = db_session.query(Auth).filter_by(username=username).first()
            if not user:
                return False
                
            # Hash the password with the stored salt
            salted_password = password + user.salt
            password_hash = hashlib.sha256(salted_password.encode()).hexdigest()
            
            # Compare hashes
            return password_hash == user.password_hash
            
        except Exception as e:
            print(f"Error verifying password: {e}")
            return False

    @classmethod
    def create_user(cls, db_session, username, password):
        """
        Create a new user with secure password hashing

        Args:
            db_session (Session): Database session
            username (str): User's email/username
            password (str): User's plain-text password

        Returns:
            Auth: Created user instance
        """
        import secrets
        import hashlib

        # First, delete any existing user with this username
        existing_user = db_session.query(cls).filter_by(username=username).first()
        if existing_user:
            db_session.delete(existing_user)

        # Generate a secure random salt
        salt = secrets.token_hex(16)  # 32-character hex string

        # Hash the password with the salt using SHA-256
        salted_password = password + salt
        password_hash = hashlib.sha256(salted_password.encode()).hexdigest()

        # Create new user
        new_user = cls(
            alias="",
            username=username,
            password_hash=password_hash,
            salt=salt
        )

        try:
            db_session.add(new_user)
            db_session.commit()
            return new_user
        except Exception as e:
            db_session.rollback()
            print(f"Error creating user: {e}")
            return None

class CMS(db.Model):
    """
    Model for CMS posts
    """
    __tablename__ = 'cms'
    
    __table_args__ = (
        db.Index('idx_cms_post_type', 'post_type'),
        db.Index('idx_cms_created', 'created'),
        db.Index('idx_cms_author', 'author'),
        db.Index('idx_cms_category', 'category'),
        db.Index('idx_cms_url_slug', 'url_slug', unique=True),
    )

    id = db.Column(db.Integer, primary_key=True, unique=True, autoincrement=True)
    post_type = db.Column(db.String(50), nullable=False)
    tags = db.Column(db.Text, nullable=True)
    draft = db.Column(db.Boolean, default=False)
    created = db.Column(db.DateTime, default=datetime.now)
    updated = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    author = db.Column(db.String(100), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    main_content = db.Column(db.Text, nullable=True)
    ingredients = db.Column(db.Text, nullable=True)
    prep_time = db.Column(db.Integer, nullable=True)
    cook_time = db.Column(db.Integer, nullable=True)
    servings = db.Column(db.Integer, nullable=True)
    difficulty = db.Column(db.String(50), nullable=True)
    category = db.Column(db.String(50), nullable=True)  # Recipe category
    instructions = db.Column(db.Text, nullable=True)    # Recipe instructions
    tips = db.Column(db.Text, nullable=True)           # Recipe tips
    dish = db.Column(db.String(255), nullable=True)  # New field for dish name
    featured_image = db.Column(db.String(500), nullable=True)  # New field for featured image URL
    url_slug = db.Column(db.String(255), nullable=True)  # New field for URL slug

    def __repr__(self):
        return f'<CMS {self.title}>'

class CustomerData(db.Model):
    """
    Model for customer data and downloads
    """
    __tablename__ = 'customer_data'
    
    __table_args__ = (
        db.Index('idx_customer_email', 'customer_email'),
        db.Index('idx_customer_timestamp', 'timestamp'),
        db.Index('idx_customer_payment_status', 'payment_status'),
        db.Index('idx_customer_download_code', 'download_code', unique=True),
    )

    id = db.Column(db.Integer, primary_key=True, unique=True, autoincrement=True)
    timestamp = db.Column(db.DateTime, default=datetime.now)
    customer_name = db.Column(db.String(100), nullable=False)
    customer_email = db.Column(db.String(120), nullable=False)
    download_code = db.Column(db.String(255), nullable=False, unique=True)
    payment_status = db.Column(db.String(50), nullable=False)
    download_count = db.Column(db.Integer, default=0)
    email_sent = db.Column(db.Boolean, default=False)
    flask_session_id = db.Column(db.String(255), nullable=True)  # Store Flask session ID for secure downloads

    def __repr__(self):
        return f'<CustomerData {self.customer_email}>'

class Analytics(db.Model):
    """
    Model for tracking website analytics and page visits
    """
    __tablename__ = 'analytics'
    
    __table_args__ = (
        db.Index('idx_analytics_timestamp', 'timestamp'),
        db.Index('idx_analytics_url', 'url'),
        db.Index('idx_analytics_ip_address', 'ip_address'),
        db.Index('idx_analytics_is_bot', 'is_bot'),
        db.Index('idx_analytics_sessionid', 'sessionid'),
    )

    id = db.Column(db.Integer, primary_key=True, unique=True, autoincrement=True)
    timestamp = db.Column(db.DateTime, default=datetime.now)
    sessionid = db.Column(db.String(255), nullable=False)
    url = db.Column(db.String(2048), nullable=False)
    referrer = db.Column(db.Text, nullable=True)
    
    # New columns for enhanced tracking
    ip_address = db.Column(db.String(45), nullable=True)
    country = db.Column(db.String(100), nullable=True)
    device_type = db.Column(db.String(50), nullable=True)
    browser = db.Column(db.String(100), nullable=True)
    status_code = db.Column(db.Integer, nullable=True)
    processing_time = db.Column(db.Float, nullable=True)

    # Bot detection fields
    is_bot = db.Column(db.Boolean, nullable=True, default=False)
    bot_detection_method = db.Column(db.String(100), nullable=True)
    bot_user_agent = db.Column(db.Text, nullable=True)

    browser_capability = db.Column(db.Integer, nullable=True)

    def __repr__(self):
        return f'<Analytics {self.url} at {self.timestamp}>'

    @classmethod
    def log_visit(cls, sessionid, url, referrer=None, ip_address=None, 
                  country=None, device_type=None, browser=None, 
                  status_code=None, processing_time=None,
                  is_bot=False, bot_detection_method=None, bot_user_agent=None,
                  browser_capability=None):
        """
        Log a page visit to the analytics table with bot detection

        Args:
            sessionid (str): Session ID of the visitor
            url (str): URL being visited
            referrer (str, optional): Referring URL
            ip_address (str, optional): IP address of the visitor
            country (str, optional): Country of the visitor
            device_type (str, optional): Type of device (Desktop, Mobile, Tablet)
            browser (str, optional): Browser name and version
            status_code (int, optional): HTTP status code of the response
            processing_time (float, optional): Time taken to process the request
            is_bot (bool, optional): Whether the visitor is a bot
            bot_detection_method (str, optional): Method used to detect bot
            bot_user_agent (str, optional): Full user agent string for bot
            browser_capability (int, optional): Browser capability score (0-100)

        Returns:
            Analytics: Created analytics entry or None
        """
        try:
            # Ensure browser_capability is explicitly set to None or int value
            browser_capability_value = None
            if browser_capability is not None:
                try:
                    browser_capability_value = int(browser_capability)
                except (TypeError, ValueError):
                    current_app.logger.warning(
                        f"Invalid browser_capability value: {browser_capability}, setting to None"
                    )

            # Log debug information
            current_app.logger.debug(
                f"Logging visit: sessionid={sessionid}, url={url}, "
                f"browser_capability={browser_capability_value}"
            )

            # Create a new analytics entry
            new_visit = cls(
                sessionid=sessionid,
                url=url,
                referrer=referrer,
                ip_address=ip_address,
                country=country,
                device_type=device_type,
                browser=browser,
                status_code=status_code,
                processing_time=processing_time,
                is_bot=is_bot,
                bot_detection_method=bot_detection_method,
                bot_user_agent=bot_user_agent,
                browser_capability=browser_capability_value
            )
            db.session.add(new_visit)
            db.session.commit()

            # Log successful storage
            current_app.logger.info(f"Successfully logged visit: {new_visit}")
            return new_visit
        except SQLAlchemyError as e:
            db.session.rollback()
            current_app.logger.error(f"Database error while logging visit: {e}")
            return None
        except Exception as e:
            current_app.logger.error(f"Unexpected error in log_visit: {e}")
            return None

    @classmethod
    def bulk_insert_analytics(cls, entries):
        """
        Efficiently insert multiple analytics entries
        """
        db_session = get_db()
        try:
            db_session.bulk_insert_mappings(Analytics, entries)
            db_session.commit()
            return True
        except SQLAlchemyError as e:
            db_session.rollback()
            print(f"Error in bulk insert: {str(e)}")
            return False

class GDPR(db.Model):
    """
    Model for tracking user GDPR consent and preferences
    """
    __tablename__ = 'gdpr'
    __table_args__ = (
        db.Index('idx_gdpr_consent_id', 'consent_id'),
        db.Index('idx_gdpr_ip_hash', 'ip_hash'),
        db.Index('idx_gdpr_timestamp', 'timestamp'),
        db.Index('idx_gdpr_revoked', 'revoked'),
    )

    id = db.Column(db.Integer, primary_key=True, unique=True, autoincrement=True)
    consent_id = db.Column(db.String(255), nullable=False)
    ip_hash = db.Column(db.String(64), nullable=False)
    preferences = db.Column(db.Text, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.now)
    revoked = db.Column(db.DateTime, nullable=True)

    def __repr__(self):
        return f'<GDPR Consent {self.consent_id}>'

class BotIP(db.Model):
    """
    Model for tracking bot IP addresses
    """
    __tablename__ = 'bot_ips'
    __table_args__ = (
        db.Index('idx_bot_ip_address', 'ip_address', unique=True),
        db.Index('idx_bot_ip_detection_method', 'detection_method'),
        db.Index('idx_bot_ip_timestamp', 'first_detected')
    )

    id = db.Column(db.Integer, primary_key=True, unique=True, autoincrement=True)
    ip_address = db.Column(db.String(45), nullable=False, unique=True)
    detection_method = db.Column(db.String(255), nullable=True)
    first_detected = db.Column(db.DateTime, default=datetime.now)
    last_detected = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    detection_count = db.Column(db.Integer, default=1)
    url_patterns = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)

    def __repr__(self):
        return f'<BotIP {self.ip_address} (Method: {self.detection_method})>'

    @classmethod
    def add_bot_ip(cls, db_session, ip_address, detection_method=None, url_patterns=None):
        """
        Add or update a bot IP address in the database
        
        Args:
            db_session (Session): Database session
            ip_address (str): IP address to add
            detection_method (str, optional): Method used to detect bot
            url_patterns (str or list, optional): URL patterns that triggered bot detection
        
        Returns:
            BotIP: Created or updated bot IP entry
        """
        try:
            # Convert url_patterns to a comma-separated string if it's a list
            if isinstance(url_patterns, list):
                url_patterns = ', '.join(url_patterns)

            # Check if IP already exists
            existing_bot_ip = db_session.query(cls).filter_by(ip_address=ip_address).first()

            if existing_bot_ip:
                # Update existing entry
                existing_bot_ip.detection_count += 1
                existing_bot_ip.last_detected = datetime.now()
                
                # Update detection method if not already present
                if detection_method and detection_method not in (existing_bot_ip.detection_method or ''):
                    existing_bot_ip.detection_method = (
                        f"{existing_bot_ip.detection_method}, {detection_method}" 
                        if existing_bot_ip.detection_method 
                        else detection_method
                    )
                
                # Update URL patterns
                if url_patterns:
                    existing_patterns = set(existing_bot_ip.url_patterns.split(', ') if existing_bot_ip.url_patterns else [])
                    new_patterns = set(url_patterns.split(', '))
                    combined_patterns = list(existing_patterns.union(new_patterns))
                    existing_bot_ip.url_patterns = ', '.join(combined_patterns)
                
                db_session.commit()
                return existing_bot_ip

            # Create new bot IP entry
            new_bot_ip = cls(
                ip_address=ip_address,
                detection_method=detection_method,
                url_patterns=url_patterns
            )
            
            db_session.add(new_bot_ip)
            db_session.commit()
            return new_bot_ip

        except SQLAlchemyError:
            db_session.rollback()
            raise

    @classmethod
    def get_bot_ips(cls, db_session, is_active=True):
        """
        Retrieve bot IP addresses
        
        Args:
            db_session (Session): Database session
            is_active (bool, optional): Filter for active/inactive bot IPs
        
        Returns:
            list: List of bot IP addresses
        """
        try:
            return [
                bot_ip.ip_address 
                for bot_ip in db_session.query(cls).filter_by(is_active=is_active).all()
            ]

        except SQLAlchemyError:
            db_session.rollback()
            raise

    @classmethod
    def deactivate_bot_ip(cls, db_session, ip_address):
        """
        Deactivate a bot IP address
        
        Args:
            db_session (Session): Database session
            ip_address (str): IP address to deactivate
        
        Returns:
            bool: True if deactivated, False if not found
        """
        try:
            bot_ip = db_session.query(cls).filter_by(ip_address=ip_address).first()
            
            if not bot_ip:
                return False
            
            bot_ip.is_active = False
            db_session.commit()
            return True

        except SQLAlchemyError:
            db_session.rollback()
            raise

def init_db():
    """
    Initialize the database by creating all tables
    """
    db.create_all()
    
    # Create indexes manually
    try:
        with db.engine.connect() as conn:
            # CMS indexes
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_cms_post_type ON cms (post_type)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_cms_created ON cms (created)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_cms_author ON cms (author)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_cms_category ON cms (category)'))
            conn.execute(db.text('CREATE UNIQUE INDEX IF NOT EXISTS idx_cms_url_slug ON cms (url_slug)'))
            
            # CustomerData indexes
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_customer_email ON customer_data (customer_email)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_customer_timestamp ON customer_data (timestamp)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_customer_payment_status ON customer_data (payment_status)'))
            conn.execute(db.text('CREATE UNIQUE INDEX IF NOT EXISTS idx_customer_download_code ON customer_data (download_code)'))
            
            # Analytics indexes
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON analytics (timestamp)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_analytics_url ON analytics (url)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_analytics_ip_address ON analytics (ip_address)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_analytics_is_bot ON analytics (is_bot)'))
            conn.execute(db.text('CREATE INDEX IF NOT EXISTS idx_analytics_sessionid ON analytics (sessionid)'))
            
            conn.commit()
    except Exception as e:
        print(f"Error creating indexes: {str(e)}")

def get_db():
    """
    Provides a database session

    Returns:
        Session: SQLAlchemy session object
    """
    return db.session

@cache.cached(timeout=300, key_prefix='get_cms_posts')
def get_cms_posts():
    """
    Get all CMS posts
    """
    db_session = get_db()
    posts = db_session.query(CMS).filter_by(draft=False).order_by(CMS.created.desc()).all()
    return jsonify([post.__dict__ for post in posts])

@cache.cached(timeout=300, key_prefix='get_cms_post')
def get_cms_post(post_id):
    """
    Get a specific CMS post
    """
    db_session = get_db()
    post = db_session.query(CMS).filter_by(id=post_id).first()
    if post:
        return jsonify(post.__dict__)
    else:
        return jsonify({'error': 'Post not found'}), 404

@cache.memoize(300)
def get_analytics_by_date(start_date, end_date):
    """
    Get analytics within a date range
    """
    db_session = get_db()
    return db_session.query(Analytics).filter(
        Analytics.timestamp.between(start_date, end_date)
    ).all()

def create_cms_post():
    data = request.json
    db_session = get_db()
    new_post = CMS(
        post_type=data['post_type'],
        tags=data['tags'],
        draft=data['draft'],
        author=data['author'],
        title=data['title'],
        description=data['description'],
        main_content=data['main_content'],
        ingredients=data['ingredients'],
        prep_time=data['prep_time'],
        cook_time=data['cook_time'],
        servings=data['servings'],
        difficulty=data['difficulty'],
        category=data.get('category'),
        instructions=data.get('instructions'),
        tips=data.get('tips'),
        dish=data.get('dish'),
        featured_image=data.get('featured_image'),
        url_slug=data.get('url_slug')
    )
    db_session.add(new_post)
    db_session.commit()
    return jsonify(new_post.__dict__), 201

def update_cms_post(post_id):
    data = request.json
    db_session = get_db()
    post = db_session.query(CMS).filter_by(id=post_id).first()
    if post:
        post.post_type = data['post_type']
        post.tags = data['tags']
        post.draft = data['draft']
        post.author = data['author']
        post.title = data['title']
        post.description = data['description']
        post.main_content = data['main_content']
        post.ingredients = data['ingredients']
        post.prep_time = data['prep_time']
        post.cook_time = data['cook_time']
        post.servings = data['servings']
        post.difficulty = data['difficulty']
        post.category = data.get('category')
        post.instructions = data.get('instructions')
        post.tips = data.get('tips')
        post.dish = data.get('dish')
        post.featured_image = data.get('featured_image')
        post.url_slug = data.get('url_slug')
        db_session.commit()
        return jsonify(post.__dict__)
    else:
        return jsonify({'error': 'Post not found'}), 404

def delete_cms_post(post_id):
    db_session = get_db()
    post = db_session.query(CMS).filter_by(id=post_id).first()
    if post:
        db_session.delete(post)
        db_session.commit()
        return jsonify({'message': 'Post deleted'}), 200
    else:
        return jsonify({'error': 'Post not found'}), 404
        post.description = data['description']
        post.main_content = data['main_content']
        post.ingredients = data['ingredients']
        post.prep_time = data['prep_time']
        post.cook_time = data['cook_time']
        post.servings = data['servings']
        post.difficulty = data['difficulty']
        post.category = data.get('category')
        post.instructions = data.get('instructions')
        post.tips = data.get('tips')
        post.dish = data.get('dish')
        post.featured_image = data.get('featured_image')
        post.url_slug = data.get('url_slug')
        db_session.commit()


def delete_cms_post(post_id):
    db_session = get_db()
    post = db_session.query(CMS).filter_by(id=post_id).first()
    if post:
        db_session.delete(post)
        db_session.commit()
        return jsonify({'message': 'Post deleted'}), 200
    else:
        return jsonify({'error': 'Post not found'}), 404
