import logging
import logging.handlers
import os
import time
import uuid
from datetime import datetime
from flask import request, has_request_context, g
from pythonjsonlogger import jsonlogger
from functools import wraps

class CustomJsonFormatter(jsonlogger.JsonFormatter):
    def add_fields(self, log_record, record, message_dict):
        super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)
        if has_request_context():
            log_record['ip'] = request.remote_addr
            log_record['url'] = request.url
            log_record['method'] = request.method
            # Add request ID if available
            if hasattr(g, 'request_id'):
                log_record['request_id'] = g.request_id
        log_record['timestamp'] = datetime.utcnow().isoformat()
        log_record['level'] = record.levelname
        log_record['logger'] = record.name

def generate_request_id():
    """Generate a unique request ID"""
    return str(uuid.uuid4())

def log_performance(logger, threshold_ms=500):
    """
    Decorator to log function performance only when execution time exceeds threshold
    Args:
        logger: Logger instance to use
        threshold_ms: Threshold in milliseconds above which to log (default 500ms)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = (time.time() - start_time) * 1000  # Convert to milliseconds
                if duration > threshold_ms:
                    logger.warning(
                        f"Slow execution detected: {func.__name__}",
                        extra={
                            'duration_ms': duration,
                            'function': func.__name__,
                            'threshold_ms': threshold_ms
                        }
                    )
        return wrapper
    return decorator

def setup_specialized_logger(name, filename, level=logging.INFO):
    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger.handlers.clear()
    
    # JSON formatter
    json_formatter = CustomJsonFormatter('%(timestamp)s %(level)s %(name)s %(message)s')
    
    # File handler with rotation
    handler = logging.handlers.RotatingFileHandler(
        filename,
        maxBytes=5242880,  # 5MB
        backupCount=5
    )
    handler.setFormatter(json_formatter)
    logger.addHandler(handler)
    
    return logger

def setup_logger(app):
    # Ensure logs directory exists
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # Set up main application logger
    logger = logging.getLogger('applogger')
    logger.setLevel(logging.INFO if not app.debug else logging.DEBUG)
    logger.handlers.clear()

    # JSON formatter for structured logging
    json_formatter = CustomJsonFormatter('%(timestamp)s %(level)s %(name)s %(message)s')

    # File handler for all logs with compression
    file_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'app.log'),
        maxBytes=10485760,  # 10MB
        backupCount=10
    )
    file_handler.setFormatter(json_formatter)
    file_handler.setLevel(logging.INFO)  # Log INFO and WARNING to app.log
    file_handler.addFilter(lambda record: record.levelno < logging.ERROR)  # Filter out ERROR and CRITICAL
    logger.addHandler(file_handler)

    # Separate error log file
    error_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'error.log'),
        maxBytes=10485760,  # 10MB
        backupCount=10
    )
    error_handler.setLevel(logging.ERROR)  # Log ERROR and CRITICAL to error.log
    error_handler.setFormatter(json_formatter)
    logger.addHandler(error_handler)

    # Request tracking middleware
    @app.before_request
    def before_request():
        g.start_time = time.time()
        g.request_id = generate_request_id()

    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            duration = (time.time() - g.start_time) * 1000
            
            # Only log non-error slow requests as warnings in app.log
            if duration > 500 and response.status_code < 400:
                logger.warning(
                    'Slow request detected',
                    extra={
                        'duration_ms': duration,
                        'status_code': response.status_code,
                        'request_id': g.request_id,
                        'path': request.path
                    }
                )
            # Log all non-error requests at DEBUG level for debugging when needed
            elif app.debug and response.status_code < 400:
                logger.debug(
                    'Request processed',
                    extra={
                        'duration_ms': duration,
                        'status_code': response.status_code,
                        'request_id': g.request_id
                    }
                )
            
            # Log errors only to error.log
            if response.status_code >= 400:
                logging.getLogger('applogger').error(
                    'Request error',
                    extra={
                        'duration_ms': duration,
                        'status_code': response.status_code,
                        'request_id': g.request_id,
                        'path': request.path
                    }
                )
        return response

    # Set up security logger
    setup_specialized_logger(
        'security',
        os.path.join(log_dir, 'login_security.log'),
        level=logging.INFO
    )

    # Set SQLAlchemy logging
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)

    # Werkzeug logging (only warnings and errors)
    logging.getLogger('werkzeug').setLevel(logging.WARNING)

    return logger

# Convenience function to get security logger
def get_security_logger():
    return logging.getLogger('security')
