# Enable mod_rewrite
<IfModule mod_rewrite.c>
RewriteEngine On

# Redirect from the subdomain to the main domain
RewriteCond %{HTTP_HOST} ^karbokari\.hostmaster\.stream$ [NC]
RewriteRule ^(.*)$ https://karbokari.com/$1 [L,R=301]

# Redirect from HTTP to HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirect from www to non-www
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^ https://%1%{REQUEST_URI} [L,R=301]

# Redirect index.html to directory root
RewriteCond %{THE_REQUEST} /index\.html [NC]
RewriteRule ^(.*/)?index\.html$ /$1 [R=301,L]
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php81” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php81 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
