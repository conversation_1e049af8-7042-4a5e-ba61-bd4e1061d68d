{% extends "cms-base.html" %}

{% block title %}Kladder{% endblock %}

{% block content %}
<div class="container py-5">
    <h1 class="mb-4">Utkast</h1>
    
    {% if drafts %}
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>Tittel</th>
                    <th>Type</th>
                    <th>Sist oppdatert</th>
                    <th><PERSON><PERSON></th>
                </tr>
            </thead>
            <tbody>
                {% for draft in drafts %}
                <tr>
                    <td>{{ draft.title }}</td>
                    <td>
                        {% if draft.post_type == 'oppskrift' %}
                        <span class="badge bg-success">Oppskrift</span>
                        {% else %}
                        <span class="badge bg-primary">Blogg</span>
                        {% endif %}
                    </td>
                    <td>{{ draft.updated.strftime('%d.%m.%Y %H:%M') }}</td>
                    <td>
                        <a href="{{ url_for('cms.cms_edit', post_id=draft.id) }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-pencil"></i> Rediger
                        </a>
                        <button class="btn btn-sm btn-danger" onclick="confirmDelete({{ draft.id }})">
                            <i class="bi bi-trash"></i> Slett
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">
        Ingen utkast funnet.
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bekreft sletting</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Er du sikker på at du vil slette dette utkastet?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Avbryt</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Slett</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(postId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = `/cms/${postId}`;
    form.method = 'POST';  // We'll override this with DELETE in the backend
    modal.show();
}
</script>
{% endblock %}
