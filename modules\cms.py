from flask import (
    Blueprint, render_template, request, redirect, 
    url_for, flash, jsonify, current_app
)
from flask_login import login_required, current_user
import logging
import os
import json
from werkzeug.utils import secure_filename
from datetime import datetime
from .models import db, CMS
from .forms import CMS<PERSON>orm
from slugify import slugify as slugify_original
from functools import wraps
from PIL import Image
import io

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Create Blueprint
cms = Blueprint('cms', __name__)

# Add template filters
@cms.app_template_filter('json_loads')
def json_loads_filter(value):
    """Template filter to parse JSON string"""
    try:
        if value:
            return json.loads(value)
        return []
    except (json.JSONDecodeError, TypeError):
        return []

def allowed_file(filename, allowed_extensions):
    """Check if the file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def allowed_file_original(filename):
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'avif', 'mp4', 'webm', 'ogg', 'mkv'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Ensure the upload directory exists
def ensure_upload_dir():
    upload_dir = os.path.join('assets', 'img')
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
    return upload_dir

def slugify(text):
    """
    Custom slugify function that properly handles Norwegian special characters.
    Converts Æ/æ to ae, Ø/ø to o, and Å/å to a before slugifying.
    """
    replacements = {
        'æ': 'ae',
        'Æ': 'ae',
        'ø': 'o',
        'Ø': 'o',
        'å': 'a',
        'Å': 'a'
    }
    for old, new in replacements.items():
        text = text.replace(old, new)
    return slugify_original(text)

def convert_to_webp(file):
    """Convert an image file to high-quality WebP format.
    
    Args:
        file: FileStorage object from request.files
        
    Returns:
        BytesIO object containing the WebP image
    """
    # Read the image using Pillow
    image = Image.open(file)
    
    # Convert RGBA to RGB if necessary (WebP doesn't support RGBA)
    if image.mode in ('RGBA', 'LA'):
        background = Image.new('RGB', image.size, (255, 255, 255))
        background.paste(image, mask=image.split()[-1])
        image = background
    
    # Create BytesIO object to hold the converted image
    webp_image = io.BytesIO()
    
    # Save as WebP with maximum quality
    image.save(webp_image, 'WEBP', quality=100, lossless=True)
    webp_image.seek(0)
    
    return webp_image

@cms.route('/cms-edit-<int:post_id>.html', methods=['GET', 'POST'])
@cms.route('/cms-new.html', methods=['GET', 'POST'])
@login_required
def cms_edit(post_id=None):
    post = None
    if post_id:
        post = CMS.query.get_or_404(post_id)
        form = CMSForm(obj=post)
        # Explicitly set ingredients_json for recipe posts
        if post.post_type == 'oppskrift' and post.ingredients:
            form.ingredients_json.data = post.ingredients
    else:
        form = CMSForm()
    
    if form.validate_on_submit():
        if post is None:
            post = CMS()
            post.author = current_user.alias
            post.created = datetime.now()

        # Update post fields from form, excluding featured_image and ingredients_json which we handle separately
        form_data = {field.name: field.data for field in form if field.name not in ['featured_image', 'ingredients_json']}
        for field, value in form_data.items():
            # Convert empty strings to None for select fields
            if field in ['difficulty', 'category'] and value == '':
                setattr(post, field, None)
            else:
                setattr(post, field, value)
        
        post.updated = datetime.now()
        
        # Generate URL slug if not exists
        if not post.url_slug:
            post.url_slug = slugify(form.title.data)

        # Handle recipe-specific data
        if post.post_type == 'oppskrift':
            # Save recipe fields
            post.main_content = form.main_content.data
            post.instructions = form.instructions.data
            post.tips = form.tips.data
            
            # Save ingredients JSON data directly from request
            ingredients_json = request.form.get('ingredients_json')
            if ingredients_json:
                try:
                    # Validate JSON format
                    ingredients = json.loads(ingredients_json)
                    post.ingredients = ingredients_json
                except json.JSONDecodeError as e:
                    flash('Det oppstod en feil med ingrediensene. Sjekk at formatet er riktig.', 'error')
                    post.ingredients = '[]'
            else:
                post.ingredients = '[]'  # Set empty list if no ingredients

        # Handle file upload only if a new file is selected
        if form.featured_image.data and form.featured_image.data.filename:
            file = form.featured_image.data
            if allowed_file_original(file.filename):
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{timestamp}_{filename}"
                
                # Ensure upload directory exists and save file
                upload_dir = ensure_upload_dir()
                file_path = os.path.join(upload_dir, filename)
                file.save(file_path)
                post.featured_image = f"/assets/img/{filename}"

        try:
            if post_id is None:
                db.session.add(post)
            db.session.commit()
            flash('Innlegget ble lagret!', 'success')
            return redirect(url_for('cms.cms_edit', post_id=post.id))
        except Exception as e:
            db.session.rollback()
            flash('En feil oppstod under lagring. Prøv igjen.', 'error')

    return render_template('cms-edit.html', form=form, post=post)

@cms.route('/cms-drafts.html')
@login_required
def cms_view_drafts():
    """Route for viewing all draft posts"""
    drafts = CMS.query.filter_by(draft=True).order_by(CMS.id.asc()).all()
    return render_template('cms-drafts.html', drafts=drafts)

@cms.route('/cms', methods=['GET'])
def cms_posts():
    """Return all published posts in JSON format"""
    try:
        posts = CMS.query.filter_by(draft=False).order_by(CMS.id.asc()).all()
        return jsonify([{
            'id': post.id,
            'title': post.title,
            'url_slug': post.url_slug,
            'description': post.description,
            'content': post.content,
            'featured_image': post.featured_image,
            'created': post.created,
            'post_type': post.post_type
        } for post in posts])
    except Exception as e:
        current_app.logger.error(f"Error fetching posts: {str(e)}")
        return jsonify({'error': 'Could not fetch posts'}), 500

@cms.route('/cms/<int:post_id>', methods=['GET'])
@login_required
def cms_post(post_id):
    post = CMS.query.get_or_404(post_id)
    return jsonify({
        'id': post.id,
        'title': post.title,
        'content': post.content,
        'status': post.status,
        'post_type': post.post_type,
        'updated': post.updated.isoformat() if post.updated else None
    })

@cms.route('/cms', methods=['POST'])
@login_required
def cms_create_post():
    data = request.get_json()
    post = CMS(
        title=data.get('title'),
        content=data.get('content'),
        status=data.get('status', 'draft'),
        post_type=data.get('post_type', 'blog'),
        author=current_user.alias,
        created=datetime.now(),
        updated=datetime.now()
    )
    db.session.add(post)
    db.session.commit()
    flash('Innlegget ble lagret', 'success')
    return jsonify({'id': post.id}), 201

@cms.route('/cms/<int:post_id>', methods=['PUT'])
@login_required
def cms_update_post(post_id):
    post = CMS.query.get_or_404(post_id)
    data = request.get_json()
    post.title = data.get('title', post.title)
    post.content = data.get('content', post.content)
    post.status = data.get('status', post.status)
    post.post_type = data.get('post_type', post.post_type)
    post.updated = datetime.now()
    db.session.commit()
    flash('Innlegget ble oppdatert', 'success')
    return jsonify({'status': 'success'})

@cms.route('/cms/<int:post_id>', methods=['POST', 'DELETE'])
@login_required
def cms_delete_post(post_id):
    post = CMS.query.get_or_404(post_id)
    try:
        post = CMS.query.get_or_404(post_id)
        db.session.delete(post)
        db.session.commit()
        flash('Innlegget ble slettet', 'success')
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        flash('En feil oppstod under sletting. Prøv igjen.', 'error')
        return jsonify({'success': False})

# Ingredient Routes
@cms.route('/ingredients', methods=['GET'])
@login_required
def ingredients():
    ingredients = Ingredients.query.all()
    return jsonify([{
        'id': ingredient.id,
        'name': ingredient.name,
        'unit': ingredient.unit,
        'calories': ingredient.calories,
        'protein': ingredient.protein,
        'fat': ingredient.fat,
        'carbs': ingredient.carbs
    } for ingredient in ingredients])

@cms.route('/ingredients/<int:ingredient_id>', methods=['GET'])
@login_required
def ingredient(ingredient_id):
    ingredient = Ingredients.query.get_or_404(ingredient_id)
    return jsonify({
        'id': ingredient.id,
        'name': ingredient.name,
        'unit': ingredient.unit,
        'calories': ingredient.calories,
        'protein': ingredient.protein,
        'fat': ingredient.fat,
        'carbs': ingredient.carbs
    })

@cms.route('/ingredients', methods=['POST'])
@login_required
def create_ingredient():
    data = request.get_json()
    ingredient = Ingredients(
        name=data.get('name'),
        unit=data.get('unit'),
        calories=data.get('calories'),
        protein=data.get('protein'),
        fat=data.get('fat'),
        carbs=data.get('carbs')
    )
    db.session.add(ingredient)
    db.session.commit()
    flash('Innlegget ble lagret', 'success')
    return jsonify({'id': ingredient.id}), 201

@cms.route('/ingredients/<int:ingredient_id>', methods=['PUT'])
@login_required
def update_ingredient(ingredient_id):
    ingredient = Ingredients.query.get_or_404(ingredient_id)
    data = request.get_json()
    ingredient.name = data.get('name', ingredient.name)
    ingredient.unit = data.get('unit', ingredient.unit)
    ingredient.calories = data.get('calories', ingredient.calories)
    ingredient.protein = data.get('protein', ingredient.protein)
    ingredient.fat = data.get('fat', ingredient.fat)
    ingredient.carbs = data.get('carbs', ingredient.carbs)
    db.session.commit()
    flash('Innlegget ble oppdatert', 'success')
    return jsonify({'status': 'success'})

@cms.route('/ingredients/<int:ingredient_id>', methods=['DELETE'])
@login_required
def delete_ingredient(ingredient_id):
    ingredient = Ingredients.query.get_or_404(ingredient_id)
    db.session.delete(ingredient)
    db.session.commit()
    flash('Innlegget ble slettet', 'success')
    return jsonify({'status': 'success'})

def log_upload_error(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Log the full error details
            current_app.logger.error(f"Upload Error: {str(e)}", exc_info=True)
            return jsonify({
                'error': {
                    'message': f'Upload failed: {str(e)}',
                    'details': str(e)
                }
            }), 500
    return wrapper

@cms.route('/upload-image', methods=['POST'])
@login_required
def upload_image():
    """Handle image and video upload from CKEditor"""
    try:
        # Log the entire request details for debugging
        logging.info(f"Upload request received. Files: {list(request.files.keys())}")
        
        if 'upload' not in request.files:
            logging.warning("No file part in the request")
            return jsonify({
                'error': {
                    'message': 'No file uploaded'
                }
            }), 400
        
        file = request.files['upload']
        
        if file.filename == '':
            logging.warning("No selected file")
            return jsonify({
                'error': {
                    'message': 'No file selected'
                }
            }), 400
        
        if not allowed_file_original(file.filename):
            logging.warning(f"Invalid file type: {file.filename}")
            return jsonify({
                'error': {
                    'message': 'Invalid file type'
                }
            }), 400
        
        # Generate secure filename with timestamp
        original_filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_ext = os.path.splitext(original_filename)[1].lower()
        filename = f"{timestamp}_{original_filename}"
        
        # Determine upload folder based on file type
        if file_ext in {'.mp4', '.webm', '.ogg', '.mkv'}:
            # Video files go to assets/videos
            upload_folder = os.path.join(current_app.root_path, '..', 'assets', 'videos')
            os.makedirs(upload_folder, exist_ok=True)
            filepath = os.path.join(upload_folder, filename)
            
            # Save the original video file
            file.save(filepath)
            
            # Return URL for videos
            file_url = f"{request.url_root}assets/videos/{filename}"
        else:
            # Image files go to assets/img and get converted to WebP
            upload_folder = os.path.join(current_app.root_path, '..', 'assets', 'img')
            os.makedirs(upload_folder, exist_ok=True)
            
            # Convert to WebP
            webp_image = convert_to_webp(file)
            filename = f"{timestamp}_{os.path.splitext(original_filename)[0]}.webp"
            filepath = os.path.join(upload_folder, filename)
            
            # Save the WebP image
            with open(filepath, 'wb') as f:
                f.write(webp_image.getvalue())
            
            # Return URL for images
            file_url = f"{request.url_root}assets/img/{filename}"
            
        logging.info(f"File saved successfully: {filepath}")
        
        return jsonify({
            'uploaded': 1,
            'fileName': filename,
            'url': file_url
        })
    
    except Exception as e:
        # Log the full error details
        logging.error(f"Upload Error: {str(e)}", exc_info=True)
        return jsonify({
            'error': {
                'message': f'Upload failed: {str(e)}'
            }
        }), 500

@cms.route('/cms-upload-image', methods=['POST'])
@login_required
def upload_image_original():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    if file and allowed_file_original(file.filename):
        # Convert to WebP
        webp_image = convert_to_webp(file)
        
        # Generate filename with .webp extension
        original_filename = secure_filename(file.filename)
        filename = f"{os.path.splitext(original_filename)[0]}.webp"
        
        upload_dir = ensure_upload_dir()
        file_path = os.path.join(upload_dir, filename)
        
        # Save the WebP image
        with open(file_path, 'wb') as f:
            f.write(webp_image.getvalue())
            
        return jsonify({'location': f'/assets/img/{filename}'})
    return jsonify({'error': 'Invalid file type'}), 400

@cms.route('/cms-delete-<int:post_id>', methods=['POST'])
@login_required
def delete_post(post_id):
    post = CMS.query.get_or_404(post_id)
    try:
        # Delete the featured image file if it exists
        if post.featured_image:
            # Convert URL path to filesystem path
            image_path = post.featured_image.lstrip('/')  # Remove leading slash
            if os.path.exists(image_path):
                os.remove(image_path)
        
        db.session.delete(post)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Innlegget ble slettet'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting post {post_id}: {str(e)}")
        return jsonify({'error': 'En feil oppstod under sletting. Prøv igjen.'}), 500

@cms.route('/cms-index.html')
def cms_index_page():
    """Display the blog index page with posts"""
    posts = CMS.query.filter_by(draft=False).order_by(CMS.id.asc()).all()
    return render_template('cms-index.html', posts=posts)

@cms.route('/<string:url_slug>')
def blog_post(url_slug):
    """Display a single blog post"""
    # Try to get post from cache first
    cache_key = f'blog_post_{url_slug}'
    post = current_app.cache.get(cache_key) if hasattr(current_app, 'cache') else None
    
    if post is None:
        post = CMS.query.filter_by(url_slug=url_slug, draft=False).first_or_404()
        
        # Pre-parse ingredients JSON if it exists
        if post.ingredients and post.post_type == 'oppskrift':
            try:
                post.parsed_ingredients = json.loads(post.ingredients)
            except (json.JSONDecodeError, TypeError):
                post.parsed_ingredients = []
                
        # Cache the post for 5 minutes
        if hasattr(current_app, 'cache'):
            current_app.cache.set(cache_key, post, timeout=300)
    
    return render_template('cms-post.html', post=post)

@cms.route('/cms')
def blog_index():
    """Display the blog index page"""
    return render_template('cms-index.html')

@cms.route('/cms', methods=['GET'])
def public_cms_posts():
    posts = CMS.query.filter_by(draft=False).order_by(CMS.id.asc()).all()
    return jsonify([{
        'id': post.id,
        'title': post.title,
        'url_slug': post.url_slug,
        'description': post.description,
        'content': post.content,
        'featured_image': post.featured_image,
        'created': post.created,
        'post_type': post.post_type
    } for post in posts])
