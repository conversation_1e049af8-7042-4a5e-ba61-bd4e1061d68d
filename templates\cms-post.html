{% extends "cms-base.html" %}

{% block head %}
{% endblock %}

{% block title %}{{ post.title }} - KETOLABBEN{% endblock %}

{% block meta_description %}{{ post.description }}{% endblock %}

{% block content %}
<article class="container py-5">
    <!-- Post Header -->
    <header class="row justify-content-center mb-5">
        <div class="col-12 col-lg-10 col-xl-8 text-center">
            <h1 class="display-5 fw-bold mb-4">{{ post.title }}</h1>

            <!-- Featured Image -->
            {% if post.featured_image %}
            <div class="featured-image-container">
                <img 
                    src="/api/placeholder/{{ post.featured_image.split('/')[-1] }}"
                    data-src="{{ post.featured_image }}"
                    alt="{{ post.title }}"
                    loading="eager"
                    fetchpriority="high"
                >
            </div>
            {% endif %}

            <div class="text-muted mb-4">
                {% if post.author %}
                <span><i class="bi bi-person"></i> {{ post.author }}</span>
                {% endif %}
                {% if post.created %}
                <span class="mx-3">|</span>
                <span><i class="bi bi-calendar3"></i> {{ post.created.strftime('%Y-%m-%d') }}</span>
                {% endif %}
                {% if post.post_type %}
                <span class="mx-3">|</span>
                <span><i class="bi bi-bookmark"></i> {{ post.post_type|title }}</span>
                {% endif %}
                {% if post.category %}
                <span class="mx-3">|</span>
                <span><i class="bi bi-tag"></i> {{ post.category }}</span>
                {% endif %}
            </div>

            {% if post.description %}
            <p class="lead text-muted">{{ post.description }}</p>
            {% endif %}
        </div>
    </header>

    <!-- Main Content Area -->
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
            <!-- Recipe Details (if post_type is 'oppskrift') -->
            {% if post.post_type == 'oppskrift' %}
            <div class="card shadow-sm mb-5">
                <div class="card-body py-4">
                    <div class="row g-4">
                        {% if post.prep_time %}
                        <div class="col-6 col-md-3 text-center">
                            <div class="p-3 rounded-3 bg-light">
                                <i class="bi bi-clock fs-4 text-primary mb-2"></i>
                                <p class="mb-1"><strong>Forberedelse</strong></p>
                                <p class="mb-0">{{ post.prep_time }} min</p>
                            </div>
                        </div>
                        {% endif %}
                        {% if post.cook_time %}
                        <div class="col-6 col-md-3 text-center">
                            <div class="p-3 rounded-3 bg-light">
                                <i class="bi bi-fire fs-4 text-primary mb-2"></i>
                                <p class="mb-1"><strong>Tilberedning</strong></p>
                                <p class="mb-0">{{ post.cook_time }} min</p>
                            </div>
                        </div>
                        {% endif %}
                        {% if post.servings %}
                        <div class="col-6 col-md-3 text-center">
                            <div class="p-3 rounded-3 bg-light">
                                <i class="bi bi-people fs-4 text-primary mb-2"></i>
                                <p class="mb-1"><strong>Porsjoner</strong></p>
                                <p class="mb-0">{{ post.servings }}</p>
                            </div>
                        </div>
                        {% endif %}
                        {% if post.difficulty %}
                        <div class="col-6 col-md-3 text-center">
                            <div class="p-3 rounded-3 bg-light">
                                <i class="bi bi-stars fs-4 text-primary mb-2"></i>
                                <p class="mb-1"><strong>Vanskelighetsgrad</strong></p>
                                <p class="mb-0">{{ post.difficulty }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Ingredients and Instructions -->
            {% set ingredients = post.ingredients|json_loads if post.ingredients else [] %}
            {% if ingredients %}
            <div class="row g-4 mb-5">
                <div class="col-12 col-md-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white py-3">
                            <h2 class="h5 mb-0">Ingredienser</h2>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                {% for ingredient in ingredients %}
                                <li class="py-2 border-bottom">
                                    <i class="bi bi-check2 text-primary me-2"></i>
                                    {{ ingredient.quantity }} {{ ingredient.unit }} {{ ingredient.name }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-8">
                    {% if post.instructions %}
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white py-3">
                            <h2 class="h5 mb-0">Fremgangsmåte</h2>
                        </div>
                        <div class="card-body">
                            {{ post.instructions|safe }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
            {% endif %}

            <!-- Main Content -->
            <div class="post-content">
                {% if post.main_content and post.main_content != 'None' %}
                {{ post.main_content|process_images|safe }}
                {% endif %}
            </div>

            <!-- Social Share Section -->
            <div class="social-share-section mt-4">
                <div class="share-container">
                    <div class="share-text">
                        Del med en venn!
                    </div>
                    <div class="share-buttons d-flex gap-2">
                        <button class="btn btn-facebook social-btn" data-platform="facebook"
                            aria-label="Del på Facebook">
                            <i class="bi bi-facebook"></i>
                        </button>
                        <button class="btn btn-instagram social-btn" data-platform="instagram"
                            aria-label="Del på Instagram">
                            <i class="bi bi-instagram"></i>
                        </button>
                        <button class="btn btn-snapchat social-btn" data-platform="snapchat"
                            aria-label="Del på Snapchat">
                            <i class="bi bi-snapchat"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tips (if post_type is 'oppskrift') -->
            {% if post.post_type == 'oppskrift' and post.tips %}
            <div class="card bg-light border-0 shadow-sm mt-5">
                <div class="card-body p-4">
                    <h2 class="h5 mb-3">
                        <i class="bi bi-lightbulb text-primary me-2"></i>
                        Tips og triks
                    </h2>
                    <div class="tips">
                        {{ post.tips|safe }}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Tags -->
            {% if post.tags %}
            <div class="mt-5">
                {% for tag in post.tags.split(',') %}
                <a href="#" class="text-decoration-none">
                    <span class="badge bg-light text-dark me-2 mb-2 py-2 px-3">
                        <i class="bi bi-tag-fill me-1 text-primary"></i>
                        {{ tag.strip() }}
                    </span>
                </a>
                {% endfor %}
            </div>
            {% endif %}

            {% if current_user.is_authenticated %}
            <div class="d-flex justify-content-center gap-3 mt-5 pt-4 border-top">
                <a href="/cms-edit-{{ post.id }}.html" class="btn btn-outline-primary">
                    <i class="bi bi-pencil"></i> Rediger
                </a>
                <button onclick="deletePost({{ post.id }})" class="btn btn-outline-danger">
                    <i class="bi bi-trash"></i> Slett
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</article>
{% endblock %}

{% block scripts %}
<script defer>
    async function shareContent(platform) {
        const url = window.location.href;
        const title = '{{ post.title }}';
        const description = '{{ post.description }}';

        // Try Web Share API first if available
        if (navigator.share) {
            try {
                await navigator.share({
                    title: title,
                    text: description,
                    url: url,
                });
                return;
            } catch (error) {
                console.log('Error with Web Share API:', error);
                // Fall back to platform-specific sharing
            }
        }

        // Platform-specific sharing URLs
        const shareUrls = {
            facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
            instagram: `https://www.instagram.com/direct/new/?text=${encodeURIComponent(title)} ${encodeURIComponent(url)}`,
            snapchat: `https://www.snapchat.com/scan?link=${encodeURIComponent(url)}&type=web&share_id=${encodeURIComponent(title)}`,
        };

        // Open share dialog in a popup window
        const width = 600;
        const height = 400;
        const left = (window.innerWidth - width) / 2;
        const top = (window.innerHeight - height) / 2;

        window.open(
            shareUrls[platform],
            'share-dialog',
            `width=${width},height=${height},left=${left},top=${top},location=no,menubar=no`
        );
    }

    function deletePost(postId) {
        if (!confirm('Er du sikker på at du vil slette dette innlegget?')) {
            return;
        }

        fetch(`/cms-delete-${postId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
            .then(response => {
                if (response.ok) {
                    window.location.href = '/';
                } else {
                    throw new Error('Failed to delete post');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Det oppstod en feil under sletting av innlegget.');
            });
    }

    function initSocialShare() {
        const shareButtons = document.querySelectorAll('.social-btn');
        shareButtons.forEach(button => {
            button.addEventListener('click', function () {
                const platform = this.getAttribute('data-platform');
                shareContent(platform);
            });
        });
    }

    document.addEventListener('DOMContentLoaded', initSocialShare);
</script>
{% endblock %}

{% block styles %}
{% endblock %}