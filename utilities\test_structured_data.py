import sys
import os
import json
from datetime import datetime
from urllib.parse import urljoin

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import Flask and create a test app
from flask import Flask, request
from modules import create_app

# Create a test app
test_app = Flask(__name__)

# Import structured data functions
from modules.structured_data import (
    generate_recipe_schema, generate_article_schema, 
    generate_product_schema, generate_book_schema, 
    generate_review_schema
)

# Override the urljoin function in structured_data module
import modules.structured_data
original_urljoin = modules.structured_data.urljoin
modules.structured_data.urljoin = lambda base, url: original_urljoin("https://ketolabben.com/", url)

def test_recipe_schema():
    """Test the recipe schema generation"""
    recipe = {
        'title': 'Keto Pannekaker',
        'description': 'Deilige lavkarbo pannekaker som er perfekte for ketogen diett.',
        'author': 'Ketolabben',
        'featured_image': '/assets/img/recipes/keto-pannekaker.jpg',
        'category': 'Frokost',
        'tags': 'keto, lavkarbo, frokost, pannekaker',
        'servings': 2,
        'prep_time': 10,
        'cook_time': 15,
        'created': datetime.now(),
        'ingredients': json.dumps([
            {'quantity': '2', 'unit': 'stk', 'name': 'egg'},
            {'quantity': '2', 'unit': 'ss', 'name': 'mandelmel'},
            {'quantity': '1', 'unit': 'ss', 'name': 'kokosolje'}
        ]),
        'instructions': '<ol><li>Bland alle ingrediensene i en bolle.</li><li>Varm opp en stekepanne med kokosolje.</li><li>Hell røren i pannen og stek til gyllen.</li></ol>',
        'nutrition': {
            'calories': '320 kcal',
            'carbs': '3g',
            'fat': '28g',
            'protein': '12g'
        }
    }
    
    schema = generate_recipe_schema(recipe)
    print("Recipe Schema:")
    print(json.dumps(json.loads(schema), indent=2))
    print("\n")

def test_article_schema():
    """Test the article schema generation"""
    article = {
        'title': 'Fordeler med Ketogen Diett',
        'description': 'Lær om de mange helsefordelene ved å følge en ketogen diett.',
        'author': 'Ketolabben',
        'featured_image': '/assets/img/articles/keto-benefits.jpg',
        'created': datetime.now(),
        'updated': datetime.now(),
        'tags': 'keto, helse, diett, vekttap'
    }
    
    schema = generate_article_schema(article)
    print("Article Schema:")
    print(json.dumps(json.loads(schema), indent=2))
    print("\n")

def test_product_schema():
    """Test the product schema generation"""
    product = {
        'title': 'Ketolabben Kokebok',
        'description': 'En komplett guide til ketogen kosthold med deilige oppskrifter.',
        'featured_image': '/assets/img/products/cookbook.jpg',
        'price': '349.00',
        'reviews': [
            {
                'author': 'Kunde',
                'rating': 5,
                'content': 'Fantastisk kokebok med enkle og smakfulle oppskrifter!'
            }
        ]
    }
    
    schema = generate_product_schema(product)
    print("Product Schema:")
    print(json.dumps(json.loads(schema), indent=2))
    print("\n")

def test_book_schema():
    """Test the book schema generation"""
    book = {
        'title': 'Ketolabben Kokebok',
        'author': 'Ketolabben',
        'description': 'En komplett guide til ketogen kosthold med deilige oppskrifter.',
        'isbn': '9788293780014',
        'price': '349.00',
        'featured_image': '/assets/img/book-cover.jpg',
        'reviews': [
            {
                'author': 'Leser',
                'rating': 5,
                'content': 'Fantastisk kokebok med enkle og smakfulle oppskrifter!'
            }
        ]
    }
    
    schema = generate_book_schema(book)
    print("Book Schema:")
    print(json.dumps(json.loads(schema), indent=2))
    print("\n")

def test_review_schema():
    """Test the review schema generation"""
    reviews = [
        {
            'author': 'Kunde 1',
            'rating': 5,
            'content': 'Veldig fornøyd med produktet!'
        },
        {
            'author': 'Kunde 2',
            'rating': 4,
            'content': 'God kvalitet, men litt høy pris.'
        }
    ]
    
    schema = generate_review_schema(reviews, "Ketolabben Kokebok", "Book")
    print("Review Schema:")
    print(json.dumps(json.loads(schema), indent=2))
    print("\n")

if __name__ == '__main__':
    print("Testing Structured Data Generation\n")
    
    # Use the test app context
    with test_app.test_request_context('https://ketolabben.com/'):
        test_recipe_schema()
        test_article_schema()
        test_product_schema()
        test_book_schema()
        test_review_schema()
    
    print("All tests completed.")