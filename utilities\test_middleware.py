from flask import Flask, Response
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Create a test app
app = Flask(__name__)

# Test HTML content
HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>Middleware Test</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Middleware Test</h1>
    <p>This page tests Flask middleware.</p>
</body>
</html>
"""

@app.route('/')
def test_page():
    """Test page for middleware"""
    return HTML

# Add middleware to process all responses
@app.after_request
def process_response(response):
    logger.debug("Processing response...")
    
    if response.content_type and response.content_type.startswith('text/html'):
        logger.debug("Response is HTML.")
        
        # Get the HTML content
        content = response.get_data(as_text=True)
        
        # Add a script tag to the head
        script_tag = '<script type="application/test">Test Script</script>'
        
        # Check if we have a head tag
        if '</head>' in content:
            logger.debug("Found head tag.")
            processed = content.replace('</head>', f'{script_tag}\n</head>')
        else:
            logger.debug("No head tag found.")
            processed = content
        
        # Set the processed content back to the response
        response.set_data(processed)
        
        # Log the processed HTML
        logger.debug("Processed HTML:")
        logger.debug(processed)
    
    return response

if __name__ == '__main__':
    print("Starting test server at http://127.0.0.1:5001")
    app.run(debug=True, host='127.0.0.1', port=5001)