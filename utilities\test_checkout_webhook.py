#!/usr/bin/env python3
"""
Test webhook with a proper checkout.session.completed event
"""
import requests
import json
from datetime import datetime

def test_checkout_session_completed():
    """Test webhook with a checkout.session.completed event"""
    print("Testing checkout.session.completed webhook...")
    
    # Realistic checkout session completed event
    checkout_event = {
        "id": "evt_test_checkout_completed",
        "object": "event",
        "api_version": "2020-08-27",
        "created": int(datetime.now().timestamp()),
        "data": {
            "object": {
                "id": "cs_test_checkout_session_123",
                "object": "checkout.session",
                "payment_status": "paid",
                "status": "complete",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Test Customer"
                },
                "amount_total": 29900,  # $299.00 in cents
                "currency": "usd",
                "payment_intent": "pi_test_payment_intent_123"
            }
        },
        "livemode": False,
        "pending_webhooks": 1,
        "request": {
            "id": None,
            "idempotency_key": None
        },
        "type": "checkout.session.completed"
    }
    
    try:
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=json.dumps(checkout_event),
            headers={'Content-Type': 'application/json'},
            verify=False,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Checkout session webhook processed successfully!")
            
            # Check if debug file was created
            import os
            debug_dir = "instance/webhook_debug"
            if os.path.exists(debug_dir):
                debug_files = [f for f in os.listdir(debug_dir) if 'checkout.session.completed' in f]
                if debug_files:
                    print(f"✅ Debug file created: {debug_files[-1]}")
                    
                    # Check if customer data was saved to database
                    print("\nChecking database for customer record...")
                    try:
                        import sys
                        import os
                        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
                        
                        from modules import create_app
                        from modules.models import CustomerData
                        
                        app = create_app()
                        with app.app_context():
                            customer = CustomerData.query.filter_by(
                                download_code="cs_test_checkout_session_123"
                            ).first()
                            
                            if customer:
                                print(f"✅ Customer record found in database!")
                                print(f"   Name: {customer.customer_name}")
                                print(f"   Email: {customer.customer_email}")
                                print(f"   Download code: {customer.download_code}")
                                print(f"   Payment status: {customer.payment_status}")
                                print(f"   Email sent: {customer.email_sent}")
                            else:
                                print("❌ Customer record not found in database")
                                
                    except Exception as db_error:
                        print(f"Error checking database: {str(db_error)}")
            
            return True
        else:
            print(f"❌ Webhook failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Checkout Session Webhook Test")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    success = test_checkout_session_completed()
    
    if success:
        print("\n🎉 Webhook test completed successfully!")
        print("The webhook is working correctly for checkout.session.completed events.")
    else:
        print("\n❌ Webhook test failed")