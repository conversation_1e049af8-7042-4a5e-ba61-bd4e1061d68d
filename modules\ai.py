from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required
import traceback
from openai import (
    OpenAI,
    OpenAIError,
    APIConnectionError,
    RateLimitError,
    AuthenticationError,
    BadRequestError,
)

# Create blueprint
ai_bp = Blueprint('ai', __name__)

@ai_bp.route("/api/ai/enhance", methods=["POST"])
@login_required
def ai_enhance_text():
    try:
        data = request.get_json()
        operation = data.get("operation")
        text = data.get("text", "")
        prompt = data.get("prompt", "")

        # Skip text validation for 'write' operation since it doesn't need input text
        if not text and operation != "write":
            return jsonify({"error": "No text provided"}), 400

        operations = {
            "write": "Begin by drafting a structured outline, then develop a comprehensive and engaging blog post on the specified topic. Ensure clear paragraph separation with double line breaks and use single line breaks for outline points.",
            "continue": "Add new and original content to the existing text, ensuring it aligns with the current style and tone. Avoid repetition and maintain all formatting and paragraph structure for consistency.",
            "proofread": "Review the text carefully and correct only grammatical and typographical errors. Do not add, remove, or alter the content beyond necessary corrections. Preserve the original formatting, line breaks, and paragraph structure.",
            "rewrite": "Revise the text to enhance clarity and readability while retaining its original meaning. Ensure that all formatting, line breaks, and paragraph structure remain unchanged.",
            "translate": "Convert the text into the target language, ensuring that all formatting, line breaks, and paragraph structure are preserved.",
        }

        if operation not in operations:
            return jsonify({"error": "Invalid operation"}), 400

        # Prepare the prompt based on the operation
        system_message = """You are a professional blogger and editor writing in Norwegian. Please adhere to the following guidelines:
        1. Avoid using quotes around the main content.
        2. Use double line breaks to separate paragraphs.
        3. Retain any existing HTML formatting within the text.
        4. Maintain original line breaks and spacing.
        5. For new content, ensure clear paragraph separation.
        6. Strip out any markdown formatting from the output.
        7. Exclude meta text such as 'Here's the text:' or 'Translation:' from the final output."""

        # For write operation, use the prompt as the text
        if operation == "write":
            full_prompt = operations[operation] + prompt
        elif operation == "translate":
            full_prompt = f"Translate this text to {prompt}, preserving all formatting: \n\n{text}"
        elif prompt:
            full_prompt = f"{prompt}: {text}"
        else:
            full_prompt = operations[operation] + "\n\n" + text

        try:
            client = OpenAI(api_key=current_app.config["OPENAI_API_KEY"])
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": full_prompt},
                ],
                temperature=0.7,
                max_tokens=1500,
                n=1,
            )

            enhanced_text = response.choices[0].message.content.strip()

            # Post-process the text
            enhanced_text = enhanced_text.replace("```", "")  # Remove code blocks
            enhanced_text = enhanced_text.replace('"', "")  # Remove quotes

            # Ensure proper paragraph spacing
            if operation == "write":
                # For new content, ensure paragraphs have proper spacing
                paragraphs = [p.strip() for p in enhanced_text.split("\n\n")]
                enhanced_text = "\n\n".join(p for p in paragraphs if p)
            else:
                # For editing operations, preserve original spacing
                enhanced_text = enhanced_text.replace(
                    "\n\n\n", "\n\n"
                )  # Fix excessive spacing

            return jsonify({"success": True, "text": enhanced_text})

        except (
            OpenAIError,
            APIConnectionError,
            RateLimitError,
            AuthenticationError,
            BadRequestError,
        ) as e:
            current_app.logger.error(f"OpenAI API error: {str(e)}")
            current_app.logger.error(f"Error type: {type(e)}")
            current_app.logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({"error": f"OpenAI API error: {str(e)}"}), 500

    except Exception as e:
        current_app.logger.error(f"Error in AI enhancement: {str(e)}")
        current_app.logger.error(f"Error type: {type(e)}")
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


# Add CORS headers to API responses
@ai_bp.after_request
def after_request(response):
    if request.path.startswith("/api/"):
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add(
            "Access-Control-Allow-Headers", "Content-Type,Authorization"
        )
        response.headers.add(
            "Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS"
        )
    return response