import os
import logging
from datetime import datetime
from flask import Blueprint, send_file, abort, session, request, url_for
from flask_wtf.csrf import CSRFProtect
from modules.models import db, CustomerData

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Create Blueprint
download_bp = Blueprint('download', __name__)

def generate_download_link(flask_session_id=None):
    """
    Generate a secure download link using Flask session ID
    
    Args:
        flask_session_id (str, optional): Flask session ID to use. If None, uses current session.
        
    Returns:
        str: Download link if Flask session ID is available, None otherwise
    """
    try:
        # Use provided Flask session ID or get from current session
        current_flask_session_id = flask_session_id or session.get('session_id')
        if not current_flask_session_id:
            logger.warning("No Flask session ID available for download link generation")
            return None
            
        return url_for('download.download_file', 
                      flask_session_id=current_flask_session_id, 
                      _external=True)
    except Exception as e:
        logger.error(f"Error generating download link: {str(e)}")
        return None

@download_bp.route('/download/<flask_session_id>', methods=['GET'])
def download_file(flask_session_id):
    """
    Secure file download endpoint that verifies Flask session ID
    """
    try:
        # Get current user's Flask session ID
        current_flask_session_id = session.get('session_id')
        if not current_flask_session_id or current_flask_session_id != flask_session_id:
            logger.warning(f"Flask session mismatch. Current: {current_flask_session_id}, Requested: {flask_session_id}")
            abort(403)  # Forbidden

        # Verify customer exists with this Flask session ID
        customer = CustomerData.query.filter_by(flask_session_id=flask_session_id).first()
        if not customer:
            logger.warning(f"No customer found for Flask session ID: {flask_session_id}")
            abort(404)

        # Update download count
        customer.download_count += 1
        db.session.commit()
        
        # Serve the file
        file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                               'assets', 'downloads', os.getenv('DOWNLOAD_FILE_NAME'))
        
        if not os.path.exists(file_path):
            logger.error(f"File not found at path: {file_path}")
            abort(404)
            
        return send_file(file_path, 
                        as_attachment=True,
                        download_name=os.getenv('DOWNLOAD_FILE_NAME'))

    except Exception as e:
        logger.error(f"Error during download: {str(e)}")
        abort(500)

@download_bp.route('/download/code/<download_code>', methods=['GET'])
def download_by_code(download_code):
    """
    Download file using download code (for webhook-processed orders)
    """
    try:
        # Validate download code format
        if not download_code or len(download_code.strip()) == 0:
            logger.warning(f"Empty download code provided")
            abort(404)
        
        # Find customer by download code
        customer = CustomerData.query.filter_by(download_code=download_code).first()
        if not customer:
            logger.warning(f"No customer found for download code: {download_code}")
            abort(404)

        # Update download count
        customer.download_count += 1
        db.session.commit()
        
        # Serve the file
        file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                               'assets', 'downloads', os.getenv('DOWNLOAD_FILE_NAME'))
        
        if not os.path.exists(file_path):
            logger.error(f"File not found at path: {file_path}")
            abort(404)
            
        logger.info(f"File download by code for customer {customer.customer_email}, download count: {customer.download_count}")
        
        return send_file(file_path, 
                        as_attachment=True,
                        download_name=os.getenv('DOWNLOAD_FILE_NAME'))

    except Exception as e:
        logger.error(f"Error during download by code: {str(e)}")
        # Check if it's a database-related error that should return 404
        if "No customer found" in str(e) or "not found" in str(e).lower():
            abort(404)
        else:
            abort(500)

def verify_download_permission(flask_session_id):
    """
    Verify if the session has permission to download
    """
    try:
        customer = CustomerData.query.filter_by(flask_session_id=flask_session_id).first()
        if not customer:
            return False
        return True
    except Exception as e:
        logger.error(f"Error verifying download permission: {str(e)}")
        return False