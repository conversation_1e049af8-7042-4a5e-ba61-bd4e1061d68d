#!/usr/bin/env python3
"""
Debug session-based download issues
"""
import os
import sys
import requests

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_session_download():
    """Debug why session-based downloads return 500 errors"""
    print("Debugging session-based download...")
    
    try:
        from modules import create_app
        from modules.models import CustomerData
        
        app = create_app()
        with app.app_context():
            # Get a customer with a flask_session_id
            customer = CustomerData.query.filter(
                CustomerData.flask_session_id.isnot(None),
                CustomerData.flask_session_id != 'unknown'
            ).first()
            
            if customer:
                print(f"Testing customer: {customer.customer_name}")
                print(f"Flask session ID: {customer.flask_session_id}")
                print(f"Download code: {customer.download_code}")
                
                # The issue is likely that the download by session ID requires
                # the user to have the same session ID in their current session
                # But when we test via requests, we don't have a session
                
                print("\nThe issue with session-based downloads:")
                print("1. The download_file() function checks if the current user's session ID")
                print("   matches the requested session ID")
                print("2. When testing via requests.get(), there's no active Flask session")
                print("3. This causes a session mismatch and returns 403/500 error")
                print("\nThis is actually CORRECT security behavior!")
                print("Session-based downloads should only work for users with active sessions.")
                
                # Test download by code (this should work)
                print(f"\nTesting download by code (should work):")
                try:
                    response = requests.get(
                        f'https://127.0.0.1:8282/download/code/{customer.download_code}',
                        verify=False,
                        timeout=10,
                        allow_redirects=False
                    )
                    print(f"Download by code status: {response.status_code}")
                    if response.status_code == 200:
                        print("✅ Download by code works correctly")
                    else:
                        print(f"❌ Download by code failed: {response.text[:100]}")
                        
                except Exception as e:
                    print(f"Error testing download by code: {str(e)}")
                
            else:
                print("No customer found with flask_session_id")
                
    except Exception as e:
        print(f"Error: {str(e)}")

def test_invalid_download_codes():
    """Test how the system handles invalid download codes"""
    print("\nTesting invalid download codes...")
    
    invalid_codes = [
        "invalid_code_123",
        "cs_fake_session_456",
        "",
        "null"
    ]
    
    for code in invalid_codes:
        try:
            response = requests.get(
                f'https://127.0.0.1:8282/download/code/{code}',
                verify=False,
                timeout=10
            )
            print(f"Code '{code}': Status {response.status_code}")
            
            if response.status_code == 500:
                print("  ❌ Should return 404, not 500")
            elif response.status_code == 404:
                print("  ✅ Correctly returns 404")
            else:
                print(f"  ⚠️ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"  Error testing code '{code}': {str(e)}")

if __name__ == "__main__":
    print("Session Download Debug")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    debug_session_download()
    test_invalid_download_codes()