from flask import render_template, request, redirect, url_for, send_from_directory
from .forms import NewsletterSubscribeForm
import os

def init_routes(app):

    @app.route('/favicon.ico')
    def favicon():
        return send_from_directory(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'favicon.ico', mimetype='image/x-icon')

    @app.route('/robots.txt')
    def robots():
        return send_from_directory(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'robots.txt', mimetype='text/plain')

    @app.route('/')
    @app.route('/index.html')
    def index():
        form = NewsletterSubscribeForm()
        return render_template('index.html', form=form)

    @app.route('/gdpr.html')
    def gdpr():
        return render_template('gdpr.html')

    @app.route('/handlekurv.html')
    def handlekurv():
        return render_template('handlekurv.html')

    @app.route('/kostplan.html')
    def kostplan():
        return render_template('kostplan.html')

    @app.route('/om.html')
    def om():
        return render_template('om.html')

    @app.route('/vilkar.html')
    def vilkar():
        return render_template('vilkar.html')

    @app.route('/omtaler.html')
    def omtaler():
        return render_template('omtaler.html')

    @app.route('/bot-detection-test.html')
    def bot_detection_test():
        return render_template('bot-detection-test.html')

    @app.errorhandler(404)
    def not_found(error):
        return render_template('404.html'), 404
