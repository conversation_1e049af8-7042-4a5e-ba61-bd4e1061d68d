#!/usr/bin/env python3
"""
Test script to verify the analytics integration with updated function
"""

import sys
import os
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.analytics_process import get_analytics_data
from flask import Flask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    from modules.models import db
    db.init_app(app)
    
    return app

def test_analytics_integration(target_date_str):
    """
    Test the analytics integration with the updated function
    """
    try:
        print(f"Testing analytics integration for {target_date_str}")
        
        # Get analytics data using the main function
        data = get_analytics_data(target_date_str, target_date_str)
        
        if isinstance(data, tuple):  # Error case
            print(f"Error getting analytics data: {data}")
            return None
        
        # Extract key metrics
        overview = data.get('overview', {})
        unique_visitors = overview.get('unique_visitors', 0)
        total_pageviews = overview.get('total_pageviews', 0)
        pageviews_per_session = overview.get('pageviews_per_session', 0)
        
        print(f"\n=== ANALYTICS RESULTS ===")
        print(f"Total pageviews: {total_pageviews}")
        print(f"Unique visitors: {unique_visitors}")
        print(f"Pageviews per session: {pageviews_per_session}")
        
        # Check if the results make sense
        if unique_visitors == 100:  # Expected for 2025-06-16
            print(f"✅ Unique visitors count matches expected conservative approach!")
        else:
            print(f"⚠️  Unique visitors count ({unique_visitors}) doesn't match expected (100)")
        
        if total_pageviews > 0 and unique_visitors > 0:
            calculated_pages_per_session = round(total_pageviews / unique_visitors, 2)
            print(f"Calculated pages per session: {calculated_pages_per_session}")
            
            if abs(calculated_pages_per_session - pageviews_per_session) < 0.01:
                print(f"✅ Pages per session calculation is correct!")
            else:
                print(f"⚠️  Pages per session calculation might be off")
        
        return data
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python test_analytics_integration.py YYYY-MM-DD")
        print("Example: python test_analytics_integration.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run test
    app = create_app()
    
    with app.app_context():
        results = test_analytics_integration(target_date)
        
        if results is None:
            print("Failed to test analytics integration.")
            sys.exit(1)
        else:
            print(f"\n✅ Analytics integration test completed successfully!")
            print(f"The updated get_robust_unique_visitor_count function is working properly.")