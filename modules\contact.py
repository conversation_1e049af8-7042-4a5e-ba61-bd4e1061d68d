import os
import json
import logging
import traceback
import requests
from flask import request, flash, redirect, url_for, render_template
from flask_mail import Message
from .security import (
    validate_altcha,
    generate_altcha_challenge, 
    get_hmac_key, 
    is_potential_spam,
    sanitize_input,
    validate_email
)
from .mail import send_contact_form_email
from .forms import ContactForm

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContactFormHandler:
    """
    Handles processing and validation of contact form submissions
    """
    def __init__(self, request, mail):
        """
        Initialize the contact form handler
        
        Args:
            request (Request): Flask request object
            mail (Mail): Flask-Mail instance
        """
        self.request = request
        self.mail = mail
        self.logger = logging.getLogger(__name__)
        self.spam_threshold = 99
        self.form = ContactForm(request.form)

    def validate_csrf(self):
        """
        Validate CSRF token using Flask-WTF
        
        Returns:
            bool: True if CSRF token is valid, False otherwise
        """
        return self.form.validate()  # Flask-WTF form validation includes CSRF check

    def check_spam(self):
        """
        Perform spam checks
        
        Returns:
            tuple: (is_spam, client_ip, abuse_confidence_score)
        """
        client_ip = self.request.headers.get("X-Forwarded-For", self.request.remote_addr)
        
        # Check IP reputation
        is_spam, abuse_confidence_score, _ = is_potential_spam(client_ip, self.spam_threshold)
        
        # Additional spam check with honeypot
        checkbox = self.request.form.get("checkbox", "off")
        
        self.logger.info(f"IP: {client_ip}, Spam Check - Is Spam: {is_spam}, Confidence Score: {abuse_confidence_score}")
        
        return is_spam or checkbox == "on", client_ip, abuse_confidence_score

    def validate_altcha(self):
        """
        Validate ALTCHA payload
        
        Returns:
            tuple: (is_valid, payload_details)
        """
        payload = self.request.form.get("altcha")
        
        try:
            is_valid, error_message, payload_details = validate_altcha_payload(payload)
            
            if not is_valid:
                self.logger.error(f"ALTCHA Validation Error: {error_message}")
                return False, None
            
            # Log ALTCHA details
            challenge = payload_details.get('challenge', 'N/A')
            salt = payload_details.get('salt', 'N/A')
            took = payload_details.get('took', 'N/A')
            self.logger.info(f"ALTCHA validation successful. Challenge: {challenge}, Salt: {salt}, Took: {took}")
            
            return True, payload_details
        
        except Exception as e:
            self.logger.error(f"Unexpected error during ALTCHA validation: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False, None

    def validate_form_data(self):
        """
        Validate form data and sanitize inputs
        
        Returns:
            tuple: (is_valid, error_message, sanitized_data)
        """
        if not self.form.validate():
            return False, "Alle felt må fylles ut.", None

        # Get and sanitize form data
        name = sanitize_input(
            self.form.name.data,
            input_type=str,
            max_length=100,
            allow_html=False  # Explicitly deny HTML for name
        )
        email = validate_email(self.form.email.data)
        message = sanitize_input(
            self.form.message.data, 
            input_type=str,
            max_length=2000,
            allow_html=True,  # Allow HTML formatting in messages
            tags=['p', 'br', 'b', 'i', 'u', 'em', 'strong', 'ul', 'ol', 'li']  # Extended set of safe HTML tags
        )
        
        # Get raw checkbox value - this is an anti-bot measure, it should be unchecked
        checkbox_value = self.form.checkbox.data
        # If the checkbox is checked, it might be a bot
        if checkbox_value is not None and checkbox_value.lower() in ('on', 'true', '1', 'yes'):
            self.logger.warning(f"Potential bot detected - checkbox was checked")
            return False, "Ugyldig skjemainnsending.", None

        # Return sanitized data
        return True, "", {
            "name": name,
            "email": email,
            "message": message
        }

    def send_email(self, sanitized_data):
        """
        Send contact form email
        
        Args:
            sanitized_data (dict): Sanitized form data
        
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            send_contact_form_email(
                self.mail, 
                sanitized_data["name"], 
                sanitized_data["email"], 
                sanitized_data["message"], 
                client_ip=self.request.headers.get("X-Forwarded-For", self.request.remote_addr), 
                abuse_confidence_score=0, 
                checkbox=sanitized_data.get("checkbox", "off")
            )
            return True
        except Exception as e:
            self.logger.error(f"Unexpected error sending email: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    def process_form(self):
        """
        Process the contact form submission
        """
        try:
            # Validate CSRF token
            if not self.validate_csrf():
                flash("Ugyldig CSRF-token. Vennligst prøv igjen.", "error")
                return redirect(url_for('contact'))

            # Validate ALTCHA
            payload = sanitize_input(self.request.form.get("altcha"))
            if not payload:
                flash("ALTCHA-validering mislyktes.", "error")
                return redirect(url_for('contact'))

            is_valid, error_msg = validate_altcha(payload)
            if not is_valid:
                flash(f"ALTCHA-validering mislyktes: {error_msg}", "error")
                return redirect(url_for('contact'))

            # Validate and sanitize form data
            is_valid, error_msg, sanitized_data = self.validate_form_data()
            if not is_valid:
                flash(error_msg, "error")
                return redirect(url_for('contact'))

            # Check for spam
            is_spam, client_ip, abuse_score = self.check_spam()
            if is_spam:
                self.logger.warning(f"Spam detected from IP {client_ip} with score {abuse_score}")
                flash("Meldingen ble identifisert som spam.", "error")
                return redirect(url_for('contact'))

            # Send email with sanitized data
            self.send_email(sanitized_data)
            
            flash("Takk for din henvendelse! Vi vil svare så snart som mulig.", "success")
            return redirect(url_for('contact'))

        except Exception as e:
            self.logger.error(f"Error processing contact form: {str(e)}")
            self.logger.error(traceback.format_exc())
            flash("En feil oppstod. Vennligst prøv igjen senere.", "error")
            return redirect(url_for('contact'))

def init_contact_routes(app, mail):
    """
    Initialize contact-related routes for the Flask application
    
    Args:
        app (Flask): The Flask application instance
        mail: Flask-Mail instance
    """
    @app.route('/kontakt.html', methods=['GET'])
    def kontakt():
        """
        Render the contact page with an ALTCHA challenge and contact form
        
        Returns:
            Rendered contact page template
        """
        challenge = generate_altcha_challenge()
        form = ContactForm()
        return render_template('kontakt.html', challenge=challenge, form=form)

    @app.route("/kontakt.html", methods=["POST"])
    def contact():
        """
        Process contact form submission
        
        Returns:
            Redirect to contact page with success or error message
        """
        handler = ContactFormHandler(request, mail)
        return handler.process_form()