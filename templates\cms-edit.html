{% extends "cms-base.html" %}

{% block title %}Rediger innlegg{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="/assets/ckeditor5/ckeditor5.css">
<link rel="stylesheet" href="/assets/ckeditor5/ckeditor5-editor.css">
<link rel="stylesheet" href="/assets/ckeditor5/ckeditor5-content.css">
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block content %}
<div class="container py-5">
    <h1 class="mb-4">{% if post %}Rediger Innhold{% else %}Nytt Innhold{% endif %}</h1>
    
    <form method="POST" enctype="multipart/form-data">
        {{ form.csrf_token }}
        
        <div class="mb-3">
            {{ form.title.label(class="form-label") }}
            {{ form.title(class="form-control") }}
            {% if form.title.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.title.errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <div class="mb-3">
            {{ form.description.label(class="form-label") }}
            {{ form.description(class="form-control", rows=3) }}
        </div>

        <div class="mb-3">
            {{ form.post_type.label(class="form-label") }}
            {{ form.post_type(class="form-select") }}
        </div>

        <div class="mb-3">
            {{ form.main_content.label(class="form-label") }}
            <div class="ai-toolbar">
                <div class="btn-group" role="group" aria-label="AI Assistanse">
                    <button type="button" class="btn ai-action" data-operation="write">
                        <i class="bi bi-pencil-square me-1"></i>Skriv Nytt
                    </button>
                    <button type="button" class="btn ai-action" data-operation="continue">
                        <i class="bi bi-arrow-right-square me-1"></i>Fortsett
                    </button>
                    <button type="button" class="btn ai-action, data-operation="proofread">
                        <i class="bi bi-check-square me-1"></i>Korrektur
                    </button>
                    <button type="button" class="btn ai-action" data-operation="rewrite">
                        <i class="bi bi-arrow-repeat me-1"></i>Omskriv
                    </button>
                    <button type="button" class="btn ai-action" data-operation="translate">
                        <i class="bi bi-translate me-1"></i>Oversett
                    </button>
                    <button type="button" class="btn" id="aiUndo" style="display: none;">
                        <i class="bi bi-arrow-counterclockwise me-1"></i>Angre
                    </button>
                </div>
                <div id="ai-spinner" class="d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Laster...</span>
                    </div>
                    <span class="ms-2">AI Prosesserer...</span>
                </div>
            </div>
            {{ form.main_content(class="form-control ckeditor-enable", rows="10") }}
            {% if form.main_content.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.main_content.errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <div class="mb-3">
            {{ form.tags.label(class="form-label") }}
            {{ form.tags(class="form-control") }}
            <div class="form-text">Skill tags med komma</div>
        </div>

        <div class="mb-3">
            {{ form.featured_image.label(class="form-label") }}
            {{ form.featured_image(class="form-control") }}
            {% if post and post.featured_image %}
            <div class="mt-2">
                <img src="{{ post.featured_image }}" alt="Current featured image" class="img-thumbnail">
            </div>
            {% endif %}
        </div>

        <div id="recipe-fields" style="display: none;">
            <div class="border rounded p-3 mb-3">
                <h5 class="mb-3">Oppskrift Detaljer</h5>
                
                <div class="row mb-3">
                    <div class="col-md-3">
                        {{ form.prep_time.label(class="form-label") }}
                        {{ form.prep_time(class="form-control") }}
                    </div>
                    <div class="col-md-3">
                        {{ form.cook_time.label(class="form-label") }}
                        {{ form.cook_time(class="form-control") }}
                    </div>
                    <div class="col-md-3">
                        {{ form.servings.label(class="form-label") }}
                        {{ form.servings(class="form-control") }}
                    </div>
                    <div class="col-md-3">
                        {{ form.difficulty.label(class="form-label") }}
                        {{ form.difficulty(class="form-select") }}
                    </div>
                </div>

                <div class="mb-3">
                    {{ form.category.label(class="form-label") }}
                    {{ form.category(class="form-select") }}
                </div>

                <div class="mb-3">
                    {{ form.dish.label(class="form-label") }}
                    {{ form.dish(class="form-control") }}
                </div>

                <div class="mb-3">
                    <h6>Ingredienser</h6>
                    <input type="hidden" name="ingredients_json" id="ingredients-data" value="{{ form.ingredients_json.data or '[]' }}">
                    <div id="ingredients-list" class="mb-2"></div>
                    <button type="button" class="btn btn-outline-secondary" id="add-ingredient">
                        Legg til ingrediens
                    </button>
                </div>

                <div class="mb-3">
                    {{ form.instructions.label(class="form-label") }}
                    {{ form.instructions(class="form-control ckeditor-enable", rows=5) }}
                </div>

                <div class="mb-3">
                    {{ form.tips.label(class="form-label") }}
                    {{ form.tips(class="form-control", rows=3) }}
                </div>
            </div>
        </div>

        <div class="mb-3 form-check">
            {{ form.draft(class="form-check-input") }}
            {{ form.draft.label(class="form-check-label") }}
        </div>

        <div class="mt-4">
            <button type="submit" class="btn btn-primary">Lagre</button>
            <a href="/cms-drafts.html" class="btn btn-secondary">Avbryt</a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}

<script type="module">
import { 
    ClassicEditor,
    Alignment,
    AutoImage,
    AutoLink,
    Autosave,
    Bold,
    Essentials,
    FindAndReplace,
    GeneralHtmlSupport,
    Heading,
    HtmlComment,
    HtmlEmbed,
    ImageBlock,
    ImageCaption,
    ImageInline,
    ImageInsert,
    ImageInsertViaUrl,
    ImageResize,
    ImageStyle,
    ImageTextAlternative,
    ImageToolbar,
    ImageUpload,
    Indent,
    IndentBlock,
    Italic,
    Link,
    LinkImage,
    List,
    ListProperties,
    MediaEmbed,
    Paragraph,
    PasteFromOffice,
    RemoveFormat,
    SimpleUploadAdapter,
    SourceEditing,
    SpecialCharacters,
    SpecialCharactersArrows,
    SpecialCharactersCurrency,
    SpecialCharactersEssentials,
    SpecialCharactersLatin,
    SpecialCharactersMathematical,
    SpecialCharactersText,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TodoList,
    Underline
} from 'ckeditor5';

document.addEventListener('DOMContentLoaded', async function() {
    const editors = document.querySelectorAll('textarea.ckeditor-enable');
    
    for (const textarea of editors) {
        try {
            const editorConfig = {
                licenseKey: 'GPL',
                simpleUpload: {
                    uploadUrl: '/upload-image',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    onError: function(error) {
                        console.error('CKEditor Upload Error:', error);
                    },
                    errorTranslator: function(error) {
                        return error.message || 'Upload failed';
                    }
                },
                plugins: [
                    Alignment,
                    AutoImage,
                    AutoLink,
                    Autosave,
                    Bold,
                    Essentials,
                    FindAndReplace,
                    GeneralHtmlSupport,
                    Heading,
                    HtmlComment,
                    HtmlEmbed,
                    ImageBlock,
                    ImageCaption,
                    ImageInline,
                    ImageInsert,
                    ImageInsertViaUrl,
                    ImageResize,
                    ImageStyle,
                    ImageTextAlternative,
                    ImageToolbar,
                    ImageUpload,
                    Indent,
                    IndentBlock,
                    Italic,
                    Link,
                    LinkImage,
                    List,
                    ListProperties,
                    MediaEmbed,
                    Paragraph,
                    PasteFromOffice,
                    RemoveFormat,
                    SimpleUploadAdapter,
                    SourceEditing,
                    SpecialCharacters,
                    SpecialCharactersArrows,
                    SpecialCharactersCurrency,
                    SpecialCharactersEssentials,
                    SpecialCharactersLatin,
                    SpecialCharactersMathematical,
                    SpecialCharactersText,
                    Table,
                    TableCaption,
                    TableCellProperties,
                    TableColumnResize,
                    TableProperties,
                    TableToolbar,
                    TodoList,
                    Underline
                ],
                toolbar: {
                    items: [
                        'sourceEditing',
                        '|',
                        'heading',
                        '|',
                        'bold',
                        'italic',
                        'underline',
                        '|',
                        'link',
                        'insertImage',
                        'mediaEmbed',
                        'insertTable',
                        '|',
                        'alignment',
                        '|',
                        'bulletedList',
                        'numberedList',
                        'todoList',
                        'outdent',
                        'indent'
                    ],
                    shouldNotGroupWhenFull: true
                },
                language: 'nb',
                image: {
                    toolbar: [
                        'toggleImageCaption',
                        'imageTextAlternative',
                        '|',
                        'imageStyle:inline',
                        'imageStyle:wrapText',
                        'imageStyle:breakText',
                        '|',
                        'resizeImage'
                    ]
                },
                table: {
                    contentToolbar: [
                        'tableColumn',
                        'tableRow',
                        'mergeTableCells',
                        'tableCellProperties',
                        'tableProperties'
                    ]
                },
                mediaEmbed: {
                    previewsInData: true,
                    providers: (() => {
                        const createVideoWrapper = (content) => 
                            `<div class="video-wrapper">${content}</div>`;
                            
                        return [
                            {
                                name: 'optimal-ketose',
                                url: /^https?:\/\/(?:www\.)?optimal-ketose\.com\/.+/,
                                html: match => createVideoWrapper(
                                    `<iframe src="${match[0]}" frameborder="0" allowfullscreen></iframe>`
                                )
                            },
                            {
                                name: 'local-video',
                                url: /^(?:\/assets\/videos\/|http:\/\/127\.0\.0\.1\/).*\.(mp4|webm|ogg)$/,
                                html: match => {
                                    const videoUrl = match[0];
                                    const type = videoUrl.endsWith('.mp4') ? 'mp4' : 
                                                videoUrl.endsWith('.webm') ? 'webm' :
                                                videoUrl.endsWith('.ogg') ? 'ogg' : 'mp4';
                                   return createVideoWrapper(`
                                        <video 
                                            controls 
                                            autoplay
                                            playsinline
                                            preload="auto"
                                            style="width:100%"
                                            muted
                                            loop
                                        >
                                            <source src="${videoUrl}" type="video/${type}">
                                            Your browser does not support the video tag.
                                        </video>
                                    `);
                                }
                            },
                            {
                                name: 'localhost',
                                url: /^http:\/\/127\.0\.0\.1\/.+/,
                                html: match => createVideoWrapper(
                                    `<iframe src="${match[0]}" frameborder="0" allowfullscreen></iframe>`
                                )
                            }
                        ];
                    })()
                },
                htmlSupport: {
                    allow: [
                        {
                            name: /^.*$/,
                            styles: true,
                            attributes: true,
                            classes: true
                        }
                    ]
                }
            };
            
            ClassicEditor
                .create(textarea, editorConfig)
                .then(editor => {
                    // Store the editor instance on the textarea for later access
                    textarea.ckeditorInstance = editor;
                    
                    // If this is the main_content or instructions textarea, do additional setup
                    if (textarea.classList.contains('ckeditor-enable')) {
                        if (textarea.id === 'main_content') {
                            // Existing main_content specific code
                        } else if (textarea.id === 'instructions') {
                            // New instructions specific code
                        }
                    }
                })
                .catch(error => {
                    console.error('Error creating editor:', error);
                });
        } catch (error) {
            console.error('Error initializing editor:', error);
        }
    }
});
</script>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // Handle recipe fields visibility
    const postTypeSelect = document.getElementById('post_type');
    const recipeFields = document.getElementById('recipe-fields');
    const ingredientsData = document.getElementById('ingredients-data');
    const ingredientsList = document.getElementById('ingredients-list');
    const addIngredientBtn = document.getElementById('add-ingredient');
    const form = document.querySelector('form');
    let ingredients = [];

    function toggleRecipeFields() {
        if (postTypeSelect.value === 'oppskrift') {
            recipeFields.style.display = 'block';
        } else {
            recipeFields.style.display = 'none';
        }
    }
    
    // Set initial state
    toggleRecipeFields();
    
    // Add event listener for changes
    postTypeSelect.addEventListener('change', toggleRecipeFields);

    // Initialize ingredients array with existing data if editing a post
    if (ingredientsData.value) {
        try {
            ingredients = JSON.parse(ingredientsData.value);
        } catch (e) {
            console.error('Error parsing ingredients data:', e);
            ingredients = [];
        }
    }
    
    function createIngredientInput(ingredient = { name: '', quantity: '', unit: '' }) {
        const div = document.createElement('div');
        div.className = 'ingredient-item d-flex gap-2 mb-2 align-items-center';
        
        const nameInput = document.createElement('input');
        nameInput.type = 'text';
        nameInput.className = 'form-control ingredient-name';
        nameInput.placeholder = 'Ingrediens';
        nameInput.value = ingredient.name;
        
        const quantityInput = document.createElement('input');
        quantityInput.type = 'text';
        quantityInput.className = 'form-control ingredient-quantity';
        quantityInput.placeholder = 'Mengde';
        quantityInput.value = ingredient.quantity;
        
        const unitInput = document.createElement('input');
        unitInput.type = 'text';
        unitInput.className = 'form-control ingredient-unit';
        unitInput.placeholder = 'Enhet';
        unitInput.value = ingredient.unit;
        
        const deleteBtn = document.createElement('button');
        deleteBtn.type = 'button';
        deleteBtn.className = 'btn btn-outline-danger';
        deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
        
        // Add input event listeners to all input fields
        [nameInput, quantityInput, unitInput].forEach(input => {
            input.addEventListener('input', updateIngredientsData);
        });
        
        // Add delete button handler
        deleteBtn.addEventListener('click', () => {
            div.remove();
            updateIngredientsData();
        });
        
        div.appendChild(nameInput);
        div.appendChild(quantityInput);
        div.appendChild(unitInput);
        div.appendChild(deleteBtn);
        
        return div;
    }

    function renderIngredients() {
        ingredientsList.innerHTML = '';
        if (ingredients && ingredients.length > 0) {
            ingredients.forEach(ingredient => {
                ingredientsList.appendChild(createIngredientInput(ingredient));
            });
        }
        updateIngredientsData();
    }

    function updateIngredientsData() {
        const items = ingredientsList.querySelectorAll('.ingredient-item');
        ingredients = Array.from(items).map(item => ({
            name: item.querySelector('.ingredient-name').value,
            quantity: item.querySelector('.ingredient-quantity').value,
            unit: item.querySelector('.ingredient-unit').value
        }));
        ingredientsData.value = JSON.stringify(ingredients);
    }

    addIngredientBtn.addEventListener('click', () => {
        const newIngredient = createIngredientInput();
        ingredientsList.appendChild(newIngredient);
        updateIngredientsData();
    });

    // Initial render of existing ingredients
    renderIngredients();

    // AI Enhancement Functions
    let lastContent = null;
    let editor = null;
    const aiSpinner = document.getElementById('ai-spinner');
    const aiUndo = document.getElementById('aiUndo');

    function startAIProcessing() {
        if (!aiSpinner.classList.contains('d-none')) return;
        aiSpinner.classList.remove('d-none');
    }

    function endAIProcessing() {
        if (aiSpinner.classList.contains('d-none')) return;
        aiSpinner.classList.add('d-none');
    }

    async function enhanceText(operation, text, prompt = '') {
        startAIProcessing();
        try {
            const response = await fetch('/api/ai/enhance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                },
                body: JSON.stringify({ operation, text, prompt })
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || 'Failed to enhance text');
            }
            if (!data.success || !data.text) {
                throw new Error('Invalid response from server');
            }
            return data.text;
        } catch (error) {
            console.error('AI Enhancement failed:', error);
            alert('Beklager, det oppstod en feil under AI-prosessering: ' + error.message);
            throw error;
        } finally {
            endAIProcessing();
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AI action handlers after CKEditor is ready
        const initAIHandlers = (editorInstance) => {
            editor = editorInstance;
            
            document.querySelectorAll('.ai-action').forEach(action => {
                action.addEventListener('click', async (e) => {
                    e.preventDefault();
                    const operation = e.target.closest('.ai-action').dataset.operation;
                    let prompt = '';
                    let text = '';

                    // Store current content for undo
                    lastContent = editor.getData();
                    aiUndo.style.display = 'inline-block';

                    // Get selected text or all content
                    const selection = editor.model.document.selection;
                    if (!selection.isCollapsed) {
                        const range = selection.getFirstRange();
                        text = Array.from(range.getItems())
                            .map(item => item.data || '')
                            .join('');
                    } else {
                        text = editor.getData();
                    }

                    // Handle special cases
                    if (operation === 'write') {
                        prompt = window.prompt('Hva vil du skrive om?');
                        if (!prompt) return;
                    } else if (operation === 'translate') {
                        prompt = window.prompt('Hvilket språk vil du oversette til?');
                        if (!prompt) return;
                    }

                    try {
                        const enhancedText = await enhanceText(operation, text, prompt);

                        if (operation === 'continue') {
                            // Append new content
                            editor.setData(editor.getData() + '\n\n' + enhancedText);
                        } else if (!selection.isCollapsed) {
                            // Replace selected content
                            editor.model.change(writer => {
                                const range = selection.getFirstRange();
                                editor.model.deleteContent(selection);
                                const insertPosition = range.start;
                                editor.model.insertContent(
                                    writer.createText(enhancedText),
                                    insertPosition
                                );
                            });
                        } else {
                            // Replace all content
                            editor.setData(enhancedText);
                        }
                    } catch (error) {
                        console.error('Failed to process text:', error);
                    }
                });
            });

            // Handle undo
            aiUndo.addEventListener('click', () => {
                if (lastContent !== null) {
                    editor.setData(lastContent);
                    lastContent = null;
                    aiUndo.style.display = 'none';
                }
            });
        };

        // Wait for CKEditor to be initialized
        const checkEditor = setInterval(() => {
            const editorInstance = document.querySelector('.ck-editor__editable').ckeditorInstance;
            if (editorInstance) {
                clearInterval(checkEditor);
                initAIHandlers(editorInstance);
            }
        }, 100);
    });
</script>
{% endblock %}
