#!/usr/bin/env python3
"""
Test script to verify the MIN(unique_ips+date, unique_sessions) approach
"""

import sys
import os
from datetime import datetime
from sqlalchemy import func

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.models import db, Analytics
from modules.analytics_process import get_robust_unique_visitor_count
from flask import Flask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    db.init_app(app)
    
    return app

def test_min_approach(target_date_str):
    """
    Test the MIN(unique_ips+date, unique_sessions) approach
    """
    try:
        # Parse the target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d')
        
        # Set start and end of the day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        print(f"Testing MIN approach for {target_date_str}")
        print(f"Date range: {start_date} to {end_date}")
        
        filters = [
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ]
        
        # Test the new get_robust_unique_visitor_count function with MIN logic
        robust_count = get_robust_unique_visitor_count(
            include_date_in_key=True, 
            db_session=db.session, 
            filters=filters
        )
        
        # Get individual counts for comparison
        unique_ips_with_date = db.session.query(
            func.count(func.distinct(
                Analytics.ip_address + '|' + func.date(Analytics.timestamp).cast(db.String)
            ))
        ).filter(*filters).scalar()
        
        unique_sessions = db.session.query(
            func.count(Analytics.sessionid.distinct())
        ).filter(*filters).scalar()
        
        # Also test without date in key
        robust_count_no_date = get_robust_unique_visitor_count(
            include_date_in_key=False, 
            db_session=db.session, 
            filters=filters
        )
        
        unique_ips_only = db.session.query(
            func.count(Analytics.ip_address.distinct())
        ).filter(*filters).scalar()
        
        print(f"\n=== WITH DATE IN KEY (include_date_in_key=True) ===")
        print(f"Unique IP+date combinations: {unique_ips_with_date}")
        print(f"Unique sessions: {unique_sessions}")
        print(f"MIN(IP+date, sessions): {min(unique_ips_with_date, unique_sessions)}")
        print(f"get_robust_unique_visitor_count result: {robust_count}")
        print(f"✓ Correct: {robust_count == min(unique_ips_with_date, unique_sessions)}")
        
        print(f"\n=== WITHOUT DATE IN KEY (include_date_in_key=False) ===")
        print(f"Unique IPs only: {unique_ips_only}")
        print(f"Unique sessions: {unique_sessions}")
        print(f"MIN(IPs, sessions): {min(unique_ips_only, unique_sessions)}")
        print(f"get_robust_unique_visitor_count result: {robust_count_no_date}")
        print(f"✓ Correct: {robust_count_no_date == min(unique_ips_only, unique_sessions)}")
        
        # Test SQLAlchemy expression mode (without db_session)
        print(f"\n=== SQLALCHEMY EXPRESSION MODE ===")
        expr_result = db.session.query(
            get_robust_unique_visitor_count(include_date_in_key=True)
        ).filter(*filters).scalar()
        
        print(f"Expression mode result: {expr_result}")
        print(f"Should equal IP+date count: {expr_result == unique_ips_with_date}")
        
        print(f"\n=== FINAL ANSWER ===")
        print(f"Conservative unique visitors for {target_date_str}: {robust_count}")
        
        if robust_count == min(unique_ips_with_date, unique_sessions):
            print(f"✅ MIN approach working correctly!")
            if unique_ips_with_date < unique_sessions:
                print(f"→ Using IP+date count ({unique_ips_with_date}) as it's lower")
            else:
                print(f"→ Using session count ({unique_sessions}) as it's lower")
        else:
            print(f"❌ Something is wrong with the MIN logic")
        
        return {
            'unique_ips_with_date': unique_ips_with_date,
            'unique_sessions': unique_sessions,
            'robust_count': robust_count,
            'expected_min': min(unique_ips_with_date, unique_sessions)
        }
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python test_min_approach.py YYYY-MM-DD")
        print("Example: python test_min_approach.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run test
    app = create_app()
    
    with app.app_context():
        results = test_min_approach(target_date)
        
        if results is None:
            print("Failed to test MIN approach.")
            sys.exit(1)