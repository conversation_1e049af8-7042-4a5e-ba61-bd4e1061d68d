#!/usr/bin/env python3
"""
Test webhook with proper Stripe signature using Stripe library
"""
import os
import sys
import json
import requests
import stripe
import time
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_webhook_with_stripe_signature():
    """Test webhook using <PERSON><PERSON>'s own signature generation"""
    print("Testing webhook with Stripe library signature...")
    
    # Get the endpoint secret
    endpoint_secret = os.getenv('STRIPE_ENDPOINT_SECRET')
    if not endpoint_secret:
        print("❌ STRIPE_ENDPOINT_SECRET not found")
        return False
    
    print(f"Using endpoint secret: {endpoint_secret[:10]}...")
    
    # Test payload
    test_payload = {
        "id": "evt_test_stripe_sig",
        "object": "event",
        "api_version": "2020-08-27",
        "created": int(datetime.now().timestamp()),
        "data": {
            "object": {
                "id": "cs_test_stripe_sig",
                "object": "checkout.session",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Stripe Test Customer"
                }
            }
        },
        "livemode": False,
        "pending_webhooks": 1,
        "request": {
            "id": None,
            "idempotency_key": None
        },
        "type": "checkout.session.completed"
    }
    
    # Convert to JSON string
    payload_json = json.dumps(test_payload, separators=(',', ':'))
    
    # Generate timestamp
    timestamp = str(int(time.time()))
    
    # Create the signature manually using the same method as Stripe
    import hmac
    import hashlib
    
    # Remove whsec_ prefix if present
    secret = endpoint_secret
    if secret.startswith('whsec_'):
        secret = secret[6:]
    
    # Create signed payload
    signed_payload = f"{timestamp}.{payload_json}"
    
    # Generate signature
    signature = hmac.new(
        secret.encode('utf-8'),
        signed_payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    # Format signature header
    sig_header = f"t={timestamp},v1={signature}"
    
    print(f"Payload size: {len(payload_json)} bytes")
    print(f"Timestamp: {timestamp}")
    print(f"Signature header: {sig_header[:50]}...")
    
    try:
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=payload_json,  # Send as raw data
            headers={
                'Content-Type': 'application/json',
                'stripe-signature': sig_header
            },
            timeout=10,
            verify=False
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_webhook_no_signature():
    """Test webhook by temporarily disabling signature verification"""
    print("\nTesting webhook with signature verification disabled...")
    
    # We'll modify the webhook handler to skip signature verification
    # by temporarily setting endpoint_secret to None
    
    test_payload = {
        "id": "evt_test_no_sig",
        "object": "event",
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": "cs_test_no_sig",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "No Sig Customer"
                }
            }
        }
    }
    
    payload_json = json.dumps(test_payload, separators=(',', ':'))
    
    try:
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=payload_json,
            headers={'Content-Type': 'application/json'},
            timeout=10,
            verify=False
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Stripe Signature Test")
    print("=" * 40)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test with proper signature
    sig_test = test_webhook_with_stripe_signature()
    
    # Test without signature
    no_sig_test = test_webhook_no_signature()
    
    if sig_test or no_sig_test:
        print("\n✅ At least one test passed!")
        if sig_test:
            print("✅ Signature verification is working")
        if no_sig_test:
            print("✅ Basic webhook functionality is working")
    else:
        print("\n❌ All tests failed")
        print("The webhook handler might have an issue or the server might not be processing requests correctly")