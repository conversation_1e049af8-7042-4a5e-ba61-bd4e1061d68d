document.addEventListener('DOMContentLoaded', () => {
    // Update placeholder with rendered dimensions
    const updatePlaceholder = (img) => {
        const width = img.clientWidth || img.parentElement.clientWidth;
        const height = img.clientHeight || img.parentElement.clientHeight;
        if (!width || !height) return;

        // Get current placeholder URL and add dimensions
        const currentSrc = img.src;
        if (currentSrc.includes('/api/placeholder/')) {
            const imageName = currentSrc.split('/').pop();
            img.src = `/api/placeholder/${imageName}?width=${width}&height=${height}`;
        }
    };

    const loadOptimizedImage = (img) => {
        if (img.hasAttribute('data-optimized')) return;
        
        const getStableWidth = () => {
            // Priority to rendered dimensions
            let width = img.clientWidth;
            
            // CMS-post fallback calculation
            if (!width && img.closest('.cms-post-content')) {
                width = img.parentElement.offsetWidth * 0.95 || 800;
            }
            
            return width || 800; // Final fallback
        };

        const width = getStableWidth();
        
        // Get the original src from data attribute
        const originalSrc = img.getAttribute('data-src');
        
        // Skip SVG files
        if (originalSrc && originalSrc.toLowerCase().endsWith('.svg')) {
            img.setAttribute('data-optimized', 'true');
            return;
        }
        
        // Get image name from original source
        if (originalSrc) {
            const imageName = originalSrc.split('/').pop();
            const optimizedSrc = `/api/optimize_image?file_name=${encodeURIComponent(imageName)}&width=${width}&quality=90&aspect_ratio=true`;
            
            // Load optimized version
            const loadingImage = new Image();
            loadingImage.onload = () => {
                img.src = optimizedSrc;
                img.setAttribute('data-optimized', 'true');
                img.style.opacity = '1';
            };
            
            loadingImage.onerror = () => {
                console.error(`Failed to load optimized image: ${optimizedSrc}`);
                img.style.opacity = '1';
                img.setAttribute('data-optimized', 'true');
            };
            
            loadingImage.src = optimizedSrc;
        }
    };

    // Set up Intersection Observer for lazy loading
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                requestAnimationFrame(() => {
                    // CMS-post specific handling
                    if (img.closest('.cms-post-content')) {
                        // Add smooth transition and ensure layout stability
                        img.style.transition = 'opacity 0.3s ease';
                        setTimeout(() => loadOptimizedImage(img), 30);
                    } else {
                        loadOptimizedImage(img);
                    }
                    observer.unobserve(img);
                });
            }
        });
    }, {
        rootMargin: '500px 0px',
        threshold: 0.01
    });

    // Auto-detect new images added to DOM
    new MutationObserver((mutations) => {
        mutations.flatMap(m => Array.from(m.addedNodes))
            .filter(node => node?.matches?.('img[data-src]'))
            .forEach(img => imageObserver.observe(img));
    }).observe(document.body, {
        subtree: true,
        childList: true
    });

    // Process all images
    document.querySelectorAll('img:not([data-optimized])').forEach(img => {
        if (img.getAttribute('loading') === 'eager') {
            // For eager images, update placeholder and optimize immediately
            updatePlaceholder(img);
            if (img.complete) {
                loadOptimizedImage(img);
            } else {
                img.onload = () => loadOptimizedImage(img);
            }
        } else {
            // For other images, use lazy loading
            img.setAttribute('loading', 'lazy');
            imageObserver.observe(img);
        }
    });

    // Handle window resize
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            document.querySelectorAll('img:not([data-optimized])').forEach(updatePlaceholder);
        }, 100);
    });
});