#!/usr/bin/env python3
"""
Conservative unique visitor count using MIN(unique_ips, unique_sessions)
"""

import sys
import os
from datetime import datetime
from sqlalchemy import func

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.models import db, Analytics
from flask import Flask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    
    # Initialize database
    db.init_app(app)
    
    return app

def get_conservative_unique_visitors(target_date_str):
    """
    Get conservative unique visitor count using MIN(unique_ips, unique_sessions)
    
    Args:
        target_date_str (str): Date in format YYYY-MM-DD
    
    Returns:
        dict: Conservative metrics
    """
    try:
        # Parse the target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d')
        
        # Set start and end of the day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        print(f"Conservative unique visitor analysis for {target_date_str}")
        print(f"Date range: {start_date} to {end_date}")
        
        # Get both counts
        unique_ips = db.session.query(
            func.count(Analytics.ip_address.distinct())
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        unique_sessions = db.session.query(
            func.count(Analytics.sessionid.distinct())
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        # Calculate conservative count (minimum of the two)
        conservative_unique_visitors = min(unique_ips, unique_sessions)
        
        # Total page views for context
        total_pageviews = db.session.query(
            func.count(Analytics.id)
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"\n=== CONSERVATIVE UNIQUE VISITOR COUNT ===")
        print(f"Unique IPs: {unique_ips}")
        print(f"Unique Sessions: {unique_sessions}")
        print(f"Conservative Count (MIN): {conservative_unique_visitors}")
        print(f"Total Page Views: {total_pageviews}")
        
        # Explain the logic
        if unique_ips < unique_sessions:
            print(f"\n→ Using IP count ({unique_ips}) as the conservative estimate")
            print(f"  This suggests multiple sessions per IP (shared networks, session resets)")
        elif unique_sessions < unique_ips:
            print(f"\n→ Using session count ({unique_sessions}) as the conservative estimate")
            print(f"  This suggests some sessions used multiple IPs (mobile users, VPN changes)")
        else:
            print(f"\n→ Both counts are equal ({conservative_unique_visitors})")
            print(f"  Perfect 1:1 ratio between IPs and sessions")
        
        # Calculate engagement
        pages_per_visitor = round(total_pageviews / conservative_unique_visitors, 2) if conservative_unique_visitors > 0 else 0
        print(f"Average pages per visitor: {pages_per_visitor}")
        
        return {
            'date': target_date_str,
            'unique_ips': unique_ips,
            'unique_sessions': unique_sessions,
            'conservative_unique_visitors': conservative_unique_visitors,
            'total_pageviews': total_pageviews,
            'pages_per_visitor': pages_per_visitor
        }
        
    except ValueError as e:
        print(f"Error parsing date: {e}")
        print("Please use format YYYY-MM-DD (e.g., 2025-06-16)")
        return None
    except Exception as e:
        print(f"Error querying database: {e}")
        return None

def get_conservative_unique_visitor_count():
    """
    SQLAlchemy function to get conservative unique visitor count
    This can be used directly in your analytics queries
    
    Returns:
        SQLAlchemy expression: MIN(COUNT(DISTINCT ip_address), COUNT(DISTINCT sessionid))
    """
    # Note: This approach requires a subquery since we need to compare two aggregates
    # Here's the concept - you'd need to implement this in your specific query context
    return func.min(
        func.count(Analytics.ip_address.distinct()),
        func.count(Analytics.sessionid.distinct())
    )

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python conservative_unique_visitors.py YYYY-MM-DD")
        print("Example: python conservative_unique_visitors.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run analysis
    app = create_app()
    
    with app.app_context():
        results = get_conservative_unique_visitors(target_date)
        
        if results:
            print(f"\n=== FINAL ANSWER ===")
            print(f"Conservative unique visitors on {target_date}: {results['conservative_unique_visitors']}")
            
            print(f"\n=== PROPOSED IMPLEMENTATION ===")
            print("Replace your complex robust function with this simple approach:")
            print("1. Calculate unique_ips = COUNT(DISTINCT ip_address)")
            print("2. Calculate unique_sessions = COUNT(DISTINCT sessionid)")  
            print("3. Return MIN(unique_ips, unique_sessions)")
            print("4. This gives you the most conservative, realistic estimate")
        else:
            print("Failed to analyze conservative unique visitors.")
            sys.exit(1)