#!/usr/bin/env python3
"""
Test webhook with raw data instead of JSON
"""
import os
import sys
import json
import requests
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_raw_webhook():
    """Test webhook with raw JSON data"""
    print("Testing webhook with raw JSON data...")
    
    # Test payload
    test_payload = {
        "id": "evt_test_raw",
        "object": "event",
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": "cs_test_raw",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Raw Test Customer"
                }
            }
        }
    }
    
    # Convert to JSON string
    payload_json = json.dumps(test_payload, separators=(',', ':'))
    
    print(f"Payload: {payload_json}")
    print(f"Payload length: {len(payload_json)}")
    
    try:
        # Send as raw data (like <PERSON>e does)
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=payload_json,  # Send as raw data, not JSON
            headers={
                'Content-Type': 'application/json',
                # No signature header - should work if endpoint_secret is not required
            },
            timeout=10,
            verify=False
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_webhook_without_endpoint_secret():
    """Test webhook after temporarily removing endpoint secret"""
    print("\nTesting webhook without endpoint secret...")
    
    # Save current endpoint secret
    original_secret = os.getenv('STRIPE_ENDPOINT_SECRET')
    
    # Temporarily remove endpoint secret
    if 'STRIPE_ENDPOINT_SECRET' in os.environ:
        del os.environ['STRIPE_ENDPOINT_SECRET']
    
    try:
        # Test payload
        test_payload = {
            "id": "evt_test_no_secret",
            "object": "event",
            "type": "checkout.session.completed",
            "data": {
                "object": {
                    "id": "cs_test_no_secret",
                    "payment_status": "paid",
                    "customer_details": {
                        "email": "<EMAIL>",
                        "name": "No Secret Customer"
                    }
                }
            }
        }
        
        payload_json = json.dumps(test_payload, separators=(',', ':'))
        
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=payload_json,
            headers={'Content-Type': 'application/json'},
            timeout=10,
            verify=False
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        result = response.status_code == 200
        
    finally:
        # Restore endpoint secret
        if original_secret:
            os.environ['STRIPE_ENDPOINT_SECRET'] = original_secret
    
    return result

if __name__ == "__main__":
    print("Raw Webhook Test")
    print("=" * 40)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test with raw data
    raw_test = test_raw_webhook()
    
    # Test without endpoint secret
    no_secret_test = test_webhook_without_endpoint_secret()
    
    if raw_test or no_secret_test:
        print("\n✅ At least one test passed!")
        if raw_test:
            print("✅ Raw data webhook works")
        if no_secret_test:
            print("✅ Webhook without signature verification works")
    else:
        print("\n❌ All tests failed")