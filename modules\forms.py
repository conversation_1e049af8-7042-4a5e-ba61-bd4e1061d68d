from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, BooleanField, IntegerField, FileField, FieldList, FormField, HiddenField, PasswordField
from wtforms.validators import DataRequired, Optional, URL, ValidationError
from .ckeditor import CKEditorField

def ckeditor_required(message=None):
    if message is None:
        message = 'Dette feltet er påkrevd.'  # This field is required
    
    def _ckeditor_required(form, field):
        if not field.data or field.data.strip() == '':
            raise ValidationError(message)
    return _ckeditor_required

class IngredientForm(FlaskForm):
    """Form for a single ingredient"""
    class Meta:
        csrf = False  # Disable CSRF for nested form
    
    name = StringField('Ingrediens', validators=[DataRequired()])
    quantity = StringField('Mengde', validators=[DataRequired()])
    unit = StringField('Enhet', validators=[DataRequired()])

class CMSForm(FlaskForm):
    title = StringField('Tittel', validators=[DataRequired()])
    description = TextAreaField('Beskrivelse')
    main_content = CKEditorField('Innhold', validators=[ckeditor_required()])
    post_type = SelectField('Type', choices=[('blogg', 'Blogg'), ('oppskrift', 'Oppskrift')])
    tags = StringField('Tags')
    draft = BooleanField('Utkast')
    featured_image = FileField('Hovedbilde')
    
    # Recipe specific fields
    prep_time = IntegerField('Forberedelsestid (minutter)', validators=[Optional()])
    cook_time = IntegerField('Tilberedningstid (minutter)', validators=[Optional()])
    servings = IntegerField('Porsjoner', validators=[Optional()])
    difficulty = SelectField('Vanskelighetsgrad',
                           choices=[('', 'Velg vanskelighetsgrad'),
                                  ('enkel', 'Enkel'),
                                  ('middels', 'Middels'),
                                  ('avansert', 'Avansert')],
                           validators=[Optional()])
    
    # Recipe ingredients
    ingredients_json = HiddenField('Ingredients JSON')
    
    # Recipe categories
    category = SelectField('Kategori',
                         choices=[('', 'Velg kategori'),
                                ('frokost', 'Frokost'),
                                ('lunsj', 'Lunsj'),
                                ('middag', 'Middag'),
                                ('dessert', 'Dessert'),
                                ('snacks', 'Snacks')],
                         validators=[Optional()])
    dish = StringField('Rett Type (f.eks. Suppe, Salat, etc.)', validators=[Optional()])
    
    # Recipe instructions
    instructions = TextAreaField('Fremgangsmåte', validators=[Optional()])
    tips = TextAreaField('Tips og triks', validators=[Optional()])
    
    # SEO and metadata
    url_slug = StringField('URL Slug', validators=[Optional()])

class NewsletterSubscribeForm(FlaskForm):
    """Form for newsletter subscription"""
    name = StringField('Navn', validators=[DataRequired()])
    email = StringField('E-post', validators=[DataRequired()])
    checkbox = HiddenField('Anti-Bot Checkbox')  # Honeypot field

class NewsletterUnsubscribeForm(FlaskForm):
    """Form for newsletter unsubscription"""
    email = StringField('E-post', validators=[DataRequired()])

class ContactForm(FlaskForm):
    """Form for contact submissions"""
    name = StringField('Navn', validators=[DataRequired()])
    email = StringField('E-post', validators=[DataRequired()])
    message = TextAreaField('Melding', validators=[DataRequired()])
    altcha = HiddenField('ALTCHA Token', validators=[DataRequired()])
    checkbox = HiddenField('Anti-Bot Checkbox')  # Honeypot field

class LoginForm(FlaskForm):
    """Form for user login"""
    username = StringField('Brukernavn', validators=[DataRequired()])
    password = PasswordField('Passord', validators=[DataRequired()])
    checkbox = HiddenField('Anti-Bot Checkbox')  # Honeypot field
