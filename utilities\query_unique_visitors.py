#!/usr/bin/env python3
"""
Script to query unique visitors for a specific date
"""

import sys
import os
from datetime import datetime, timedelta
from sqlalchemy import func, case

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.models import db, Analytics
from modules.analytics_process import get_robust_unique_visitor_count
from flask import Flask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    db.init_app(app)
    
    return app

def get_unique_visitors_for_date(target_date_str):
    """
    Get unique visitors for a specific date using the robust method
    
    Args:
        target_date_str (str): Date in format YYYY-MM-DD
    
    Returns:
        int: Number of unique visitors
    """
    try:
        # Parse the target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d')
        
        # Set start and end of the day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        print(f"Querying unique visitors for {target_date_str}")
        print(f"Date range: {start_date} to {end_date}")
        
        # Get total visits (excluding bots) for context
        total_visits = Analytics.query.filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).count()
        
        print(f"Total visits (excluding bots): {total_visits}")
        
        if total_visits == 0:
            print("No visits found for this date.")
            return 0
        
        # Get unique visitors using the robust hybrid approach
        unique_visitors = db.session.query(
            get_robust_unique_visitor_count(include_date_in_key=False)  # False for same-day analysis
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"Unique visitors (robust method): {unique_visitors}")
        
        # For comparison, also show session-only method
        session_only_count = db.session.query(
            func.count(Analytics.sessionid.distinct())
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"Unique visitors (session-only method): {session_only_count}")
        
        # Show some sample data for verification
        print("\nSample visits for this date:")
        sample_visits = Analytics.query.filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).limit(5).all()
        
        for visit in sample_visits:
            print(f"  - {visit.timestamp}: {visit.url} (Session: {visit.sessionid[:8]}..., IP: {visit.ip_address})")
        
        return unique_visitors
        
    except ValueError as e:
        print(f"Error parsing date: {e}")
        print("Please use format YYYY-MM-DD (e.g., 2025-06-16)")
        return None
    except Exception as e:
        print(f"Error querying database: {e}")
        return None

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python query_unique_visitors.py YYYY-MM-DD")
        print("Example: python query_unique_visitors.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run query
    app = create_app()
    
    with app.app_context():
        unique_visitors = get_unique_visitors_for_date(target_date)
        
        if unique_visitors is not None:
            print(f"\n=== RESULT ===")
            print(f"Unique visitors on {target_date}: {unique_visitors}")
        else:
            print("Failed to get unique visitors count.")
            sys.exit(1)