import secrets
import logging
import traceback
import os
import base64
import json
from flask import Flask, request, jsonify, current_app
from altcha import ChallengeOptions, create_challenge, verify_solution
import re
import logging
from typing import Any, Optional, Tuple, Dict, Union
import html
import bleach
import requests
from dotenv import load_dotenv
from flask_cors import CORS

# Load environment variables from .env file
load_dotenv()

# Utility function to retrieve HMAC key
def get_hmac_key():
    """
    Retrieve the HMAC key from environment variables.
    
    Returns:
        str: The HMAC key
    
    Raises:
        ValueError: If HMAC key is not set
    """
    hmac_key = os.getenv('HMAC_KEY')
    if not hmac_key:
        raise ValueError("HMAC_KEY must be set in the .env file")
    return hmac_key

# Generate a secure HMAC key
def generate_hmac_key(length=32):
    """
    Generate a secure HMAC key for cryptographic purposes
    
    Args:
        length (int, optional): Length of the key in bytes. Defaults to 32.
    
    Returns:
        str: A cryptographically secure hex-encoded HMAC key
    """
    # Use the HMAC_KEY from environment variable
    return get_hmac_key()

# Generate a secure CSRF token
def generate_csrf_token():
    """
    Generate a secure CSRF token.
    
    Returns:
        str: A cryptographically secure random token
    """
    if 'csrf_token' not in current_app.config:
        current_app.config['csrf_token'] = secrets.token_hex(16)
    return current_app.config['csrf_token']

def validate_csrf_token(token):
    """
    Validate the provided CSRF token.
    
    Args:
        token (str): The token to validate
    
    Returns:
        bool: True if token is valid, False otherwise
    """
    stored_token = current_app.config.get('csrf_token')
    return secrets.compare_digest(str(token), str(stored_token)) if stored_token else False

def generate_altcha_challenge(max_number=100000):
    """
    Generate an ALTCHA challenge.
    
    Args:
        max_number (int, optional): Maximum number for challenge. Defaults to 100000.
    
    Returns:
        dict: A dictionary containing challenge details
    """
    try:
        hmac_key = get_hmac_key()
        
        options = ChallengeOptions(
            max_number=max_number,
            hmac_key=hmac_key,
        )
        challenge = create_challenge(options)
        
        logging.info(f"Generated ALTCHA challenge: {challenge.challenge}")
        logging.debug(f"Challenge details: algorithm={challenge.algorithm}, salt={challenge.salt}")

        return {
            "algorithm": challenge.algorithm,
            "challenge": challenge.challenge,
            "salt": challenge.salt,
            "signature": challenge.signature,
        }
    except Exception as e:
        logging.error(f"Error creating ALTCHA challenge: {str(e)}")
        logging.error(f"Error type: {type(e)}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": "Internal server error"}

def validate_altcha_payload(payload):
    """
    Comprehensive validation of ALTCHA payload with detailed error checking
    
    Args:
        payload (str): Base64 encoded ALTCHA payload
    
    Returns:
        tuple: (is_valid, error_message, payload_details)
            - is_valid (bool): Whether the payload is valid
            - error_message (str): Descriptive error message if validation fails
            - payload_details (dict): Decoded payload details
    """
    try:
        import base64
        import json
        
        # Decode the payload
        try:
            decoded_payload = base64.b64decode(payload).decode('utf-8')
            payload_dict = json.loads(decoded_payload)
        except (base64.binascii.Error, json.JSONDecodeError) as decode_err:
            logging.error(f"Payload decoding error: {decode_err}")
            return False, f"Invalid payload format: {decode_err}", None
        
        # Validate required keys
        required_keys = ['challenge', 'salt', 'signature', 'algorithm']
        for key in required_keys:
            if key not in payload_dict:
                logging.warning(f"Missing required key: {key}")
                return False, f"Missing required key: {key}", payload_dict
        
        # Perform ALTCHA validation
        hmac_key = get_hmac_key()
        ok, err = verify_solution(payload, hmac_key, check_expires=True)
        
        if ok:
            logging.info("ALTCHA validation successful")
            return True, "", payload_dict
        else:
            logging.warning(f"ALTCHA validation failed: {err}")
            return False, err or "ALTCHA validation failed", payload_dict
    
    except Exception as e:
        logging.error(f"Unexpected error in ALTCHA validation: {str(e)}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False, f"Unexpected validation error: {str(e)}", None

def validate_altcha(payload):
    """
    Simplified ALTCHA validation wrapper
    
    Args:
        payload (str): The challenge response payload
        
    Returns:
        tuple: (bool, str) - (success, error_message)
    """
    is_valid, error_message, _ = validate_altcha_payload(payload)
    return is_valid, error_message

def check_ip_reputation(client_ip, spam_threshold=99):
    """
    Check IP reputation using AbuseIPDB API
    
    Args:
        client_ip (str): IP address to check
        spam_threshold (int, optional): Threshold for considering an IP as spam. Defaults to 99.
    
    Returns:
        tuple: (abuse_confidence_score, error_message)
            - abuse_confidence_score (int): Confidence score of IP abuse
            - error_message (str or None): Error message if check fails
    """
    import os
    import json
    import logging
    import requests
    
    # AbuseIPDB API configuration
    ip_check_url = "https://api.abuseipdb.com/api/v2/check"
    
    # Prepare request parameters
    querystring = {"ipAddress": client_ip, "maxAgeInDays": "90"}
    headers = {
        "Accept": "application/json", 
        "Key": os.getenv("ABUSEIPDB_API_KEY")
    }
    
    # Default to 0 if API check fails
    abuse_confidence_score = 0
    
    try:
        # Log request details for debugging
        logging.info(f"AbuseIPDB Request URL: {ip_check_url}")
        logging.info(f"AbuseIPDB Request Headers: {headers}")
        logging.info(f"AbuseIPDB Request Params: {querystring}")
        
        # Make API request
        response = requests.request(
            method="GET", 
            url=ip_check_url, 
            headers=headers, 
            params=querystring
        )
        
        # Log response details
        logging.info(f"AbuseIPDB Response Status: {response.status_code}")
        logging.info(f"AbuseIPDB Response Text: {response.text}")
        
        # Check if the response is successful
        if response.status_code == 200:
            try:
                decoded_response = response.json()
                abuse_confidence_score = decoded_response.get("data", {}).get("abuseConfidenceScore", 0)
                
                # Log the abuse confidence score
                logging.info(f"IP {client_ip} Abuse Confidence Score: {abuse_confidence_score}")
                
                return abuse_confidence_score, None
            except json.JSONDecodeError as json_err:
                error_msg = f"JSON Decode Error: {json_err}"
                logging.error(error_msg)
                logging.error(f"Response Text: {response.text}")
                return 0, error_msg
        else:
            error_msg = f"AbuseIPDB API returned non-200 status: {response.status_code}"
            logging.warning(error_msg)
            logging.warning(f"Response Text: {response.text}")
            return 0, error_msg
    
    except requests.RequestException as e:
        error_msg = f"AbuseIPDB API request error: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return 0, error_msg

def check_ip_detective(client_ip):
    """
    Check if an IP is a bot using IPdetective.io API
    
    Args:
        client_ip (str): IP address to check
    
    Returns:
        tuple: (is_bot, error_message)
            - is_bot (bool): Whether the IP is a bot according to IPdetective.io
            - error_message (str or None): Error message if check fails
    """
    import os
    import json
    import logging
    import requests
    
    # IPdetective.io API configuration
    ip_check_url = f"https://api.ipdetective.io/ip/{client_ip}"
    
    # Get API key from environment variables
    api_key = os.getenv("IPDETECTIVE_API_KEY")
    
    if not api_key:
        error_msg = "IPDETECTIVE_API_KEY not found in environment variables"
        logging.error(error_msg)
        return False, error_msg
    
    # Prepare request headers
    headers = {
        "x-api-key": api_key
    }
    
    try:
        # Log request details for debugging
        logging.info(f"IPdetective.io Request URL: {ip_check_url}")
        
        # Make API request
        response = requests.request(
            method="GET", 
            url=ip_check_url, 
            headers=headers
        )
        
        # Log response details
        logging.info(f"IPdetective.io Response Status: {response.status_code}")
        logging.info(f"IPdetective.io Response Text: {response.text}")
        
        # Check if the response is successful
        if response.status_code == 200:
            try:
                decoded_response = response.json()
                is_bot = decoded_response.get("bot", False)
                
                # Log the bot detection result
                logging.info(f"IP {client_ip} Bot Detection (IPdetective.io): {is_bot}")
                
                return is_bot, None
            except json.JSONDecodeError as json_err:
                error_msg = f"JSON Decode Error: {json_err}"
                logging.error(error_msg)
                logging.error(f"Response Text: {response.text}")
                return False, error_msg
        else:
            error_msg = f"IPdetective.io API returned non-200 status: {response.status_code}"
            logging.warning(error_msg)
            logging.warning(f"Response Text: {response.text}")
            return False, error_msg
    
    except requests.RequestException as e:
        error_msg = f"IPdetective.io API request error: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False, error_msg

def is_potential_spam(client_ip, spam_threshold=99):
    """
    Determine if an IP is potentially spam based on AbuseIPDB score
    
    Args:
        client_ip (str): IP address to check
        spam_threshold (int, optional): Threshold for considering an IP as spam. Defaults to 99.
    
    Returns:
        tuple: (is_spam, abuse_confidence_score, error_message)
            - is_spam (bool): Whether the IP is considered spam
            - abuse_confidence_score (int): Confidence score of IP abuse
            - error_message (str or None): Error message if check fails
    """
    abuse_confidence_score, error_message = check_ip_reputation(client_ip, spam_threshold)
    
    # Consider spam if score is above threshold or if there was an error in checking
    is_spam = abuse_confidence_score > spam_threshold or error_message is not None
    
    return is_spam, abuse_confidence_score, error_message

def init_security_routes(app):
    """
    Initialize security-related routes
    
    Args:
        app (Flask): The Flask application instance
    """
    @app.route('/altcha_challenge')
    def altcha_challenge():
        """
        Generate an ALTCHA challenge
        
        Returns:
            JSON response with challenge details
        """
        challenge = generate_altcha_challenge()
        return jsonify(challenge)

def init_cors(app: Flask):
    """
    Initialize CORS (Cross-Origin Resource Sharing) for the Flask application.
    
    Args:
        app (Flask): The Flask application instance
    """
    # Define allowed origins
    cors_origins = [
        os.getenv('STRIPE_DOMAIN'),
        'http://localhost:5000',
        'http://localhost:8282',
        'https://localhost:5000',
        'https://localhost:8282',
        'http://127.0.0.1:5000',
        'http://127.0.0.1:8282',
        'https://127.0.0.1:5000',
        'https://127.0.0.1:8282'
    ]
    
    # Configure CORS
    CORS(app, resources={
        r"/*": {
            "origins": cors_origins,
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"],
            "supports_credentials": True
        }
    })
    
    # Optional: Add a logging mechanism for CORS
    @app.after_request
    def log_cors_headers(response):
        """
        Log CORS-related headers for debugging purposes.
        
        Args:
            response: Flask response object
        
        Returns:
            Flask response object
        """
        if 'Origin' in request.headers:
            logging.info(f"CORS request from origin: {request.headers['Origin']}")
        return response

def init_csp(app: Flask):
    """
    Initialize Content Security Policy (CSP) for the Flask application.
    
    Args:
        app (Flask): The Flask application instance
    """
    @app.after_request
    def add_security_headers(response):
        """
        Add security headers including CSP to all responses.
        
        Args:
            response: Flask response object
        
        Returns:
            Flask response object with added security headers
        """
        # Define CSP directives
        csp_directives = {
            'default-src': ["'self'"],
            'script-src': [
                "'self'",
                "'unsafe-inline'",  # Required for inline scripts
                "'unsafe-eval'",    # Required for some JavaScript frameworks 
                "data:",  # Allow base64 encoded scripts
                "blob:",  # Allow blob URLs for web workers
                "https://js.stripe.com/v3/",
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'",  # Required for inline styles
            ],
            'img-src': [
                "'self'",
                "data:",           # For embedded images
                "https:",          # Allow images from any HTTPS source
                "blob:",           # For dynamic image handling
            ],
            'font-src': [
                "'self'",
            ],
            'connect-src': [
                "'self'",
                "https://api.abuseipdb.com",  # AbuseIPDB API endpoint
                "https://api.ipdetective.io"  # IPdetective.io API endpoint
            ],
            'frame-src': [
                "'self'",
                os.getenv('STRIPE_DOMAIN'),
                "https://*.stripe.com",
                "https://*.stripecdn.com",
                "https://*.stripe.network",
                "https://pay.google.com"
            ],
            'media-src': ["'self'"],
            'object-src': ["'none'"],  # Disable plugins
            'base-uri': ["'self'"],    # Restrict base tag
            'form-action': ["'self'"],  # Restrict form submissions
            'frame-ancestors': [
                "'self'",
                "https://*.stripe.com",
                "https://*.stripecdn.com",
                "https://*.stripe.network",
                "https://pay.google.com"
                ],  # Control embedding
            'upgrade-insecure-requests': []  # Force HTTPS
        }

        # Build CSP string
        csp_string = '; '.join(
            f"{key} {' '.join(values)}" 
            for key, values in csp_directives.items()
        )

        # Add security headers
        response.headers['Content-Security-Policy'] = csp_string
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'SAMEORIGIN'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'

        return response

    return add_security_headers

def init_security(app: Flask):
    """
    Initialize all security features for the Flask application.
    
    Args:
        app (Flask): The Flask application instance
    """
    # Initialize CORS
    init_cors(app)
    
    # Initialize CSP and other security headers
    init_csp(app)
    
    # Log security-related events
    @app.after_request
    def log_security_headers(response):
        if app.debug:
            logging.debug(f"Security headers set: {dict(response.headers)}")
        return response

def sanitize_input(
    value: Any, 
    input_type: type = str, 
    max_length: int = 500, 
    default: Any = '', 
    allow_html: bool = False,
    tags: list = None
) -> Optional[Union[str, int, float]]:
    """
    Enhanced input sanitization with type-specific validation
    
    Args:
        value: Input value to sanitize
        input_type: Expected input type (str, int, float)
        max_length: Maximum allowed length
        default: Default value if input is invalid
        allow_html: Whether to allow limited HTML
        tags: List of allowed HTML tags when allow_html is True
    
    Returns:
        Sanitized input or default value
    """
    if value is None:
        return default
    
    try:
        # Type conversion
        if input_type == str:
            str_value = str(value)[:max_length]
            
            # Advanced sanitization
            if not allow_html:
                # Remove all HTML tags
                str_value = bleach.clean(str_value, strip=True)
            else:
                # Allow specified HTML tags or default safe tags
                allowed_tags = tags if tags else ['b', 'i', 'u', 'em', 'strong']
                str_value = bleach.clean(str_value, tags=allowed_tags, strip=True)
            
            # Escape special characters
            str_value = html.escape(str_value)
            
            return str_value.strip()
        
        elif input_type in (int, float):
            # Numeric type validation
            return input_type(value)
        
    except (ValueError, TypeError):
        return default
    
    return default

def validate_email(email: str) -> Optional[str]:
    """
    Advanced email validation with normalization
    
    Args:
        email: Email address to validate
    
    Returns:
        Normalized email or None if invalid
    """
    if not email:
        return None
    
    # Normalize email
    email = email.strip().lower()
    
    # Comprehensive email regex that follows RFC 5322
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    if not re.match(email_regex, email):
        return None
    
    # Advanced length checks
    if len(email) > 254 or len(email.split('@')[1]) > 253:
        return None
        
    # Additional security: sanitize with bleach
    email = bleach.clean(email, strip=True)
    
    return email

def safe_execute(func: callable, *args, **kwargs) -> tuple:
    """
    Safely execute a function and handle potential errors
    
    Args:
        func (callable): Function to execute
        *args: Positional arguments
        **kwargs: Keyword arguments
    
    Returns:
        tuple: (success, result/error)
    """
    try:
        result = func(*args, **kwargs)
        return True, result
    except Exception as e:
        logging.error(f"Error in safe_execute: {str(e)}")
        return False, e

def get_client_ip(request):
    """
    Retrieve client IP address safely
    
    Args:
        request: Flask request object
    
    Returns:
        str: Client IP address
    """
    # Check for common proxy headers in order of reliability
    headers_to_check = [
        'X-Forwarded-For',   # Most common proxy header
        'X-Real-IP',         # Nginx proxy header
        'HTTP_X_FORWARDED_FOR', 
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'HTTP_CLIENT_IP'
    ]
    
    for header in headers_to_check:
        ip = request.headers.get(header)
        if ip:
            # If multiple IPs are present (common with X-Forwarded-For), take the first
            ip = ip.split(',')[0].strip()
            if ip:
                return ip
    
    # Fallback to remote address
    return request.remote_addr or '127.0.0.1'