/* Custom CSS Bootstrap Studio */

#index-top-row {
  background: url("../../assets/img/rosa-blob.webp") bottom / auto no-repeat;
}

#index-top-row-text-div {
  max-width: 450px;
}

#index-top-row-image {
  width: 500px;
  height: 600px;
}

#index-mealplan-features-row {
  max-width: 900px;
}

.bs-icon-lg.bs-icon {
  top: 1rem;
  right: 1rem;
  position: absolute;
}

.bs-icon {
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  font-size: var(--bs-icon-size);
  width: calc(var(--bs-icon-size) * 2);
  height: calc(var(--bs-icon-size) * 2);
  color: var(--bs-primary);
}

.kostplan-hero-container {
  top: -50px;
}

.bs-icon-lg.bs-icon.kostplan-card-div {
  top: -30px;
}

.signature {
  font-family: Allura, serif;
  font-size: 30px;
}

.carousel-inner {
  height: 600px;
}

.custom-card {
  padding: 30px;
  border-radius: 50px 50px 0 0;
  color: white;
  margin-bottom: 20px;
}

.purple-card {
  background-color: #e88cf1;
}

.yellow-card {
  background-color: #FFC966;
}

.red-card {
  background-color: #FF6D6D;
}

.icon {
  font-size: 40px;
  margin-bottom: 15px;
  color: #fff;
}

/* AI Progress and Status Indicators */

.ai-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #f3f3f3;
  z-index: 9999;
}

.ai-progress-bar {
  height: 100%;
  width: 0;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.ai-status {
  position: fixed;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 15px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: none;
  align-items: center;
  gap: 10px;
  z-index: 9999;
  border: 1px solid #e0e0e0;
  font-size: 14px;
}

.ai-status.active {
  display: flex;
}

.ai-status-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: ai-spin 1s linear infinite;
}

@keyframes ai-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* CKEditor Styles */

.ck-editor__editable {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
}

/* Share dialog styles */

.share-button {
  font-family: 'Shadows Into Light Two', cursive;
  font-size: 1.2rem;
}

.decorative-arrow {
  width: 60px;
  height: auto;
}

.guarantee {
  opacity: 0.80;
}

.newsletter-container {
  background: var(--bs-primary-bg-subtle);
}

.contact-container {
  background: var(--bs-primary-bg-subtle);
}

.shopping-container {
  background: var(--bs-primary-bg-subtle);
}

.about-container {
  background: url("../../assets/img/rosa-blob.webp") bottom / auto no-repeat;
}

.carousel-item {
  height: inherit;
}

.horizontal-images {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
}

.horizontal-images img {
  max-width: 100px;
  height: auto;
  margin: 0 1rem;
  display: inline-block;
}

