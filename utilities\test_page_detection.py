import sys
import os
from urllib.parse import urljoin

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import Flask and create a test app
from flask import Flask, g, request
import modules.seo_data_extractor

# Create a test app
test_app = Flask(__name__)

# Mock the database queries
def mock_get_current_post():
    """Mock implementation of get_current_post for testing"""
    if hasattr(g, 'post'):
        return g.post
    return None

# Replace the actual function with our mock
modules.seo_data_extractor.get_current_post = mock_get_current_post

# Import page detection functions after mocking
from modules.seo_data_extractor import (
    get_page_type, is_recipe_page, is_article_page, 
    is_product_page, is_book_page, is_review_page,
    get_current_endpoint
)

def test_page_detection(url, endpoint=None, post=None):
    """Test page type detection with a given URL and context"""
    print(f"\nTesting URL: {url}")
    print(f"Endpoint: {endpoint}")
    print(f"Post type: {post.post_type if post else 'None'}")
    
    # We need to monkey patch the get_current_endpoint function for testing
    original_get_endpoint = modules.seo_data_extractor.get_current_endpoint
    
    def mock_get_endpoint():
        return endpoint
    
    # Replace the function temporarily
    if endpoint:
        modules.seo_data_extractor.get_current_endpoint = mock_get_endpoint
    
    try:
        with test_app.test_request_context(url):
            # Set the post in g if provided
            if post:
                g.post = post
            
            # Test all detection methods
            print(f"Current endpoint: {get_current_endpoint()}")
            print(f"Is recipe page: {is_recipe_page()}")
            print(f"Is article page: {is_article_page()}")
            print(f"Is product page: {is_product_page()}")
            print(f"Is book page: {is_book_page()}")
            print(f"Is review page: {is_review_page()}")
            print(f"Page type: {get_page_type()}")
    finally:
        # Restore the original function
        modules.seo_data_extractor.get_current_endpoint = original_get_endpoint

if __name__ == '__main__':
    print("Testing Page Type Detection\n")
    
    # Create mock CMS posts for testing
    recipe_post = type('CMS', (), {
        'id': 1,
        'post_type': 'oppskrift',
        'title': 'Test Recipe',
        'url_slug': 'test-recipe'
    })
    
    article_post = type('CMS', (), {
        'id': 2,
        'post_type': 'artikkel',
        'title': 'Test Article',
        'url_slug': 'test-article'
    })
    
    # Test various scenarios
    
    # Test 1: Home page
    test_page_detection('https://ketolabben.com/', 'index')
    
    # Test 2: Recipe page with URL pattern
    test_page_detection('https://ketolabben.com/oppskrift/keto-pannekaker', 'recipe_page')
    
    # Test 3: Recipe page with CMS post
    test_page_detection('https://ketolabben.com/test-recipe', 'blog_post', recipe_post)
    
    # Test 4: Article page with URL pattern
    test_page_detection('https://ketolabben.com/artikkel/fordeler-med-keto', 'article_page')
    
    # Test 5: Article page with CMS post
    test_page_detection('https://ketolabben.com/test-article', 'blog_post', article_post)
    
    # Test 6: Product page
    test_page_detection('https://ketolabben.com/produkt/ketolabben-kokebok', 'product_page')
    
    # Test 7: Book page
    test_page_detection('https://ketolabben.com/bok/ketolabben-kokebok', 'book_page')
    
    # Test 8: Review page
    test_page_detection('https://ketolabben.com/omtaler.html', 'omtaler')
    
    # Test 9: CMS post with ambiguous URL but clear post type
    test_page_detection('https://ketolabben.com/cms-post-123.html', None, recipe_post)
    
    # Test 10: Ambiguous URL with no endpoint or post
    test_page_detection('https://ketolabben.com/some-random-page.html')
    
    print("\nAll tests completed.")