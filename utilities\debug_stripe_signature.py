#!/usr/bin/env python3
"""
Debug Stripe signature verification to understand the issue
"""
import os
import sys
import json
import stripe
import hmac
import hashlib
import time
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stripe_signature_verification():
    """Test Stripe signature verification locally"""
    print("Testing Stripe signature verification locally...")
    
    # Get the endpoint secret
    endpoint_secret = os.getenv('STRIPE_ENDPOINT_SECRET')
    if not endpoint_secret:
        print("❌ STRIPE_ENDPOINT_SECRET not found")
        return False
    
    print(f"Endpoint secret: {endpoint_secret}")
    
    # Test payload
    test_payload = {
        "id": "evt_test_local",
        "object": "event",
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": "cs_test_local",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Local Test"
                }
            }
        }
    }
    
    payload_json = json.dumps(test_payload, separators=(',', ':'))
    print(f"Payload: {payload_json}")
    
    # Generate signature manually (method 1 - with whsec_ prefix)
    timestamp = str(int(time.time()))
    signed_payload = f"{timestamp}.{payload_json}"
    
    # Try with full secret (including whsec_)
    try:
        signature1 = hmac.new(
            endpoint_secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        sig_header1 = f"t={timestamp},v1={signature1}"
        print(f"Signature with whsec_ prefix: {sig_header1}")
        
        # Test with Stripe's construct_event
        event1 = stripe.Webhook.construct_event(
            payload_json, sig_header1, endpoint_secret
        )
        print("✅ Signature verification successful with whsec_ prefix!")
        
    except Exception as e:
        print(f"❌ Signature verification failed with whsec_ prefix: {str(e)}")
    
    # Try without whsec_ prefix (method 2)
    raw_secret = endpoint_secret[6:] if endpoint_secret.startswith('whsec_') else endpoint_secret
    try:
        signature2 = hmac.new(
            raw_secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        sig_header2 = f"t={timestamp},v1={signature2}"
        print(f"Signature without whsec_ prefix: {sig_header2}")
        
        # Test with Stripe's construct_event
        event2 = stripe.Webhook.construct_event(
            payload_json, sig_header2, endpoint_secret
        )
        print("✅ Signature verification successful without whsec_ prefix!")
        
    except Exception as e:
        print(f"❌ Signature verification failed without whsec_ prefix: {str(e)}")
    
    # Try to understand what Stripe expects
    print(f"\nDebugging info:")
    print(f"Full endpoint secret: {endpoint_secret}")
    print(f"Raw secret (without whsec_): {raw_secret}")
    print(f"Timestamp: {timestamp}")
    print(f"Signed payload: {signed_payload[:100]}...")
    
    return True

def test_webhook_with_correct_signature():
    """Test webhook with the correct signature method"""
    print("\nTesting webhook with corrected signature...")
    
    endpoint_secret = os.getenv('STRIPE_ENDPOINT_SECRET')
    if not endpoint_secret:
        return False
    
    # Use the method that worked in local testing
    test_payload = {
        "id": "evt_test_corrected",
        "object": "event",
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": "cs_test_corrected",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Corrected Test"
                }
            }
        }
    }
    
    payload_json = json.dumps(test_payload, separators=(',', ':'))
    
    # Generate signature using the raw secret (without whsec_)
    timestamp = str(int(time.time()))
    signed_payload = f"{timestamp}.{payload_json}"
    raw_secret = endpoint_secret[6:] if endpoint_secret.startswith('whsec_') else endpoint_secret
    
    signature = hmac.new(
        raw_secret.encode('utf-8'),
        signed_payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    sig_header = f"t={timestamp},v1={signature}"
    
    try:
        import requests
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=payload_json,
            headers={
                'Content-Type': 'application/json',
                'stripe-signature': sig_header
            },
            verify=False,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Stripe Signature Debug")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test signature verification locally
    test_stripe_signature_verification()
    
    # Test webhook with corrected signature
    test_webhook_with_correct_signature()