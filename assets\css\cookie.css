.consent-display {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
    font-size: 14px;
}

.consent-display h3 {
    margin: 0 0 0.5rem 0;
    font-size: 16px;
    color: #212529;
}

.consent-item {
    display: flex;
    align-items: center;
    margin: 0.25rem 0;
}

.consent-status {
    margin-left: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.status-accepted {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.consent-meta {
    font-size: 12px;
    color: #6c757d;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #dee2e6;
}

#consent-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f8f9fa;
    padding: 1rem;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    display: none;
    font-size: 14px;
}

#consent-banner .consent-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

#consent-banner .consent-text {
    margin-bottom: 0.5rem;
}

#consent-banner .consent-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
}

.consent-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    white-space: nowrap;
}

.btn-accept {
    background: #28a745;
    color: white;
}

.btn-reject {
    background: #dc3545;
    color: white;
}

.btn-settings {
    background: #6c757d;
    color: white;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 24px;
    margin-left: 1rem;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #28a745;
}

input:disabled + .slider {
    background-color: #e9ecef;
    cursor: not-allowed;
}

input:checked + .slider:before {
    transform: translateX(16px);
}

#settings-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1055;
}

.modal-dialog {
    position: absolute;
    width: 90%;
    max-width: 400px;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    user-select: none;
}

.modal-content {
    background-color: #fff;
    border-radius: 0.3rem;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    cursor: move;
    background: #f8f9fa;
    border-radius: 0.3rem 0.3rem 0 0;
}

.modal-body {
    padding: 0.75rem;
    overflow-y: auto;
    max-height: calc(80vh - 120px);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    background: #fff;
    border-radius: 0 0 0.3rem 0.3rem;
}

.category-item {
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.875rem;
}

.category-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.category-description {
    font-size: 0.8125rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.cookie-list {
    font-size: 0.8125rem;
    color: #6c757d;
    margin-top: 0.25rem;
    padding-left: 0.75rem;
}

.cookie-provider {
    font-weight: 500;
    color: #495057;
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.cookie-details {
    margin-left: 0.5rem;
    margin-top: 0.125rem;
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .modal-dialog {
        width: 95%;
        margin: 0.5rem;
    }
    
    .modal-body {
        padding: 0.5rem;
    }
}