// Wait for Stripe to be available
const waitForStripe = () => {
  return new Promise((resolve) => {
    if (typeof Stripe !== 'undefined') {
      resolve();
    } else {
      const checkStripe = setInterval(() => {
        if (typeof Stripe !== 'undefined') {
          clearInterval(checkStripe);
          resolve();
        }
      }, 100);
    }
  });
};

let stripeInstance;

// Initialize after Stripe is available
waitForStripe().then(() => {
  // Create a Checkout Session with 1 second delay
  // Stripe instance will be initialized with the server-provided key in the initialize() function
  setTimeout(() => {
    initialize();
  }, 1000);
});

// Create a Checkout Session
async function initialize() {
  const fetchClientSecret = async () => {
    const response = await fetch("/create-checkout-session", {
      method: "POST",
    });
    const { clientSecret, publishableKey } = await response.json();
    stripeInstance = Stripe(publishableKey);  // Initialize stripe instance with the server-provided key
    return clientSecret;
  };

  // First fetch the client secret and initialize Stripe with the server-provided key
  const clientSecret = await fetchClientSecret();
  
  const checkout = await stripeInstance.initEmbeddedCheckout({
    fetchClientSecret: () => Promise.resolve(clientSecret),
  });

  // Mount Checkout
  checkout.mount('#checkout');
}