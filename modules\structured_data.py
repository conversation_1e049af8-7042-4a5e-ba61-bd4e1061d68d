import json
import re
from flask import request, current_app
from urllib.parse import urljoin
from datetime import datetime

def generate_product_schema(product):
    """
    Generate JSON-LD structured data for a product
    
    Args:
        product: Product data dictionary
        
    Returns:
        JSON-LD structured data as a string
    """
    schema = {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": product.get('title', ''),
        "description": product.get('description', ''),
        "image": urljoin(request.url_root, product.get('featured_image', '')),
        "url": request.url,
    }
    
    # Add offers if price is available
    if product.get('price'):
        schema["offers"] = {
            "@type": "Offer",
            "price": product.get('price'),
            "priceCurrency": "NOK",
            "availability": "https://schema.org/InStock",
            "url": request.url
        }
    
    # Add reviews if available
    if product.get('reviews'):
        schema["review"] = []
        for review in product.get('reviews', []):
            schema["review"].append({
                "@type": "Review",
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": review.get('rating', 5),
                    "bestRating": 5
                },
                "author": {
                    "@type": "Person",
                    "name": review.get('author', 'Anonymous')
                },
                "reviewBody": review.get('content', '')
            })
    
    return json.dumps(schema, ensure_ascii=False)

def generate_recipe_schema(recipe):
    """
    Generate JSON-LD structured data for a recipe
    
    Args:
        recipe: Recipe data from CMS
        
    Returns:
        JSON-LD structured data as a string
    """
    # Parse ingredients from JSON string if needed
    ingredients_list = []
    if recipe.get('ingredients'):
        try:
            ingredients_data = json.loads(recipe.get('ingredients'))
            for ingredient in ingredients_data:
                ingredients_list.append(
                    f"{ingredient.get('quantity', '')} {ingredient.get('unit', '')} {ingredient.get('name', '')}"
                )
        except (json.JSONDecodeError, TypeError):
            pass
    
    # Calculate total time
    total_time = 0
    prep_time = recipe.get('prep_time', 0) or 0
    cook_time = recipe.get('cook_time', 0) or 0
    
    if prep_time or cook_time:
        total_time = prep_time + cook_time
    
    # Format times in ISO 8601 duration format
    prep_time_iso = f"PT{prep_time}M" if prep_time else None
    cook_time_iso = f"PT{cook_time}M" if cook_time else None
    total_time_iso = f"PT{total_time}M" if total_time else None
    
    # Extract instructions from HTML content
    instructions = []
    if recipe.get('instructions'):
        # Simple HTML to text conversion for instructions
        instruction_text = re.sub(r'<[^>]+>', ' ', recipe.get('instructions'))
        instruction_text = re.sub(r'\s+', ' ', instruction_text).strip()
        
        # Split by numbers or bullet points
        instruction_parts = re.split(r'\d+\.\s|\•\s', instruction_text)
        for part in instruction_parts:
            if part.strip():
                instructions.append(part.strip())
    
    schema = {
        "@context": "https://schema.org/",
        "@type": "Recipe",
        "name": recipe.get('title', ''),
        "description": recipe.get('description', ''),
        "author": {
            "@type": "Person",
            "name": recipe.get('author', 'Ketolabben')
        },
        "image": urljoin(request.url_root, recipe.get('featured_image', '')),
        "url": request.url,
        "recipeCategory": recipe.get('category', ''),
        "keywords": recipe.get('tags', ''),
        "recipeYield": f"{recipe.get('servings', '')} porsjoner" if recipe.get('servings') else "",
        "datePublished": recipe.get('created').strftime('%Y-%m-%d') if recipe.get('created') else datetime.now().strftime('%Y-%m-%d')
    }
    
    # Add times if available
    if prep_time_iso:
        schema["prepTime"] = prep_time_iso
    if cook_time_iso:
        schema["cookTime"] = cook_time_iso
    if total_time_iso:
        schema["totalTime"] = total_time_iso
    
    # Add ingredients and instructions
    if ingredients_list:
        schema["recipeIngredient"] = ingredients_list
    
    if instructions:
        schema["recipeInstructions"] = []
        for i, step in enumerate(instructions, 1):
            schema["recipeInstructions"].append({
                "@type": "HowToStep",
                "position": i,
                "text": step
            })
    
    # Add nutrition information if available (for keto recipes)
    if recipe.get('nutrition'):
        schema["nutrition"] = {
            "@type": "NutritionInformation",
            "calories": recipe.get('nutrition', {}).get('calories', ''),
            "carbohydrateContent": recipe.get('nutrition', {}).get('carbs', ''),
            "fatContent": recipe.get('nutrition', {}).get('fat', ''),
            "proteinContent": recipe.get('nutrition', {}).get('protein', '')
        }
    
    return json.dumps(schema, ensure_ascii=False)

def generate_article_schema(article):
    """
    Generate JSON-LD structured data for a blog article
    
    Args:
        article: Article data from CMS
        
    Returns:
        JSON-LD structured data as a string
    """
    schema = {
        "@context": "https://schema.org/",
        "@type": "BlogPosting",
        "headline": article.get('title', ''),
        "description": article.get('description', ''),
        "image": urljoin(request.url_root, article.get('featured_image', '')),
        "author": {
            "@type": "Person",
            "name": article.get('author', 'Ketolabben')
        },
        "publisher": {
            "@type": "Organization",
            "name": "Ketolabben",
            "logo": {
                "@type": "ImageObject",
                "url": urljoin(request.url_root, "/assets/img/logo.png")
            }
        },
        "url": request.url,
        "datePublished": article.get('created').strftime('%Y-%m-%d') if article.get('created') else datetime.now().strftime('%Y-%m-%d'),
        "dateModified": article.get('updated').strftime('%Y-%m-%d') if article.get('updated') else datetime.now().strftime('%Y-%m-%d'),
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": request.url
        }
    }
    
    # Add keywords if available
    if article.get('tags'):
        schema["keywords"] = article.get('tags')
    
    return json.dumps(schema, ensure_ascii=False)

def generate_review_schema(reviews, item_name, item_type="Product"):
    """
    Generate JSON-LD structured data for reviews
    
    Args:
        reviews: List of review data
        item_name: Name of the reviewed item
        item_type: Type of the reviewed item (Product, Book, etc.)
        
    Returns:
        JSON-LD structured data as a string
    """
    if not reviews:
        return None
    
    # Calculate average rating
    total_rating = sum(review.get('rating', 5) for review in reviews)
    avg_rating = total_rating / len(reviews) if reviews else 0
    
    schema = {
        "@context": "https://schema.org/",
        "@type": item_type,
        "name": item_name,
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": round(avg_rating, 1),
            "reviewCount": len(reviews),
            "bestRating": 5,
            "worstRating": 1
        },
        "review": []
    }
    
    for review in reviews:
        schema["review"].append({
            "@type": "Review",
            "reviewRating": {
                "@type": "Rating",
                "ratingValue": review.get('rating', 5),
                "bestRating": 5
            },
            "author": {
                "@type": "Person",
                "name": review.get('author', 'Anonymous')
            },
            "reviewBody": review.get('content', '')
        })
    
    return json.dumps(schema, ensure_ascii=False)

def generate_book_schema(book):
    """
    Generate JSON-LD structured data for a book
    
    Args:
        book: Book data dictionary
        
    Returns:
        JSON-LD structured data as a string
    """
    schema = {
        "@context": "https://schema.org/",
        "@type": "Book",
        "name": book.get('title', ''),
        "description": book.get('description', ''),
        "image": urljoin(request.url_root, book.get('featured_image', '')),
        "url": request.url,
        "author": {
            "@type": "Person",
            "name": book.get('author', 'Ketolabben')
        },
        "publisher": {
            "@type": "Organization",
            "name": "Ketolabben"
        },
        "inLanguage": "no",
        "isbn": book.get('isbn', '')
    }
    
    # Add offers if price is available
    if book.get('price'):
        schema["offers"] = {
            "@type": "Offer",
            "price": book.get('price'),
            "priceCurrency": "NOK",
            "availability": "https://schema.org/InStock",
            "url": request.url
        }
    
    # Add reviews if available
    if book.get('reviews'):
        total_rating = sum(review.get('rating', 5) for review in book.get('reviews'))
        avg_rating = total_rating / len(book.get('reviews')) if book.get('reviews') else 0
        
        schema["aggregateRating"] = {
            "@type": "AggregateRating",
            "ratingValue": round(avg_rating, 1),
            "reviewCount": len(book.get('reviews')),
            "bestRating": 5,
            "worstRating": 1
        }
        
        schema["review"] = []
        for review in book.get('reviews', []):
            schema["review"].append({
                "@type": "Review",
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": review.get('rating', 5),
                    "bestRating": 5
                },
                "author": {
                    "@type": "Person",
                    "name": review.get('author', 'Anonymous')
                },
                "reviewBody": review.get('content', '')
            })
    
    return json.dumps(schema, ensure_ascii=False)

def generate_organization_schema():
    """
    Generate JSON-LD structured data for the organization
    
    Returns:
        JSON-LD structured data as a string
    """
    schema = {
        "@context": "https://schema.org/",
        "@type": "Organization",
        "name": "Ketolabben",
        "url": request.url_root,
        "logo": urljoin(request.url_root, "/assets/img/logo.png"),
        "sameAs": [
            "https://www.facebook.com/ketolabben",
            "https://www.instagram.com/ketolabben"
        ]
    }
    
    return json.dumps(schema, ensure_ascii=False)

def generate_website_schema():
    """
    Generate JSON-LD structured data for the website
    
    Returns:
        JSON-LD structured data as a string
    """
    schema = {
        "@context": "https://schema.org/",
        "@type": "WebSite",
        "name": "Ketolabben",
        "url": request.url_root
    }
    
    return json.dumps(schema, ensure_ascii=False)

def inject_structured_data(html, data_list):
    """
    Inject structured data into HTML using BeautifulSoup for robust parsing
    
    Args:
        html: HTML content
        data_list: List of JSON-LD structured data strings
        
    Returns:
        HTML with structured data injected
    """
    if not data_list:
        return html
    
    # Filter out None values
    data_list = [data for data in data_list if data]
    
    if not data_list:
        return html
    
    try:
        # Import BeautifulSoup
        from bs4 import BeautifulSoup
        
        # Parse the HTML
        soup = BeautifulSoup(html, 'html.parser')
        
        # Create script tags for each JSON-LD data
        for data in data_list:
            if isinstance(data, str) and data:
                # Create a new script tag
                script_tag = soup.new_tag('script', type='application/ld+json')
                script_tag.string = data
                
                # Add the script tag to the head
                if soup.head:
                    soup.head.append(script_tag)
                else:
                    # Create a head tag if it doesn't exist
                    head_tag = soup.new_tag('head')
                    head_tag.append(script_tag)
                    
                    # Add the head tag to the document
                    if soup.html:
                        soup.html.insert(0, head_tag)
                    else:
                        # Create an html tag if it doesn't exist
                        html_tag = soup.new_tag('html')
                        html_tag.append(head_tag)
                        soup.append(html_tag)
        
        # Convert back to string
        return str(soup)
    except ImportError:
        # BeautifulSoup is not available, fall back to regex-based approach
        if hasattr(current_app, 'logger'):
            current_app.logger.warning("BeautifulSoup is not available. Using regex-based approach.")
        
        # Create script tags for each JSON-LD data
        script_tags = []
        for data in data_list:
            if isinstance(data, str) and data:
                script_tags.append(f'<script type="application/ld+json">{data}</script>')
        
        # Join all script tags with proper line breaks
        all_scripts = '\n'.join(script_tags)
        
        # Check if the HTML is already escaped
        if '&lt;html' in html or '&lt;head' in html:
            if hasattr(current_app, 'logger'):
                current_app.logger.warning("HTML content appears to be escaped. Structured data may not be properly injected.")
            return html
        
        # Use regular expressions to find the head tag and insert the scripts
        import re
        
        # Try to find the head tag
        head_match = re.search(r'<head[^>]*>(.*?)</head>', html, re.DOTALL | re.IGNORECASE)
        if head_match:
            # Insert at the end of the head content
            head_content = head_match.group(1)
            new_head = f'<head>{head_content}\n{all_scripts}\n</head>'
            return html.replace(head_match.group(0), new_head)
        
        # If no head tag, try to insert after the opening html tag
        html_match = re.search(r'<html[^>]*>', html, re.IGNORECASE)
        if html_match:
            # Insert after the opening html tag
            html_tag = html_match.group(0)
            return html.replace(html_tag, f'{html_tag}\n<head>\n{all_scripts}\n</head>')
        
        # If no html tag, try to insert before the body tag
        body_match = re.search(r'<body[^>]*>', html, re.IGNORECASE)
        if body_match:
            # Insert before the body tag
            body_tag = body_match.group(0)
            return html.replace(body_tag, f'<head>\n{all_scripts}\n</head>\n{body_tag}')
        
        # If all else fails, just append to the beginning of the document
        return f'<head>\n{all_scripts}\n</head>\n{html}'
    except Exception as e:
        # Log the error and return the original HTML
        if hasattr(current_app, 'logger'):
            current_app.logger.error(f"Error injecting structured data: {str(e)}")
        return html