#!/usr/bin/env python3
"""
List all routes in the Flask application to debug webhook registration
"""
import os
import sys
from flask import Flask

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def list_all_routes():
    """List all routes registered in the Flask application"""
    print("Listing all Flask routes...")
    
    try:
        # Import and create the app
        from modules import create_app
        app = create_app()
        
        print(f"\nTotal routes registered: {len(app.url_map._rules)}")
        print("\nRoutes:")
        print("-" * 80)
        
        webhook_found = False
        
        for rule in app.url_map.iter_rules():
            methods = ', '.join(sorted(rule.methods - {'HEAD', 'OPTIONS'}))
            print(f"{rule.rule:<40} {methods:<20} {rule.endpoint}")
            
            if 'webhook' in rule.rule.lower() or 'webhook' in rule.endpoint.lower():
                webhook_found = True
                print(f"  ^^^ WEBHOOK ROUTE FOUND! ^^^")
        
        print("-" * 80)
        
        if webhook_found:
            print("\n✅ Webhook route is registered!")
        else:
            print("\n❌ Webhook route NOT found in registered routes!")
            
        # Check if webhook blueprint is imported correctly
        try:
            from modules.webhook_stripe import webhook_bp
            print(f"\n✅ Webhook blueprint imported successfully")
            print(f"Blueprint name: {webhook_bp.name}")
            print(f"Blueprint routes: {len(webhook_bp.deferred_functions)}")
        except ImportError as e:
            print(f"\n❌ Failed to import webhook blueprint: {str(e)}")
            
    except Exception as e:
        print(f"Error creating app or listing routes: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Flask Routes Debug")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    list_all_routes()