import importlib.util
import os
import sys

# Add the directory of the current file to the system path
sys.path.insert(0, os.path.dirname(__file__))

# Define the module name and file path
module_name = 'wsgi'
file_path = 'run.py'

# Load the module using importlib
spec = importlib.util.spec_from_file_location(module_name, file_path)
wsgi = importlib.util.module_from_spec(spec)
spec.loader.exec_module(wsgi)

# Access the application attribute from the loaded module
application = wsgi.app