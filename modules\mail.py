import os
import logging
from dotenv import load_dotenv
from flask_mail import Mail, Message
import smtplib
from flask import url_for, current_app

# Explicitly load environment variables
load_dotenv()

def init_mail(app):
    """
    Initialize Flask-Mail with the application context
    
    Args:
        app (Flask): The Flask application instance
    
    Returns:
        Mail: Configured Mail instance
    """
    # Configure mail settings from environment variables
    app.config['MAIL_SERVER'] = os.getenv('MAIL_SERVER', 'localhost')
    app.config['MAIL_PORT'] = int(os.getenv('MAIL_PORT', 587))
    
    # Determine TLS/SSL configuration
    use_tls = os.getenv('MAIL_USE_TLS', 'true').lower() == 'true'
    use_ssl = os.getenv('MAIL_USE_SSL', 'false').lower() == 'true'
    
    app.config['MAIL_USE_TLS'] = use_tls
    app.config['MAIL_USE_SSL'] = use_ssl
    
    app.config['MAIL_USERNAME'] = os.getenv('MAIL_USERNAME', '')
    app.config['MAIL_PASSWORD'] = os.getenv('MAIL_PASSWORD', '')
    
    # Set sender information in config
    sender_name = os.getenv('MAIL_SENDER_NAME', '')
    sender_email = os.getenv('MAIL_SENDER', '')
    
    # Format the sender as "Name <email>"
    formatted_sender = f"{sender_name} <{sender_email}>"
    app.config['MAIL_DEFAULT_SENDER'] = formatted_sender
    
    logging.info(f"Configured sender: {formatted_sender}")
    
    # Additional mail configuration for robustness
    app.config['MAIL_DEBUG'] = True  # Enable debug logging
    app.config['MAIL_SUPPRESS_SEND'] = False  # Actually send emails
    
    # Validate required mail configuration
    required_configs = ['MAIL_SERVER', 'MAIL_USERNAME', 'MAIL_PASSWORD', 'MAIL_SENDER', 'MAIL_SENDER_NAME']
    missing_configs = [config for config in required_configs if not os.getenv(config)]
    
    if missing_configs:
        logging.error(f"Missing mail configurations: {missing_configs}")
        # Log the actual environment variables for debugging
        logging.error("Current environment variables:")
        for key in required_configs:
            logging.error(f"{key}: {os.getenv(key, 'NOT SET')}")
        raise ValueError(f"Missing required mail configurations: {missing_configs}")
    
    # Initialize Mail
    mail = Mail(app)
    logging.info("Mail configuration initialized successfully")
    return mail

def send_contact_form_email(mail, name, email, message, client_ip=None, abuse_confidence_score=None, checkbox=None):
    """
    Send an email from the contact form
    
    Args:
        mail (Mail): Flask-Mail instance
        name (str): Sender's name
        email (str): Sender's email address
        message (str): Message content
        client_ip (str, optional): Client's IP address
        abuse_confidence_score (int, optional): AbuseIPDB confidence score
        checkbox (str, optional): Honeypot checkbox value
    
    Returns:
        bool: True if email sent successfully, False otherwise
    
    Raises:
        Exception: If email sending fails
    """
    import os
    import logging
    from flask_mail import Message
    
    try:
        # Validate required parameters
        if not all([mail, name, email, message]):
            raise ValueError("Missing required parameters: mail, name, email, and message are required")
            
        sender = current_app.config['MAIL_DEFAULT_SENDER']
        recipient = os.getenv("REPLY_TO")
        
        if not recipient:
            raise ValueError("REPLY_TO environment variable is not set")
            
        logging.info(f"Contact form sender: {sender}")
        
        # For contact form, we want replies to go back to the person who filled out the form
        msg = Message(
            "Kontaktskjema - Ny melding",
            sender=sender,
            recipients=[recipient],
            reply_to=email if email else sender  # Fallback to sender if email is somehow None
        )
        
        # Construct email body with optional additional information
        body_parts = [
            f"Fra: {name}",
            f"E-post: {email}",
        ]
        
        # Add optional details if provided
        if client_ip:
            body_parts.append(f"IP: {client_ip}")
        if abuse_confidence_score is not None:
            body_parts.append(f"AbuseIPDB score: {abuse_confidence_score}")
        if checkbox is not None:
            body_parts.append(f"SPAM Honeypot: {checkbox}")
        
        # Add message
        body_parts.extend(["", "Melding:", message])
        
        msg.body = "\n".join(body_parts)
        
        # Send the email
        mail.send(msg)
        
        logging.info(f"Email sent successfully to {os.getenv('REPLY_TO')}")
        return True
    
    except Exception as e:
        logging.error(f"Error sending contact form email: {str(e)}")
        logging.error(f"Error type: {type(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise

def send_newsletter_verification_email(mail, app, email: str, verification_token: str):
    """
    Send newsletter verification email to subscriber
    
    Args:
        mail (Mail): Flask-Mail instance
        app (Flask): Flask application context
        email (str): Recipient email address
        verification_token (str): Unique verification token
    
    Raises:
        Exception: If email sending fails
    """
    logging.info(f"Sending newsletter verification email to: {email}")
    try:
        # Validate required parameters
        if not all([mail, app, email, verification_token]):
            raise ValueError("Missing required parameters: mail, app, email, and verification_token are required")
            
        # Generate verification link
        with app.app_context():
            verification_link = url_for('newsletter.verify_email', token=verification_token, _external=True)
        
        sender = app.config['MAIL_DEFAULT_SENDER']
        logging.info(f"Newsletter verification sender: {sender}")
        
        # Get reply-to from environment variable
        reply_to = os.getenv("REPLY_TO")
        if not reply_to:
            raise ValueError("REPLY_TO environment variable is not set")
        
        # Compose email message with proper sender format
        msg = Message(
            'Bekreft din e-postadresse for nyhetsbrev',
            sender=sender,
            recipients=[email] if email else [],
            reply_to=reply_to
        )
        
        # HTML email body with verification link
        msg.html = f'''
        <!DOCTYPE html>
        <html lang="no">
        <head>
            <meta charset="UTF-8">
            <title>Bekreft nyhetsbrev-abonnement</title>
        </head>
        <body>
            <h2>Bekreft din e-postadresse</h2>
            
            <p>Takk for at du vil abonnere på vårt nyhetsbrev. For å fullføre påmeldingen, vennligst bekreft din e-postadresse ved å klikke på knappen nedenfor:</p>
            
            <p style="text-align: left; margin: 20px 0;">
                <a href="{verification_link}" style="background-color: #4CAF50; color: white; padding: 14px 25px; text-align: center; text-decoration: none; display: inline-block; border-radius: 5px;">Bekreft e-postadresse</a>
            </p>
            
            <p>Hvis du ikke har meldt deg på vårt nyhetsbrev, kan du se bort fra denne e-posten.</p>
            
            <p>Vennlig hilsen,<br>KETOLABBEN</p>
        </body>
        </html>
        '''
        
        # Send the email
        mail.send(msg)
    except Exception as e:
        logging.error(f"Failed to send verification email: {e}")
        import traceback
        logging.error(traceback.format_exc())
        raise




