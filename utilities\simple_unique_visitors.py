#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to get simple, clear unique visitor counts
"""

import sys
import os
from datetime import datetime
from sqlalchemy import func

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.models import db, Analytics
from flask import Flask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    db.init_app(app)
    
    return app

def get_simple_unique_visitors(target_date_str):
    """
    Get simple, clear unique visitor counts using two separate methods
    
    Args:
        target_date_str (str): Date in format YYYY-MM-DD
    
    Returns:
        dict: Clear metrics
    """
    try:
        # Parse the target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d')
        
        # Set start and end of the day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        print(f"Simple unique visitor analysis for {target_date_str}")
        print(f"Date range: {start_date} to {end_date}")
        
        # Method 1: Count unique IP addresses (one visitor per IP per day)
        unique_ips = db.session.query(
            func.count(Analytics.ip_address.distinct())
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        # Method 2: Count unique session IDs (one visitor per session)
        unique_sessions = db.session.query(
            func.count(Analytics.sessionid.distinct())
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        # Total page views for context
        total_pageviews = db.session.query(
            func.count(Analytics.id)
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"\n=== CLEAR METRICS ===")
        print(f"Total page views: {total_pageviews}")
        print(f"Unique visitors by IP address: {unique_ips}")
        print(f"Unique visitors by session ID: {unique_sessions}")
        
        # Calculate engagement metrics
        pages_per_ip = round(total_pageviews / unique_ips, 2) if unique_ips > 0 else 0
        pages_per_session = round(total_pageviews / unique_sessions, 2) if unique_sessions > 0 else 0
        
        print(f"\n=== ENGAGEMENT METRICS ===")
        print(f"Average pages per IP: {pages_per_ip}")
        print(f"Average pages per session: {pages_per_session}")
        
        # Show the difference
        session_ip_ratio = round(unique_sessions / unique_ips, 2) if unique_ips > 0 else 0
        print(f"Sessions per IP ratio: {session_ip_ratio}")
        
        if session_ip_ratio > 1:
            print(f"→ This means some IP addresses generated multiple sessions")
            print(f"  (could be multiple people on same network, or session resets)")
        elif session_ip_ratio < 1:
            print(f"→ This means some sessions used multiple IP addresses")
            print(f"  (mobile users switching networks, VPN changes)")
        else:
            print(f"→ Perfect 1:1 ratio between sessions and IPs")
        
        return {
            'date': target_date_str,
            'total_pageviews': total_pageviews,
            'unique_visitors_by_ip': unique_ips,
            'unique_visitors_by_session': unique_sessions,
            'pages_per_ip': pages_per_ip,
            'pages_per_session': pages_per_session,
            'sessions_per_ip_ratio': session_ip_ratio
        }
        
    except ValueError as e:
        print(f"Error parsing date: {e}")
        print("Please use format YYYY-MM-DD (e.g., 2025-06-16)")
        return None
    except Exception as e:
        print(f"Error querying database: {e}")
        return None

def create_simple_unique_visitor_function():
    """
    Create a simple function that can replace the complex robust one
    
    Returns:
        str: SQL expression for simple unique visitor count
    """
    print("\n=== PROPOSED REPLACEMENT FUNCTIONS ===")
    print("Option 1 - IP-based unique visitors (conservative):")
    print("  COUNT(DISTINCT ip_address)")
    print("  → Counts each IP address only once per day")
    print("  → Good for: Estimating actual people/locations")
    print("  → Issue: Undercounts if multiple people share same IP")
    
    print("\nOption 2 - Session-based unique visitors (liberal):")
    print("  COUNT(DISTINCT sessionid)")
    print("  → Counts each session only once")
    print("  → Good for: Tracking user engagement/behavior")
    print("  → Issue: Overcounts if users clear cookies/restart browsers")
    
    print("\nOption 3 - Hybrid approach (recommended):")
    print("  Use both metrics and report them separately:")
    print("  - 'Unique IPs': COUNT(DISTINCT ip_address)")
    print("  - 'Unique Sessions': COUNT(DISTINCT sessionid)")
    print("  → Most transparent and informative approach")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python simple_unique_visitors.py YYYY-MM-DD")
        print("Example: python simple_unique_visitors.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run analysis
    app = create_app()
    
    with app.app_context():
        results = get_simple_unique_visitors(target_date)
        
        if results:
            create_simple_unique_visitor_function()
            
            print(f"\n=== RECOMMENDATION ===")
            print(f"For {target_date}:")
            print(f"- Report 'Unique IPs': {results['unique_visitors_by_ip']}")
            print(f"- Report 'Unique Sessions': {results['unique_visitors_by_session']}")
            print(f"- Both metrics are clear and meaningful")
        else:
            print("Failed to analyze unique visitors.")
            sys.exit(1)