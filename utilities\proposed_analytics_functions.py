#!/usr/bin/env python3
"""
Proposed replacement functions for the complex robust unique visitor count
"""

from sqlalchemy import func
from .models import Analytics

def get_unique_visitors_by_ip():
    """
    Simple function to count unique visitors by IP address
    
    Returns:
        SQLAlchemy expression: COUNT(DISTINCT ip_address)
    """
    return func.count(Analytics.ip_address.distinct())

def get_unique_visitors_by_session():
    """
    Simple function to count unique visitors by session ID
    
    Returns:
        SQLAlchemy expression: COUNT(DISTINCT sessionid)
    """
    return func.count(Analytics.sessionid.distinct())

def get_unique_visitors_hybrid():
    """
    Get both IP-based and session-based unique visitor counts
    
    Returns:
        dict: Both metrics for comprehensive analysis
    """
    return {
        'unique_ips': get_unique_visitors_by_ip(),
        'unique_sessions': get_unique_visitors_by_session()
    }

def get_conservative_unique_visitors(db_session, filters):
    """
    Get conservative unique visitor count using MIN(unique_ips, unique_sessions)
    
    This approach:
    1. Counts unique IP addresses
    2. Counts unique session IDs  
    3. Returns the smaller number (most conservative estimate)
    
    Args:
        db_session: Database session
        filters: List of filter conditions (e.g., date range, bot exclusion)
    
    Returns:
        int: Conservative unique visitor count
    """
    # Get unique IP count
    unique_ips = db_session.query(
        func.count(Analytics.ip_address.distinct())
    ).filter(*filters).scalar()
    
    # Get unique session count
    unique_sessions = db_session.query(
        func.count(Analytics.sessionid.distinct())
    ).filter(*filters).scalar()
    
    # Return the minimum (most conservative)
    return min(unique_ips, unique_sessions)

# Example usage in your analytics_process.py:
"""
# Replace this complex code:
total_unique_visitors = db.session.query(
    get_robust_unique_visitor_count(include_date_in_key=True)
).filter(
    ~Analytics.is_bot,
    Analytics.timestamp >= start_date_inclusive,
    Analytics.timestamp <= end_date_inclusive
).scalar()

# With this simple, conservative approach:
filters = [
    ~Analytics.is_bot,
    Analytics.timestamp >= start_date_inclusive,
    Analytics.timestamp <= end_date_inclusive
]

conservative_unique_visitors = get_conservative_unique_visitors(db.session, filters)

# Or if you want both metrics separately:
unique_ips = db.session.query(
    get_unique_visitors_by_ip()
).filter(*filters).scalar()

unique_sessions = db.session.query(
    get_unique_visitors_by_session()
).filter(*filters).scalar()

# Report the results:
print(f"Conservative unique visitors: {conservative_unique_visitors}")
print(f"Unique visitors (by IP): {unique_ips}")
print(f"Unique visitors (by session): {unique_sessions}")
"""