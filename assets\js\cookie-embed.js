(() => {
    // Function to delete all cookies
    function deleteAllCookies() {
        // Get all cookies
        const cookies = document.cookie.split(';');

        // Loop through each cookie and delete it
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i];
            const eqPos = cookie.indexOf('=');
            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
            
            // Delete the cookie by setting an expired date
            document.cookie = `${name.trim()}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`;
        }

        // Clear localStorage
        const userConsent = JSON.parse(localStorage.getItem('userConsent') || '{}');
        localStorage.removeItem('userConsent');

        // Send revoke request to server if userConsentId exists
        if (userConsent.userConsentId) {
            fetch(`/api/cookie-consent/${userConsent.userConsentId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                console.log('Consent revocation response:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Consent revocation result:', data);
            })
            .catch(error => {
                console.error('Error revoking consent:', error);
            });
        }

        // Reload the page to reset everything
        window.location.reload();
    }

    // Create and insert the consent display
    function createConsentDisplay() {
        let userConsent = {};

        // Get from localStorage
        const localStorageValue = localStorage.getItem('userConsent');
        if (localStorageValue) {
            try {
                userConsent = JSON.parse(localStorageValue);
            } catch (e) {
                // Silently handle parsing errors
            }
        }

        // Dynamically generate consent items
        const consentItems = Object.entries(userConsent)
            .filter(([key]) => 
                key !== 'timestamp' && 
                key !== 'userConsentId' && 
                typeof userConsent[key] === 'boolean'
            )
            .map(([key, value]) => {
                // Convert camelCase to Title Case
                const formattedKey = key
                    .replace(/([A-Z])/g, ' $1')
                    .replace(/^./, str => str.toUpperCase());
                
                return `
                    <div class="consent-item">
                        ${formattedKey}: 
                        <span class="consent-status ${value ? 'status-accepted' : 'status-rejected'}">
                            ${value ? 'Akseptert' : 'Ikke akseptert'}
                        </span>
                    </div>
                `;
            })
            .join('');

        const container = document.createElement('div');
        container.className = 'consent-display';

        container.innerHTML = `
            <h3>Samtykke til informasjonskapsler</h3>
            <div class="consent-items">
                ${consentItems}
            </div>
            <div class="consent-meta">
                Samtykke gitt: ${userConsent.timestamp ? new Date(userConsent.timestamp).toLocaleString('nb-NO') : 'Ikke registrert'}
                <br>
                Samtykke-ID: ${userConsent.userConsentId || 'Ikke tilgjengelig'}
            </div>
            <button class="revoke-consent-btn">Tilbakekall samtykke</button>
        `;

        // Add event listener to revoke consent button
        const revokeButton = container.querySelector('.revoke-consent-btn');
        revokeButton.addEventListener('click', () => {
            if (confirm('Er du sikker på at du vil tilbakekalle alle informasjonskapsler? Dette vil fjerne alle dine innstillinger.')) {
                deleteAllCookies();
            }
        });

        // Add styles for the button
        const style = document.createElement('style');
        style.textContent = `
            .consent-display {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 15px;
                margin: 15px 0;
                font-size: 14px;
            }

            .revoke-consent-btn {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
                font-size: 14px;
            }

            .revoke-consent-btn:hover {
                background-color: #c82333;
            }

            .consent-item {
                margin-bottom: 5px;
            }

            .consent-status {
                font-weight: bold;
            }

            .status-accepted {
                color: #28a745;
            }

            .status-rejected {
                color: #dc3545;
            }
        `;

        document.head.appendChild(style);
        document.currentScript.parentElement.insertBefore(container, document.currentScript);
    }

    // Initialize the display
    createConsentDisplay();
})();