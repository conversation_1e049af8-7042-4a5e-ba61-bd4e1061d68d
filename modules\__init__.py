import os
import warnings
import logging
from flask import Flask, send_from_directory, session, g, request
from flask_login import Login<PERSON>anager
from flask_session import Session
from dotenv import load_dotenv
from .routes import init_routes
from .security import init_security, generate_csrf_token, init_security_routes
from .models import db, Auth, init_db
from .logger import log_performance
from .cms import cms, delete_post
from .ckeditor import ckeditor
from .login import init_login_routes
from .contact import init_contact_routes
from .mail import init_mail
from .newsletter import newsletter_bp
from .ai import ai_bp
from .imgresize import imgresize_bp
from .analytics_collect import init_analytics, register_analytics_routes
from .analytics_dashboard import analytics_dashboard_bp
from .seo_sitemap import init_sitemap_routes
from .optimization import register_module as register_optimization_module
from .payment import create_checkout_session, payment_bp
from .download import download_bp
from .cookie import cookie_bp
from .webhook_stripe import webhook_bp
from datetime import timedelta
import uuid
from sqlalchemy import event
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect

# Set up logging
logging.basicConfig(level=logging.WARNING)  # Set default level to WARNING
logger = logging.getLogger(__name__)

# Configure application logger specifically
app_logger = logging.getLogger('applogger')
app_logger.setLevel(logging.INFO)  # Set to INFO for important application events

def create_app():
    """Create and configure the Flask application"""
    logger.info("Creating Flask application")

    # Load environment variables
    load_dotenv()

    # Suppress specific warnings
    warnings.filterwarnings('ignore', message='Using the in-memory storage')

    # Determine template folder path
    template_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'templates'))

    # Log template folder path for debugging
    logger.info(f"Template folder path: {template_path}")

    # Check if template folder exists
    if not os.path.exists(template_path):
        logger.error(f"Template folder does not exist: {template_path}")
        # Try to create a log entry in a direct file
        try:
            log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
            os.makedirs(log_dir, exist_ok=True)
            with open(os.path.join(log_dir, 'template_error.log'), 'a') as f:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"{timestamp} - Template folder does not exist: {template_path}\n")
        except Exception as e:
            print(f"Failed to write template error log: {str(e)}")
    else:
        # Log the contents of the template folder
        try:
            template_files = os.listdir(template_path)
            logger.info(f"Template folder contains {len(template_files)} files: {', '.join(template_files)}")
        except Exception as e:
            logger.error(f"Error listing template folder contents: {str(e)}")

    # Create Flask app with custom folder configurations
    app = Flask(__name__,
                static_folder='../assets',  # Set static folder to 'assets'
                static_url_path='/assets',  # Set static URL path
                template_folder=template_path)

    # Register payment blueprint
    logger.info("Registering payment blueprint")
    app.register_blueprint(payment_bp)

    # Register download blueprint
    logger.info("Registering download blueprint")
    app.register_blueprint(download_bp)
    
    # Register webhook blueprint
    logger.info("Registering webhook blueprint")
    app.register_blueprint(webhook_bp)

    # Initialize security features (CORS, CSP, and other security headers)
    init_security(app)

    # Configure the app
    secret_key = os.getenv('SECRET_KEY', 'dev-key')
    app.config.update(
        DEBUG=os.getenv('FLASK_ENV') == 'development',
        SQLALCHEMY_DATABASE_URI=os.getenv('DATABASE_URL', f'sqlite:///{os.path.join(os.path.dirname(__file__), "..", "instance", "core.db")}'),
        SQLALCHEMY_TRACK_MODIFICATIONS=False,
        SQLALCHEMY_POOL_SIZE=10,
        SQLALCHEMY_MAX_OVERFLOW=20,
        SQLALCHEMY_POOL_TIMEOUT=30,
        SQLALCHEMY_POOL_RECYCLE=3600,
        SQLALCHEMY_ENGINE_OPTIONS={
            'connect_args': {
                'timeout': 30,
                'isolation_level': 'IMMEDIATE',  # Valid SQLite isolation level
                'cached_statements': 100,
            }
        },

        # Enhanced Session Configuration
        SECRET_KEY=secret_key,
        SESSION_TYPE='filesystem',  # Store sessions on filesystem
        SESSION_PERMANENT=True,  # Wheter sessions expire on browser close
        PERMANENT_SESSION_LIFETIME=timedelta(hours=8),  # Max time until session expires
        SESSION_FILE_DIR=os.path.join(os.path.dirname(__file__), '..', 'sessions'),  # Dedicated session storage

        # Cookie Security
        SESSION_COOKIE_HTTPONLY=False,  # Allow JavaScript access
        SESSION_COOKIE_SECURE=True,
        SESSION_COOKIE_SAMESITE='Lax',
        SESSION_COOKIE_NAME='ketolabben_session',

        # Additional Security
        WTF_CSRF_ENABLED=True,
        SESSION_PROTECTION='strong',

        # Prevent session refresh on each request
        SESSION_REFRESH_EACH_REQUEST=False,

        OPENAI_API_KEY=os.getenv('OPENAI_API_KEY'),
    )

    # Ensure session directory exists
    os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

    # Initialize Flask-Session
    Session(app)

    # Ensure a session ID is generated for each request if not present
    @app.before_request
    def generate_session_id():
        # Try to get session ID from existing cookie first
        session_id = request.cookies.get('ketolabben_session')

        # If no cookie exists, generate a new UUID
        if not session_id:
            session_id = str(uuid.uuid4())

        # Set the session ID in Flask session
        session['session_id'] = session_id

    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'Du må logge inn for å få tilgang til denne siden.'
    login_manager.login_message_category = 'error'

    @login_manager.user_loader
    def load_user(user_id):
        return Auth.query.get(int(user_id))

    # Add CSRF token generation to template context
    app.jinja_env.globals['csrf_token'] = generate_csrf_token

    # Initialize CSRF protection and exempt some routes
    csrf = CSRFProtect(app)
    csrf.exempt(delete_post)
    csrf.exempt(create_checkout_session)
    csrf.exempt(cookie_bp)
    csrf.exempt(webhook_bp)

    # Import browser_capability_bp to exempt it from CSRF protection
    from .analytics_collect import browser_capability_bp
    csrf.exempt(browser_capability_bp)



    # Initialize extensions and routes
    db.init_app(app)

    # Initialize security features
    init_security(app)

    # Initialize the database and apply optimizations
    with app.app_context():
        init_db()
        logger.info("Database initialized with optimizations")

        @event.listens_for(db.engine, 'connect')
        def optimize_sqlite(dbapi_connection, connection_record):
            # Enable WAL mode
            dbapi_connection.execute('PRAGMA journal_mode=WAL')
            # Memory-mapped I/O for better performance
            dbapi_connection.execute('PRAGMA mmap_size=30000000000')
            # Larger page cache for better performance
            dbapi_connection.execute('PRAGMA page_size=4096')
            dbapi_connection.execute('PRAGMA cache_size=-2000')  # 2MB cache
            # Other performance optimizations
            dbapi_connection.execute('PRAGMA synchronous=NORMAL')
            dbapi_connection.execute('PRAGMA temp_store=MEMORY')
            dbapi_connection.execute('PRAGMA busy_timeout=60000')

        logger.info("SQLite optimizations applied")

    # Initialize Flask-Migrate
    migrate = Migrate(app, db)
    logger.info("Flask-Migrate initialized")

    # Rest of your blueprint registrations...
    logger.info("Registering other blueprints")
    app.register_blueprint(newsletter_bp)
    app.register_blueprint(cms)
    app.register_blueprint(ckeditor)
    app.register_blueprint(ai_bp)
    app.register_blueprint(imgresize_bp, url_prefix='/api')
    app.register_blueprint(analytics_dashboard_bp)
    app.register_blueprint(cookie_bp)

    # Initialize routes after blueprints
    logger.info("Initializing routes")
    init_routes(app)
    init_login_routes(app)
    init_contact_routes(app, init_mail(app))
    init_analytics(app)
    register_analytics_routes(app)

    # Initialize security routes
    from .security import init_security_routes
    init_security_routes(app)

    # Initialize SEO sitemap routes
    init_sitemap_routes(app)

    # Register optimization module
    register_optimization_module(app)

    # Add route to serve assets
    @app.route('/assets/<path:path>')
    def serve_assets(path):
        """Serve static assets with proper MIME types."""
        mime_types = {
            '.css': 'text/css',
            '.js': 'application/javascript',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon',
            '.woff': 'font/woff',
            '.woff2': 'font/woff2',
            '.ttf': 'font/ttf',
            '.eot': 'application/vnd.ms-fontobject',
            '.webp': 'image/webp',
            '.avif': 'image/avif',
            '.heic': 'image/heic',
            '.heif': 'image/heif'
        }

        # Get the file extension
        _, ext = os.path.splitext(path)

        # Get the MIME type based on file extension
        mime_type = mime_types.get(ext.lower(), None)

        return send_from_directory(
            os.path.join(app.root_path, '..', 'assets'),
            path,
            mimetype=mime_type
        )

    return app
