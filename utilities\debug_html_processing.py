import sys
import os
import re

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from modules.structured_data import inject_structured_data

def debug_html_injection(html_file=None, html_content=None):
    """
    Debug HTML injection by showing how structured data would be injected
    
    Args:
        html_file: Path to HTML file to test
        html_content: HTML content to test (alternative to html_file)
    """
    # Get HTML content
    if html_file:
        with open(html_file, 'r', encoding='utf-8') as f:
            html = f.read()
    elif html_content:
        html = html_content
    else:
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Page</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>Test Content</h1>
        </body>
        </html>
        """
    
    # Create test structured data
    test_data = [
        '{"@context": "https://schema.org/", "@type": "Organization", "name": "Test Organization"}',
        '{"@context": "https://schema.org/", "@type": "WebSite", "name": "Test Website"}'
    ]
    
    # Inject structured data
    processed = inject_structured_data(html, test_data)
    
    # Print original HTML
    print("Original HTML:")
    print("-" * 80)
    print(html)
    print("-" * 80)
    
    # Print processed HTML
    print("\nProcessed HTML:")
    print("-" * 80)
    print(processed)
    print("-" * 80)
    
    # Check if structured data was injected
    if '<script type="application/ld+json">' in processed:
        print("\nStructured data was successfully injected.")
        
        # Check where it was injected
        if '</head>' in html:
            head_pos = processed.find('</head>')
            script_pos = processed.find('<script type="application/ld+json">')
            if script_pos < head_pos:
                print("Structured data was injected before the closing head tag.")
            else:
                print("WARNING: Structured data was NOT injected before the closing head tag.")
        else:
            print("No head tag found in the HTML.")
    else:
        print("\nWARNING: Structured data was NOT injected.")
    
    # Check for HTML escaping issues
    if '&lt;script' in processed:
        print("\nWARNING: HTML escaping detected. Script tags may be escaped.")
    
    # Check for malformed HTML
    if processed.count('<html') != processed.count('</html>'):
        print("\nWARNING: Malformed HTML detected. HTML tags are not balanced.")

if __name__ == '__main__':
    # Test with sample HTML
    debug_html_injection()
    
    # Test with a real HTML file if provided
    if len(sys.argv) > 1:
        debug_html_injection(html_file=sys.argv[1])