import sys
import os
import requests
from bs4 import BeautifulSoup

def test_html_response(url):
    """
    Test if structured data is properly injected into HTML
    
    Args:
        url: URL to test
    """
    print(f"Testing URL: {url}")
    
    try:
        # Make a request to the URL
        response = requests.get(url, verify=False)
        
        # Check if the response is HTML
        if 'text/html' not in response.headers.get('Content-Type', ''):
            print("Response is not HTML.")
            return
        
        # Get the HTML content
        html = response.text
        
        # Check if the HTML contains structured data
        if '<script type="application/ld+json">' in html:
            print("Structured data found in HTML.")
            
            # Parse the HTML
            soup = BeautifulSoup(html, 'html.parser')
            
            # Find all structured data scripts
            scripts = soup.find_all('script', type='application/ld+json')
            
            print(f"Found {len(scripts)} structured data scripts:")
            for i, script in enumerate(scripts, 1):
                print(f"\nScript {i}:")
                print(script.string)
        else:
            print("No structured data found in HTML.")
            
            # Check if the HTML contains escaped structured data
            if '&lt;script type=&quot;application/ld+json&quot;&gt;' in html:
                print("Found escaped structured data. HTML might be double-escaped.")
            
            # Check if the HTML is malformed
            if '<html' not in html.lower():
                print("HTML appears to be malformed. No <html> tag found.")
            
            # Check if the HTML has a head tag
            if '</head>' not in html.lower():
                print("HTML appears to be malformed. No </head> tag found.")
    
    except Exception as e:
        print(f"Error testing URL: {str(e)}")

if __name__ == '__main__':
    # Test the local development server
    test_html_response('https://127.0.0.1:8282/')
    
    # Test a specific URL if provided
    if len(sys.argv) > 1:
        test_html_response(sys.argv[1])