import os, sys
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

from modules.models import Auth, db
from flask import Flask

def create_user(username, password):
    # Create a minimal Flask app for database context
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(parent_dir, 'instance', 'core.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)

    with app.app_context():
        # Use the Auth model's create_user method
        new_user = Auth.create_user(db.session, username, password)
        if new_user:
            print(f"User '{username}' created successfully.")
        else:
            print(f"Failed to create user '{username}'.")

if __name__ == "__main__":
    username = input("Enter username: ")
    password = input("Enter password: ")
    create_user(username, password)