#!/usr/bin/env python3
"""
Script to debug what the robust unique visitor function is actually counting
"""

import sys
import os
from datetime import datetime
from sqlalchemy import func, case
from collections import Counter

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.models import db, Analytics
from modules.analytics_process import get_robust_unique_visitor_count
from flask import Flask

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Database configuration
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'core.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    db.init_app(app)
    
    return app

def debug_robust_function(target_date_str):
    """
    Debug what the robust unique visitor function is actually counting
    """
    try:
        # Parse the target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d')
        
        # Set start and end of the day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        print(f"Debugging robust function for {target_date_str}")
        print(f"Date range: {start_date} to {end_date}")
        
        # Get the actual identifiers that the robust function creates
        session_identifier = func.coalesce(Analytics.sessionid, 'ZZZZ_NO_SESSION')
        ip_identifier = 'IP:' + Analytics.ip_address
        
        # Get all the composite identifiers that would be created
        composite_identifiers = db.session.query(
            Analytics.sessionid,
            Analytics.ip_address,
            session_identifier.label('session_id'),
            ip_identifier.label('ip_id'),
            case(
                (session_identifier < ip_identifier, session_identifier),
                else_=ip_identifier
            ).label('chosen_identifier')
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).all()
        
        print(f"\nTotal records: {len(composite_identifiers)}")
        
        # Count unique chosen identifiers
        chosen_identifiers = [row.chosen_identifier for row in composite_identifiers]
        unique_chosen = set(chosen_identifiers)
        
        print(f"Unique chosen identifiers: {len(unique_chosen)}")
        
        # Show some examples
        print(f"\nFirst 10 examples of identifier selection:")
        for i, row in enumerate(composite_identifiers[:10]):
            print(f"  {i+1}. Session: {row.sessionid[:8]}..., IP: {row.ip_address}")
            print(f"      Session ID: {row.session_id[:20]}...")
            print(f"      IP ID: {row.ip_id}")
            print(f"      Chosen: {row.chosen_identifier[:20]}...")
            print()
        
        # Count how many times each identifier type is chosen
        session_chosen = sum(1 for id in chosen_identifiers if not id.startswith('IP:'))
        ip_chosen = sum(1 for id in chosen_identifiers if id.startswith('IP:'))
        
        print(f"Times session ID was chosen: {session_chosen}")
        print(f"Times IP ID was chosen: {ip_chosen}")
        
        # Show the actual robust count
        robust_count = db.session.query(
            get_robust_unique_visitor_count(include_date_in_key=False)
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"\nRobust unique visitor count: {robust_count}")
        print(f"Manual count of unique identifiers: {len(unique_chosen)}")
        
        # Show unique IP addresses for comparison
        unique_ips = db.session.query(
            func.count(Analytics.ip_address.distinct())
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"Unique IP addresses: {unique_ips}")
        
        # Show unique sessions for comparison
        unique_sessions = db.session.query(
            func.count(Analytics.sessionid.distinct())
        ).filter(
            ~Analytics.is_bot,
            Analytics.timestamp >= start_date,
            Analytics.timestamp <= end_date
        ).scalar()
        
        print(f"Unique sessions: {unique_sessions}")
        
        return {
            'robust_count': robust_count,
            'unique_ips': unique_ips,
            'unique_sessions': unique_sessions,
            'manual_count': len(unique_chosen)
        }
        
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python debug_robust_function.py YYYY-MM-DD")
        print("Example: python debug_robust_function.py 2025-06-16")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Create Flask app and run debug
    app = create_app()
    
    with app.app_context():
        results = debug_robust_function(target_date)
        
        if results is None:
            print("Failed to debug robust function.")
            sys.exit(1)