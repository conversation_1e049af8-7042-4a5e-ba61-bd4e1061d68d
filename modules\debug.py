import functools
import logging
import traceback
from flask import flash, redirect, url_for
from typing import Callable, Any

# Configure global logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='app.log',
    filemode='a'
)

class AppError(Exception):
    """Base class for application-specific exceptions"""
    def __init__(self, message, error_type='generic', redirect_url=None):
        super().__init__(message)
        self.error_type = error_type
        self.redirect_url = redirect_url

def log_error(func: Callable) -> Callable:
    """
    Decorator to log errors with additional context
    
    Args:
        func (Callable): Function to be decorated
    
    Returns:
        Callable: Wrapped function with error logging
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        try:
            return func(*args, **kwargs)
        except AppError as e:
            # Handle application-specific errors
            logger.error(f"App Error in {func.__name__}: {str(e)}")
            if e.redirect_url:
                flash(str(e), f'error-{e.error_type}')
                return redirect(url_for(e.redirect_url))
            raise
        except Exception as e:
            # Log unexpected errors
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    return wrapper

def sanitize_input(value: Any, max_length: int = 500, default: Any = '') -> Any:
    """
    Sanitize and validate input values
    
    Args:
        value (Any): Input value to sanitize
        max_length (int): Maximum allowed length
        default (Any): Default value if input is invalid
    
    Returns:
        Any: Sanitized input value
    """
    if value is None:
        return default
    
    # Convert to string and truncate
    str_value = str(value)[:max_length]
    
    # Basic sanitization (expand as needed)
    return str_value.strip()

def validate_email(email: str) -> bool:
    """
    Basic email validation
    
    Args:
        email (str): Email address to validate
    
    Returns:
        bool: True if email is valid, False otherwise
    """
    import re
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_regex, email) is not None

def safe_execute(func: Callable, *args, **kwargs) -> tuple:
    """
    Safely execute a function and handle potential errors
    
    Args:
        func (Callable): Function to execute
        *args: Positional arguments
        **kwargs: Keyword arguments
    
    Returns:
        tuple: (success, result/error)
    """
    try:
        result = func(*args, **kwargs)
        return True, result
    except Exception as e:
        logging.error(f"Error in safe_execute: {str(e)}")
        logging.error(traceback.format_exc())
        return False, e

def get_client_ip(request) -> str:
    """
    Retrieve client IP address safely
    
    Args:
        request: Flask request object
    
    Returns:
        str: Client IP address
    """
    return request.headers.get("X-Forwarded-For", request.remote_addr)
