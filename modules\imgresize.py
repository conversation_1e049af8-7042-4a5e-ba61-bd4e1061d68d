from PIL import Image, ImageOps
import pillow_avif
import os
import hashlib
import re
from bs4 import BeautifulSoup
from markupsafe import Markup
from flask import Blueprint, send_file, request, current_app, Response
from typing import Optional, Tuple
from functools import lru_cache

# Create blueprint
imgresize_bp = Blueprint('imgresize', __name__, url_prefix='/api')

# Constants
ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.gif', '.webp', '.png', '.avif', '.svg'}
CACHE_DIR = None

def get_cache_dir():
    """Get the cache directory path"""
    global CACHE_DIR
    if CACHE_DIR is None:
        CACHE_DIR = os.path.abspath(os.path.join(current_app.static_folder, 'img', 'cache'))
        os.makedirs(CACHE_DIR, exist_ok=True)
    return CACHE_DIR

def find_image(image_name: str) -> Optional[str]:
    """Find image in static directories"""
    # Check static/img directory
    static_path = os.path.join(current_app.static_folder, 'img', image_name)
    if os.path.exists(static_path):
        return static_path
    
    # Check assets/img directory
    assets_path = os.path.join(current_app.root_path, '..', 'assets', 'img', image_name)
    if os.path.exists(assets_path):
        return assets_path
    
    return None

def get_cached_path(image_path: str, width: int, quality: int = 90) -> str:
    """Generate cache path for image"""
    filename = os.path.basename(image_path)
    base, ext = os.path.splitext(filename)
    cache_key = hashlib.md5(f"{base}-{width}-{quality}".encode()).hexdigest()
    return os.path.join(get_cache_dir(), f"{base}_{cache_key}{ext}")

def optimize_image(image_path: str, width: int, quality: int = 90) -> Optional[str]:
    """Optimize image with given width and quality"""
    try:
        # Get cache path
        cached_path = get_cached_path(image_path, width, quality)
        
        # Return cached version if exists
        if os.path.exists(cached_path):
            return cached_path
        
        # Open and resize image
        with Image.open(image_path) as img:
            # Calculate height maintaining aspect ratio
            ratio = img.height / img.width
            height = int(width * ratio)
            
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                background = Image.new('RGB', img.size, 'white')
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1])
                img = background
            
            # High quality resize
            resized = img.resize((width, height), Image.Resampling.LANCZOS)
            
            # Save optimized version
            resized.save(cached_path, quality=quality, optimize=True)
            return cached_path
            
    except Exception as e:
        current_app.logger.error(f"Failed to optimize image: {e}")
        return None

def create_placeholder_svg(width: int, height: int) -> str:
    """Create a placeholder SVG with given dimensions"""
    return f'''<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f8f9fa"/>
        <text x="50%" y="50%" font-family="Arial" font-size="12" fill="#adb5bd" 
              text-anchor="middle" dominant-baseline="middle">Loading...</text>
    </svg>'''

@imgresize_bp.app_template_filter('process_images')
def process_images(html_content):
    """Process all img tags in HTML content to use placeholders"""
    if not html_content:
        return html_content
        
    soup = BeautifulSoup(html_content, 'html.parser')
    for img in soup.find_all('img'):
        src = img.get('src')
        if not src:
            continue
            
        # Skip SVG images
        if src.lower().endswith('.svg'):
            continue
            
        # Get image name
        image_name = src.split('/')[-1]
        
        # Create container div
        container = soup.new_tag('div')
        container['class'] = 'image-container'
        img.wrap(container)
        
        # Store original src in data attribute
        img['data-src'] = src
        # Replace src with placeholder
        img['src'] = f'/api/placeholder/{image_name}'
        
    return Markup(str(soup))

@imgresize_bp.route('/placeholder/<path:image_name>')
def placeholder_image(image_name):
    """Serve a placeholder SVG for the image"""
    try:
        # Get dimensions from query parameters
        width = request.args.get('width', type=int)
        height = request.args.get('height', type=int)
        
        # Find original image
        img_path = find_image(image_name)
        if not img_path:
            return "Image not found", 404
            
        # If it's an SVG, serve it directly
        if img_path.lower().endswith('.svg'):
            return send_file(img_path, mimetype='image/svg+xml')
            
        # If no dimensions provided, get from original image
        if not width or not height:
            with Image.open(img_path) as img:
                width, height = img.size
            
        # Create SVG placeholder
        svg = create_placeholder_svg(width, height)
        
        # Return SVG with proper content type
        return Response(svg, mimetype='image/svg+xml')
        
    except Exception as e:
        current_app.logger.error(f"Failed to create placeholder: {e}")
        return "Failed to create placeholder", 500

@imgresize_bp.route('/optimize_image')
def optimize_image_route():
    """Handle image optimization request"""
    try:
        # Get parameters
        file_name = request.args.get('file_name')
        width = request.args.get('width', type=int)
        quality = request.args.get('quality', 90, type=int)
        aspect_ratio = request.args.get('aspect_ratio', False, type=bool)
        
        if not file_name or not width:
            return "Missing required parameters", 400
            
        # Find image
        image_path = find_image(file_name)
        if not image_path:
            return "Image not found", 404
            
        # Get cached path
        cached_path = get_cached_path(image_path, width, quality)
        
        # If cached version exists, serve it
        if os.path.exists(cached_path):
            return send_file(cached_path)
            
        # Optimize image
        try:
            with Image.open(image_path) as img:
                # Calculate height to maintain aspect ratio if requested
                if aspect_ratio:
                    ratio = img.height / img.width
                    height = int(width * ratio)
                    width = int(width)
                    
                # Resize and optimize
                optimized = optimize_image(image_path, width, quality)
                if optimized:
                    return send_file(optimized)
                    
        except Exception as e:
            current_app.logger.error(f"Failed to optimize image: {e}")
            # If optimization fails, serve original
            return send_file(image_path)
            
    except Exception as e:
        current_app.logger.error(f"Failed to handle optimize request: {e}")
        return "Failed to optimize image", 500
