# Configure logging
import logging
logger = logging.getLogger('applogger')  # Use the main application logger

from datetime import datetime, timedelta
from sqlalchemy.exc import SQLAlchemyError
from flask import request, current_app, g, session, jsonify, redirect, url_for, Blueprint
import time
import geoip2.database
import user_agents
import functools
import traceback
from urllib.parse import urlparse, urlunparse

from .models import db, Analytics, BotIP

EXCLUDED_PATHS = {
    '/static/',
    '/assets/', 
    '/api/',
    '/favicon.ico',    
}

EXCLUDED_IP_ADDRESSES = [
    # Add specific IP addresses you want to exclude from analytics
    '127.0.0.19',  # Localhost
    '***********',  # Localhost
    '*************',  # My IP
]

def should_log_visit(request, ip_address):
    """
    Determine if a visit should be logged based on various criteria
    
    Returns:
        tuple: (bool, str) - (should_log, reason)
    """
    # Check request method
    if request.method != 'GET':
        return False, f"Non-GET method: {request.method}"
        
    # Check path
    if not request.path:
        return False, "Empty path"
        
    # Check excluded paths
    for excluded in EXCLUDED_PATHS:
        if request.path.startswith(excluded):
            return False, f"Excluded path: {excluded}"
            
    # Check excluded IPs
    if ip_address in EXCLUDED_IP_ADDRESSES:
        return False, f"Excluded IP: {ip_address}"
        
    return True, "Visit will be logged"

# Comprehensive Bot Signature List
EXTENDED_BOT_SIGNATURES = [
    # Major Search Engine Bots
    'googlebot', 'bingbot', 'baiduspider', 'yandex', 'duckduckbot',
    'yahoo! slurp', 'sogou', 'exabot', 'YandexBot', 'YandexImages',
    'YandexRenderResourcesBot', 'msnbot', 'msnbot-media', 'SeznamBot', '360Spider',
    'Naver', 'NaverBot', 'Daum', 'Yeti', 'YisouSpider', 'ZumBot', 'coccocbot-web',
    'MojeekBot', 'HaoSouSpider', 'Sogou web spider', 'Teoma', 'Qwantify',
    'bingbot/desktop', 'bingbot/mobile', 'adidxbot',

    # Specialized Search Engine Bots
    'Googlebot-Image', 'Googlebot-News', 'Googlebot-Video', 'Googlebot-Mobile',
    'GoogleOther', 'Google-CloudVertexBot', 'Google Favicon', 'googleweblight',
    'Storebot-Google', 'DuplexWeb-Google', 'GoogleProducer', 'Google-Read-Aloud',
    'Google-Structured-Data-Testing-Tool', 'Google-InspectionTool', 'Google-Safety',
    'Google-Site-Verification', 'baiduspider-image', 'baiduspider-video',
    'TinEye', 'TinEye-bot', 'Feedfetcher-Google',

    # AI & LLM Bots
    'ChatGPT-User', 'OAI-SearchBot', 'GPTBot', 'Claude-Web', 'ClaudeBot', 'PerplexityBot',
    'CCBot', 'AI2Bot', 'Google-Extended', 'Applebot-Extended', 'Amazonbot', 'YouBot',
    'Kangaroo Bot', 'Diffbot', 'Timpibot', 'Webzio-Extended', 'Meta-ExternalFetcher',
    'anthropic-ai', 'cohere-ai', 'omgili', 'NeevaBot', 'QuoraBot', 'OpenAI-User',
    'Anthropic-Crawler', 'Bytespider', 'Cohere-crawler', 'Glean-crawler',
    'Midjourney-Bot', 'Vercel-ai-crawler',

    # Social Media Bots
    'twitterbot', 'facebookexternalhit', 'meta-externalagent', 'linkedinbot',
    'pinterest', 'Pinterestbot', 'discordbot', 'FacebookBot', 'TelegramBot',
    'Slackbot-LinkExpanding',

    # Ads & Marketing Bots
    'AdsBot-Google-Mobile', 'AdsBot-Google', 'Mediapartners-Google', 'adsbot-google',
    'EmailWolf', 'HubSpot', 'Mailchimp', 'Sendgrid', 'Litmus', 'Email Preview',

    # E-commerce & Product Bots
    'AmazonBot', 'ShopifyBot', 'PriceSpider', 'ShopzillaBot', 'YottaShopping_Bot', 'Anthill',

    # SEO & Analytics Bots
    'ahrefs', 'ahrefsbot', 'majestic', 'sistrix', 'semrushbot', 'SemrushBot',
    'rogerbot', 'serpstatbot', 'BrightEdge Crawler', 'DataForSeoBot',
    'AwarioSmartBot', 'BuildtWith', 'Wappalyzer', 'Linespider',

    # Monitoring & Status Bots
    'lighthouse', 'gtmetrix', 'pingdom', 'newrelic', 'Chrome Lighthouse',
    'statusbot', 'uptimerobot', 'Uptimebot', 'robot.alp', 'monitoring',
    'Pingdom.com_bot', 'UptimeRobot', 'StatusCake', 'Pulsetic', 'Site24x7',
    'WebPageTest',

    # Security & Vulnerability Scanners
    'Nuclei', 'Acunetix', 'Qualys', 'Burp', 'ZAP', 'Nikto', 'Arachni',
    'SecurityTrails', 'Nessus', 'Virustotal', 'CensysInspect', 'Shodan',
    'Nmap Scripting Engine', 'scan', 'inspect', 'sitecheck.sucuri.net',
    'Cortex Xpanse bot',

    # Archival and Preservation Bots
    'archive.org_bot', 'wayback machine', 'internetarchive', 'ia_archiver',
    'AlexandriaOrgBot', 'BoldGrid',

    # Feed Readers & Content Aggregators
    'Feedly', 'Feedbin', 'Atom Feed Robot', 'FeedFetcher-Google',

    # Development and Testing Tools
    'postman', 'curl', 'cURL', 'wget', 'httpunit', 'phantomjs', 'Cpanel-HTTP-Client',
    'validator', 'Headless Chrome', 'Go-http-client', 'OkHttp', 'python',
    'Python-urllib', 'ZGrab', 'axios', 'Node fetch', 'libwww-perl',

    # Generic Crawlers and Scrapers
    'crawler', 'spider', 'bot', 'scraper', 'ALittle Client', 'BLEXBot', 'spbot', 'BUbiNG', 'ltx71', 'Ezooms', 'DotBot',
    'panscient.com', 'findlinks', 'Gigabot', 'ichiro', 'mogimogi', 'Openbot',
    'phpcrawl', 'nutch', 'MetaInspector', 'Python opengraph', 'Iframely',
    'Zspider', 'zermelo', 'socsci_bot', 'discobot', 'SurveyBot', 'VoidEYE',
    'webcrawl.net', 'proximic', 'ZoominfoBot', 'NetcraftSurveyAgent', 'Scrapy',
    'AppEngine-Google', 'Datanyze', 'petalbot', 'Mail.RU_Bot', 'Seekport Crawler',

    # Browser & Automation Identifiers
    'headless', 'automation', 'selenium', 'puppeteer', 'robot', 'research'
]

# Cache for IP reputation data (IP -> (data, timestamp))
# Data can be either AbuseIPDB score or IPdetective result
IPDB_CACHE = {}
IPDB_CACHE_TTL = 24 * 60 * 60  # 24 hours in seconds

# Cache for browser capability scores (IP -> (score, timestamp))
BROWSER_CAPABILITY_CACHE = {}
BROWSER_CAPABILITY_TTL = 24 * 60 * 60  # 24 hours in seconds

# Bot detection settings
CAPABILITY_GRACE_PERIOD = 10  # seconds before marking as bot due to missing capability data, analytics are logged immediately

# Create blueprint for browser capability API
browser_capability_bp = Blueprint('browser_capability', __name__)

@browser_capability_bp.route('/browser-capabilities', methods=['POST', 'GET'])
def browser_capabilities():
    """
    Endpoint to receive browser capability test results

    Returns:
        JSON response confirming receipt
    """
    # Handle GET requests with a helpful message
    if request.method == 'GET':
        return jsonify({
            "status": "info",
            "message": "This endpoint accepts POST requests with browser capability test results. "
                      "It should not be accessed directly in a browser. "
                      "The JavaScript on the website automatically sends capability data to this endpoint."
        }), 200

    # Handle POST requests
    try:
        # Log the raw request data for debugging
        content_type = request.headers.get('Content-Type', '')
        current_app.logger.info(f"Received request with Content-Type: {content_type}")

        # Check if the Content-Type is correct
        if not content_type.startswith('application/json'):
            current_app.logger.warning(f"Invalid Content-Type: {content_type}")
            return jsonify({
                "status": "error",
                "message": "Content-Type must be application/json"
            }), 415

        # Try to parse the JSON data
        try:
            data = request.get_json(silent=False)
        except Exception as json_error:
            current_app.logger.error(f"JSON parsing error: {str(json_error)}")
            return jsonify({
                "status": "error",
                "message": f"Invalid JSON format: {str(json_error)}"
            }), 400

        # Check if data is present
        if not data:
            current_app.logger.warning("Empty browser capability data received")
            return jsonify({"status": "error", "message": "No data provided"}), 400

        # Log the received data for debugging
        current_app.logger.debug(f"Received browser capability data: {data}")

        # Validate required fields
        if 'score' not in data:
            current_app.logger.warning("Missing 'score' field in browser capability data")
            return jsonify({"status": "error", "message": "Missing 'score' field"}), 400

        if 'details' not in data:
            current_app.logger.warning("Missing 'details' field in browser capability data")
            return jsonify({"status": "error", "message": "Missing 'details' field"}), 400

        # Extract data
        try:
            # Ensure score is an integer
            score = int(data.get('score', 0))
            details = data.get('details', {})
            current_app.logger.debug(f"Processing capability score: {score}")
        except (TypeError, ValueError) as e:
            current_app.logger.error(f"Invalid score value: {data.get('score')}, Error: {e}")
            return jsonify({"status": "error", "message": "Score must be a number"}), 400

        # Get IP address and session ID
        ip_address = request.remote_addr
        session_id = session.sid if hasattr(session, 'sid') else None

        # Log session information for debugging
        current_app.logger.warning(
            f"SESSION INFO (browser_capabilities): sid={session_id}, has_pending_analytics={hasattr(session, 'pending_analytics')}, "
            f"pending_count={len(session.get('pending_analytics', []))}, "
            f"session_keys={list(session.keys() if hasattr(session, 'keys') else [])}"
        )

        # Store in cache
        BROWSER_CAPABILITY_CACHE[ip_address] = (score, time.time(), details)
        
        # Log cache information for debugging
        current_app.logger.warning(
            f"CACHE UPDATED: IP={ip_address}, score={score}, cache_size={len(BROWSER_CAPABILITY_CACHE)}"
        )

        # Log the capability score
        current_app.logger.warning(
            f"Browser capability score for IP {ip_address}: {score}%. "
            f"Details: {details}"
        )

        # Check if this is a bot based on capability score
        if score < 70:  # Less than 70% of capabilities = likely bot
            # Add to bot database
            try:
                failed_tests = [test for test, passed in details.items() if not passed]
                add_bot_ip(
                    ip_address,
                    detection_method="Browser Capability",
                    patterns=f"Score: {score}%, Failed: {', '.join(failed_tests)}"
                )
                # Update all previous analytics entries
                update_analytics_for_bot_ip(ip_address, "Browser Capability")
                current_app.logger.warning(
                    f"Bot detected by browser capability tests: {ip_address} "
                    f"(Score: {score}%, Failed: {', '.join(failed_tests)})"
                )

                # Immediately mark the current session as a bot
                if hasattr(session, 'is_bot'):
                    session['is_bot'] = True
                    session['bot_detection_method'] = "Browser Capability"
                    current_app.logger.warning(f"Current session marked as bot based on browser capability")
            except Exception as e:
                current_app.logger.error(f"Failed to add bot IP to database: {e}")

        # We no longer need to process delayed first visits since we're logging them immediately
        # and updating them later
        
        # Make sure session is initialized
        if not hasattr(session, 'sid'):
            current_app.logger.warning("SESSION NOT INITIALIZED: No session ID available")
            
        # Update any pending analytics entries with the capability score
        if hasattr(session, 'pending_analytics') and session.get('pending_analytics'):
            try:
                current_app.logger.warning(
                    f"UPDATING PENDING ENTRIES: Found {len(session.get('pending_analytics', []))} entries to update with score {score}"
                )
                
                db_session = get_database_session()
                if db_session is None:
                    raise Exception("No database session available")

                pending_ids = session.get('pending_analytics', [])
                updated_count = 0
                
                for entry_id in pending_ids:
                    try:
                        entry = db_session.query(Analytics).get(entry_id)
                        if entry:
                            entry.browser_capability = score  # Score is already an int
                            updated_count += 1
                            current_app.logger.warning(
                                f"UPDATED ENTRY: Analytics entry {entry_id} updated with capability score {score}"
                            )
                        else:
                            current_app.logger.error(f"Entry {entry_id} not found in database")
                    except Exception as e:
                        current_app.logger.error(
                            f"Failed to update entry {entry_id}: {e}"
                        )
                
                db_session.commit()
                session.pop('pending_analytics', None)  # Clear pending updates
                session.modified = True  # Make sure the session is saved
                
                current_app.logger.warning(
                    f"UPDATE COMPLETE: Updated {updated_count} analytics entries with capability score {score}"
                )
            except Exception as e:
                current_app.logger.error(f"Failed to update analytics entries: {e}")
                if 'db_session' in locals() and db_session:
                    db_session.rollback()
        # If there are no pending analytics but we have a session ID, try to find and update the most recent visit
        elif hasattr(session, 'sid') and session.sid:
            try:
                db_session = get_database_session()
                if db_session is None:
                    raise Exception("No database session available")
                
                # Find the most recent visit from this session that has no browser capability data
                recent_visit = db_session.query(Analytics).filter(
                    Analytics.sessionid == session.sid,
                    Analytics.browser_capability == None
                ).order_by(Analytics.timestamp.desc()).first()
                
                if recent_visit:
                    recent_visit.browser_capability = score
                    db_session.commit()
                    current_app.logger.warning(
                        f"UPDATED RECENT VISIT: Analytics entry {recent_visit.id} updated with capability score {score}"
                    )
            except Exception as e:
                current_app.logger.error(f"Failed to update recent visit: {e}")
                if 'db_session' in locals() and db_session:
                    db_session.rollback()

            except Exception as e:
                current_app.logger.error(f"Failed to update analytics entries: {e}")
                db_session.rollback()

        return jsonify({"status": "success"}), 200
    except Exception as e:
        current_app.logger.error(f"Error processing browser capability data: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

def get_cached_ip_data(ip_address, source='abuseipdb'):
    """
    Get cached IP data if not expired

    Args:
        ip_address (str): IP address to check
        source (str): Data source ('abuseipdb' or 'ipdetective')

    Returns:
        Any: Cached data or None if not found or expired
    """
    if ip_address in IPDB_CACHE and source in IPDB_CACHE[ip_address]:
        data, timestamp = IPDB_CACHE[ip_address][source]
        if time.time() - timestamp < IPDB_CACHE_TTL:
            return data
        # Remove expired entry
        del IPDB_CACHE[ip_address][source]
        if not IPDB_CACHE[ip_address]:  # If no sources left, remove the IP
            del IPDB_CACHE[ip_address]
    return None

def get_cached_browser_capability(ip_address):
    """
    Get cached browser capability score if not expired

    Args:
        ip_address (str): IP address to check

    Returns:
        tuple: (score, details) or (None, None) if not found or expired
               
    Note:
        Returning (None, None) indicates that no browser capability data has been
        received for this IP address, which may be used to identify potential bots
        that don't execute JavaScript or don't report capability data.
    """
    current_app.logger.debug(f"Checking browser capability cache for IP: {ip_address}")
    
    if ip_address in BROWSER_CAPABILITY_CACHE:
        score, timestamp, details = BROWSER_CAPABILITY_CACHE[ip_address]
        age = time.time() - timestamp
        
        if age < BROWSER_CAPABILITY_TTL:
            current_app.logger.debug(f"Cache hit for IP {ip_address}: score={score}, age={age:.2f}s")
            return score, details
        else:
            current_app.logger.debug(f"Cache expired for IP {ip_address}: age={age:.2f}s > TTL={BROWSER_CAPABILITY_TTL}s")
            # Remove expired entry
            del BROWSER_CAPABILITY_CACHE[ip_address]
    else:
        current_app.logger.debug(f"Cache miss for IP {ip_address}")
        
    return None, None

# List of URL patterns that indicate bot activity
BOT_URL_PATTERNS = [
    # WordPress-related bot patterns
    'wp-login.php',
    'wp-admin',
    'xmlrpc.php',
    'wp-includes',
    'wordpress',
    'wlwmanifest.xml',
    'wp-config.php',
    'wp-cron.php',
    'wp-json',
    'wp-upload.php',
    'wp-links-opml.php',
    'wp-mail.php',
    'wp-register.php',
    'wp-trackback.php',

    # CMS and Admin Panels
    'admin/',
    'administrator/',
    'login.php',
    'admin.php',
    'portal/login',
    'dashboard/',
    'control/',
    'cpanel',
    'webadmin',
    'manager/',
    'backend/',
    'admincp/',
    'moderator/',
    'panel/',

    # Database Management
    'phpmyadmin/',
    'myadmin/',
    'mysql/',
    'mariadb/',
    'adminer.php',
    'dbadmin/',
    'sql/',

    # Configuration and Environment Files
    '.env',
    '.git/',
    '.svn/',
    '.htaccess',
    '.htpasswd',
    'web.config',
    'config.php',
    'configuration.php',
    'settings.php',
    'appsettings',
    'app.config',
    'site.xml',
    'config.yml',
    'config.json',
    '.env.backup',
    '.env.local',
    '.env.development',
    '.env.production',
    'config.inc.php',

    # Backup and Temporary Files
    'backup',
    'backups/',
    '.bak',
    '.old',
    '.backup',
    '.tmp',
    '.temp',
    'temp/',
    'old/',
    'archive/',
    '.zip',
    '.tar',
    '.gz',
    '.sql',
    '.dump',

    # System and Sensitive Files
    '/etc/passwd',
    'passwd',
    'shadow',
    'phpinfo',
    'info.php',
    'server-status',
    'server-info',
    'status.php',
    'system/',
    'logs/',
    'log.txt',
    'error_log',
    'debug.log',
    'access.log',
    '.well-known/',
    'install.php',
    'setup.php',
    'install/',
    'setup/',

    # Common Exploit Targets
    '.cgi',
    '.pl',
    '.py',
    '.sh',
    'cgi-bin/',
    'bin/',
    'shell.php',
    'backdoor.php',
    'cmd.php',
    'exec.php',
    'eval.php',
    'system.php',
    'upload.php',
    'file.php',
    'rce.php',
    'remote.php',
    'filemanager/',
    'kcfinder/',
    'tinymce/',
    'fckeditor/',
    'elfinder/',

    # API Endpoints (excluding legitimate site endpoints)
    'api/admin',
    'api/users',
    'api/config',
    'api/system',
    'api/debug',
    'api/v1/',
    'api/v2/',
    'api/test',
    'api/dev',
    'api/internal',
    'api/private',
    'api/public',
    'api/upload',
    'api/download',
    'api/settings',
    'api/swagger',
    'api/docs',
    'api/spec',
    'api/schema',
    'rest/',
    'graphql',
    'graphiql',
    'oauth/',
    'token/',
    'auth/',
    'login/oauth',
    'api/login',
    'api/auth',

    # Note: The following legitimate API endpoints are NOT included:
    # - api/placeholder/
    # - api/ai/enhance
    # - api/cookie-consent
    # - api/analytics/data

    # Development and Testing Paths
    'test.php',
    'debug.php',
    'dev/',
    'development/',
    'staging/',
    'test/',
    'demo/',
    'beta/',
    'testing/',
    'uat/',
    'sandbox/',
    'example/',
    'sample/',

    # Common Bot/Crawler Paths
    'robots.txt',
    'sitemap.xml',
    'sitemap/',
    'wp-sitemap.xml',
    'rss',
    'feed/',
    'atom.xml',
    'rss.xml',

    # IDE and Development Tools
    'vscode',
    '.vscode/',
    '.idea/',
    '.project',
    '.settings/',
    'node_modules/',
    'vendor/',
    'composer.json',
    'package.json',
    'Gemfile',
    'Dockerfile',
    'docker-compose.yml',

    # Common Vulnerability Scanners Paths
    'struts',
    'solr/',
    'jenkins/',
    'joomla/',
    'drupal/',
    'magento/',
    'laravel/',
    'symfony/',
    'zend/',
    'typo3/',
    'concrete5/',
    'shopify/',
    'prestashop/',
    'wordpress/',
    'moodle/',
    'cgi/',
    'webdav/',
    'xmlrpc/'
]

def get_geolocation(ip_address):
    """
    Retrieve geolocation information for an IP address

    Args:
        ip_address (str): IP address to lookup

    Returns:
        str: Country name or None
    """
    try:
        with geoip2.database.Reader('assets/geoip/GeoLite2-Country.mmdb') as reader:
            response = reader.country(ip_address)
            return response.country.name
    except Exception as e:
        current_app.logger.warning(f"Geolocation lookup failed: {e}")
        return None

def get_database_session():
    """
    Create a new database session

    Returns:
        Session: SQLAlchemy session object
    """
    try:
        return db.session
    except Exception as e:
        current_app.logger.error(f"Failed to create database session: {e}")
        return None

def get_bot_ips(session=None):
    """
    Retrieve current bot IP addresses from the database

    Args:
        session (Session, optional): Database session. If None, a new session will be created.

    Returns:
        list: List of active bot IP addresses
    """
    try:
        # Use provided session or create a new one
        db_session = session or get_database_session()

        if db_session is None:
            current_app.logger.error("No database session available")
            return []

        return BotIP.get_bot_ips(db_session)
    except SQLAlchemyError:
        current_app.logger.error("Failed to retrieve bot IPs from database")
        return []

def add_bot_ip(ip_address, detection_method=None, patterns=None):
    """
    Add an IP address to bot list in the database

    Args:
        ip_address (str): IP address to add to bot list
        detection_method (str, optional): Method used to detect the bot
        patterns (str or list, optional): Patterns that triggered bot detection (URLs or signatures)
    """
    try:
        # Create a database session
        db_session = get_database_session()

        if db_session is None:
            current_app.logger.error("Cannot add bot IP: No database session available")
            return

        # Add or update bot IP in the database
        BotIP.add_bot_ip(
            db_session,
            ip_address,
            detection_method=detection_method,
            url_patterns=patterns
        )

        current_app.logger.warning(
            f"Added IP {ip_address} to bot list "
            f"(detected by {detection_method}, patterns: {patterns})"
        )
    except SQLAlchemyError as e:
        current_app.logger.error(f"Failed to add bot IP: {e}")

def update_analytics_for_bot_ip(ip_address, detection_method):
    """
    Update all analytics entries for a specific IP to mark them as bot traffic

    Args:
        ip_address (str): IP address to update
        detection_method (str): Method used to detect the bot
    """
    try:
        db_session = get_database_session()
        if db_session is None:
            current_app.logger.error("Cannot update analytics: No database session available")
            return

        # Find all analytics entries for this IP
        entries = db_session.query(Analytics).filter(
            Analytics.ip_address == ip_address,
            ~Analytics.is_bot  # Only update entries not already marked as bots
        ).all()

        if not entries:
            current_app.logger.info(f"No analytics entries to update for bot IP: {ip_address}")
            return

        # Update all entries
        for entry in entries:
            entry.is_bot = True
            if entry.bot_detection_method:
                if detection_method not in entry.bot_detection_method:
                    entry.bot_detection_method = f"{entry.bot_detection_method}, {detection_method}"
            else:
                entry.bot_detection_method = detection_method

        db_session.commit()
        current_app.logger.info(f"Updated {len(entries)} analytics entries for bot IP: {ip_address}")
    except SQLAlchemyError as e:
        current_app.logger.error(f"Failed to update analytics for bot IP: {e}")

def detect_bot(user_agent_string, ip_address=None):
    """
    Comprehensive bot detection mechanism with extended signatures and IP-based detection

    Detection methods include:
    - IP-based detection (checking against known bot IPs)
    - User agent pattern matching
    - Browser capability testing
    - Missing browser capability data (requests without capability data are considered bots)
    - IP reputation services (AbuseIPDB, IPdetective)

    Args:
        user_agent_string (str): Full user agent string
        ip_address (str, optional): IP address to check against bot list

    Returns:
        dict: Bot detection results
    """
    # Initialize detection methods list
    detection_methods = []

    # Initialize bot detection result
    is_bot = False

    # Initialize other variables
    abuse_score = None
    country = None
    is_norwegian = False
    ipdetective_is_bot = None
    browser_capability_score = None
    browser_capability_details = None

    # Normalize user agent to lowercase for matching
    lower_ua = user_agent_string.lower()

    # 1. First check the local bot database
    if ip_address:
        try:
            db_session = get_database_session()
            if db_session is not None:
                bot_ips = get_bot_ips(db_session)
                if ip_address in bot_ips:
                    is_bot = True
                    detection_methods.append("ip_based_detection")
                    current_app.logger.info(f"IP {ip_address} found in bot database")
            else:
                current_app.logger.warning("No database session available for bot IP check")
        except Exception as e:
            current_app.logger.error(f"Failed to check bot database: {e}")

    # 2. If not in bot database, proceed to user agent checks
    if not is_bot:
        # Keyword-based detection with extended signatures (case-insensitive)
        keyword_match = [
            keyword for keyword in EXTENDED_BOT_SIGNATURES
            if keyword.lower() in lower_ua
        ]

        # Library-based detection
        parsed_user_agent = user_agents.parse(user_agent_string)
        library_detection = parsed_user_agent.is_bot

        # Suspicious pattern detection
        suspicious_patterns = [
            # Very short generic user agents
            len(user_agent_string) < 50,
            # Overly generic Mozilla/5.0 pattern
            user_agent_string.startswith('Mozilla/5.0') and len(user_agent_string.split()) < 3,
            # Uncommon or suspicious user agent patterns
            'compatible;' in lower_ua and len(lower_ua) < 100
        ]

        # Check if any user agent checks indicate a bot
        if keyword_match:
            is_bot = True
            detection_methods.append("keyword_match")
            # Add to bot database
            if ip_address:
                try:
                    add_bot_ip(
                        ip_address,
                        detection_method="User Agent Keywords",
                        patterns=', '.join(keyword_match)
                    )
                    # Update all previous analytics entries
                    update_analytics_for_bot_ip(ip_address, "User Agent Keywords")
                except Exception as e:
                    current_app.logger.error(f"Failed to add bot IP to database: {e}")

        if not is_bot and library_detection:
            is_bot = True
            detection_methods.append("library_detection")
            # Add to bot database
            if ip_address:
                try:
                    add_bot_ip(
                        ip_address,
                        detection_method="User Agent Library",
                        patterns="user_agents.parse detection"
                    )
                    # Update all previous analytics entries
                    update_analytics_for_bot_ip(ip_address, "User Agent Library")
                except Exception as e:
                    current_app.logger.error(f"Failed to add bot IP to database: {e}")

        if not is_bot and any(suspicious_patterns):
            is_bot = True
            detection_methods.append("suspicious_pattern")
            # Add to bot database
            if ip_address:
                patterns = [
                    "Short UA" if i == 0 else
                    "Generic Mozilla" if i == 1 else
                    "Suspicious Compatible" if i == 2 else
                    f"Pattern {i}"
                    for i, is_suspicious in enumerate(suspicious_patterns) if is_suspicious
                ]
                try:
                    add_bot_ip(
                        ip_address,
                        detection_method="Suspicious Pattern",
                        patterns=', '.join(patterns)
                    )
                    # Update all previous analytics entries
                    update_analytics_for_bot_ip(ip_address, "Suspicious Pattern")
                except Exception as e:
                    current_app.logger.error(f"Failed to add bot IP to database: {e}")

    # 3. Check browser capabilities - with improved timing
    if not is_bot and ip_address:
        try:
            # Check browser capability cache
            browser_capability_score, browser_capability_details = get_cached_browser_capability(ip_address)

            # If no browser capability data is available, check timing
            if browser_capability_score is None:
                current_time = time.time()
                
                # Initialize session tracking if needed
                if not hasattr(session, 'capability_check_attempts'):
                    session['capability_check_attempts'] = 0
                    session['first_capability_check'] = current_time
                
                # Track capability check attempt
                session['capability_check_attempts'] += 1
                time_since_first_check = current_time - session.get('first_capability_check', current_time)
                
                # Log capability check attempt
                current_app.logger.debug(
                    f"Capability check attempt {session['capability_check_attempts']} "
                    f"for {ip_address} after {time_since_first_check:.2f}s"
                )
                
                # Only mark as bot if:
                # 1. Grace period has expired AND
                # 2. We've tried checking multiple times OR significant time has passed
                if (time_since_first_check >= CAPABILITY_GRACE_PERIOD and 
                    (session['capability_check_attempts'] >= 3 or 
                     time_since_first_check >= CAPABILITY_GRACE_PERIOD * 2)):
                    
                    is_bot = True
                    detection_methods.append("missing_browser_capability")
                    patterns = (
                        f"No capability data after {time_since_first_check:.2f}s "
                        f"and {session['capability_check_attempts']} attempts"
                    )
                    
                    # Add to bot database with detailed timing info
                    try:
                        add_bot_ip(
                            ip_address,
                            detection_method="Missing Browser Capability",
                            patterns=patterns
                        )
                        # Update analytics entries
                        update_analytics_for_bot_ip(ip_address, "Missing Browser Capability")
                        current_app.logger.warning(f"Bot detected: {patterns}")
                    except Exception as e:
                        current_app.logger.error(f"Failed to add bot IP to database: {e}")
                else:
                    # Still waiting for capability data
                    current_app.logger.debug(
                        f"Waiting for capability data: "
                        f"attempts={session['capability_check_attempts']}, "
                        f"time={time_since_first_check:.2f}s"
                    )

            # Check if browser capability score is too low
            elif browser_capability_score < 70:
                is_bot = True
                detection_methods.append("browser_capability")
                # Add to bot database (if not already done by the API endpoint)
                try:
                    if browser_capability_details:
                        failed_tests = [test for test, passed in browser_capability_details.items() if not passed]
                        patterns = f"Score: {browser_capability_score}%, Failed: {', '.join(failed_tests)}"
                    else:
                        patterns = f"Score: {browser_capability_score}%"

                    add_bot_ip(
                        ip_address,
                        detection_method="Browser Capability",
                        patterns=patterns
                    )
                    # Update all previous analytics entries
                    update_analytics_for_bot_ip(ip_address, "Browser Capability")
                    current_app.logger.warning(f"Bot detected by browser capability: {ip_address} ({patterns})")

                    # Mark the current session as a bot
                    if hasattr(session, 'is_bot'):
                        session['is_bot'] = True
                        session['bot_detection_method'] = "Browser Capability"
                        current_app.logger.warning("Current session marked as bot based on browser capability")
                except Exception as e:
                    current_app.logger.error(f"Failed to add bot IP to database: {e}")
        except Exception as e:
            current_app.logger.error(f"Error during browser capability check: {e}")

    # 4. If not yet determined to be a bot, check IPdetective
    if not is_bot and ip_address:
        try:
            # Get country for later use with AbuseIPDB thresholds
            country = get_geolocation(ip_address)
            is_norwegian = country and country.lower() == 'norway'

            # Check IPdetective cache first
            ipdetective_is_bot = get_cached_ip_data(ip_address, source='ipdetective')

            if ipdetective_is_bot is None:
                # Not in cache, call the API
                from .security import check_ip_detective
                ipdetective_is_bot, ipdetective_error = check_ip_detective(ip_address)

                if ipdetective_error:
                    current_app.logger.warning(f"IPdetective.io check error: {ipdetective_error}")
                else:
                    # Cache the result
                    if ip_address not in IPDB_CACHE:
                        IPDB_CACHE[ip_address] = {}
                    IPDB_CACHE[ip_address]['ipdetective'] = (ipdetective_is_bot, time.time())

            if ipdetective_is_bot:
                is_bot = True
                detection_methods.append("ipdetective")
                # Add to bot database
                try:
                    add_bot_ip(
                        ip_address,
                        detection_method="ipdetective",
                        patterns=["IPdetective.io Bot Detection"]
                    )
                    # Update all previous analytics entries
                    update_analytics_for_bot_ip(ip_address, "ipdetective")
                    current_app.logger.warning(f"Bot detected by IPdetective.io: {ip_address}")
                except Exception as e:
                    current_app.logger.error(f"Failed to add bot IP to database: {e}")
        except Exception as e:
            current_app.logger.error(f"Error during IPdetective check: {e}")

    # 4. If not yet determined to be a bot, check AbuseIPDB
    if not is_bot and ip_address:
        try:
            # If country wasn't determined yet, get it now
            if country is None:
                country = get_geolocation(ip_address)
                is_norwegian = country and country.lower() == 'norway'

            # Check AbuseIPDB cache first
            abuse_score = get_cached_ip_data(ip_address, source='abuseipdb')

            if abuse_score is None:
                # Not in cache, call the API
                from .security import check_ip_reputation
                abuse_score, error = check_ip_reputation(ip_address)

                if error is None and abuse_score is not None:
                    # Cache the score
                    if ip_address not in IPDB_CACHE:
                        IPDB_CACHE[ip_address] = {}
                    IPDB_CACHE[ip_address]['abuseipdb'] = (abuse_score, time.time())

            if abuse_score is not None:
                # Use different thresholds based on country
                threshold = 90 if is_norwegian else 1

                if abuse_score >= threshold:
                    is_bot = True
                    detection_methods.append("abuseipdb")
                    # Add to bot database
                    try:
                        country_info = "Norwegian IP, " if is_norwegian else ""
                        add_bot_ip(
                            ip_address,
                            detection_method="abuseipdb",
                            patterns=[f"{country_info}AbuseIPDB Score: {abuse_score}"]
                        )
                        # Update all previous analytics entries
                        update_analytics_for_bot_ip(ip_address, "abuseipdb")
                    except Exception as e:
                        current_app.logger.error(f"Failed to add bot IP to database: {e}")
        except Exception as e:
            current_app.logger.error(f"Error during AbuseIPDB check: {e}")

    return {
        'is_bot': is_bot,
        'detection_methods': detection_methods,
        'matched_signatures': keyword_match if 'keyword_match' in locals() else [],
        'suspicious_patterns': [i for i, is_suspicious in enumerate(suspicious_patterns)] if 'suspicious_patterns' in locals() else [],
        'library_detection': library_detection if 'library_detection' in locals() else False,
        'user_agent': user_agent_string,
        'ip_address': ip_address,
        'abuse_score': abuse_score,
        'ipdetective_result': ipdetective_is_bot,
        'browser_capability_score': browser_capability_score,
        'browser_capability_details': browser_capability_details
    }

def detect_bot_by_url(url):
    """
    Detect bots based on URL patterns

    Args:
        url (str): The URL being accessed

    Returns:
        dict: Bot detection results for URL pattern
    """
    # Normalize URL to lowercase for matching
    lower_url = url.lower()

    # Check if any bot URL pattern is in the requested URL
    matched_patterns = [
        pattern for pattern in BOT_URL_PATTERNS
        if pattern in lower_url
    ]

    return {
        'is_bot': bool(matched_patterns),
        'detection_methods': ['url_pattern'] if matched_patterns else [],
        'matched_patterns': matched_patterns,
        'url': url
    }

def log_page_view(response):
    """Enhanced page view logging with explicit exclusion handling"""
    start_time = getattr(g, 'start_time', None)
    ip_address = request.remote_addr

    # Check if visit should be logged
    should_log, reason = should_log_visit(request, ip_address)
    if not should_log:
        current_app.logger.debug(f"Skipping analytics: {reason}")
        return response

    try:
        # Get device and browser info
        user_agent_string = request.user_agent.string
        parsed_user_agent = user_agents.parse(user_agent_string)

        device_type = (
            'Mobile' if parsed_user_agent.is_mobile else
            'Tablet' if parsed_user_agent.is_tablet else
            'Desktop' if parsed_user_agent.is_pc else
            'Other'
        )
        browser = f"{parsed_user_agent.browser.family} {parsed_user_agent.browser.version_string}"
        session_id = session.sid if hasattr(session, 'sid') else 'unknown'

        # Initialize browser capability score
        browser_capability_score = None
        
        # Check if this session is already marked as a bot from browser capability tests
        if hasattr(session, 'is_bot') and session.get('is_bot') and session.get('bot_detection_method') == "Browser Capability":
            current_app.logger.warning("Session already marked as bot by browser capability tests")
            is_bot = True
            detection_methods = ["browser_capability"]

            # Ensure the IP is in the bot database
            add_bot_ip(
                ip_address,
                detection_method="Browser Capability",
                patterns="Session marked as bot"
            )

            # Get user agent for logging
            user_agent_string = request.user_agent.string
            parsed_user_agent = user_agents.parse(user_agent_string)
            
            # Get browser capability score from cache
            browser_capability_score, _ = get_cached_browser_capability(ip_address)
        else:
            # Detect user agent and bot status
            user_agent_string = request.user_agent.string

            # First check URL patterns for bot detection
            url_bot_detection = detect_bot_by_url(request.path)

            is_bot = url_bot_detection['is_bot']
            detection_methods = url_bot_detection['detection_methods']

            # If URL indicates a bot, add to bot database
            if is_bot:
                current_app.logger.warning(
                    f"Bot Detected by URL pattern: {', '.join(url_bot_detection['matched_patterns'])}"
                )

                # Add to bot list
                add_bot_ip(
                    ip_address,
                    detection_method="URL Pattern",
                    patterns=', '.join(url_bot_detection['matched_patterns'])
                )

                # Update all previous analytics entries
                update_analytics_for_bot_ip(ip_address, "URL Pattern")

            # If not detected as bot by URL, proceed with comprehensive detection
            if not is_bot:
                # Check browser capability cache first for immediate detection
                browser_capability_score, browser_capability_details = get_cached_browser_capability(ip_address)
                
                # Log cache retrieval for debugging
                current_app.logger.warning(
                    f"CACHE CHECK: IP={ip_address}, score={browser_capability_score}, "
                    f"cache_size={len(BROWSER_CAPABILITY_CACHE)}, "
                    f"cache_keys={list(BROWSER_CAPABILITY_CACHE.keys())[:5]}..."
                )

                if browser_capability_score is not None and browser_capability_score < 70:
                    is_bot = True
                    detection_methods = ["browser_capability"]
                    current_app.logger.warning(
                        f"Bot detected by browser capability cache: {ip_address} "
                        f"(Score: {browser_capability_score}%)"
                    )
                else:
                    # Continue with other detection methods
                    bot_detection = detect_bot(user_agent_string, ip_address)
                    is_bot = bot_detection['is_bot']
                    detection_methods = bot_detection['detection_methods']

                    # Log bot detection if detected
                    if is_bot:
                        current_app.logger.warning(
                            f"Bot Detected: {', '.join(detection_methods)}"
                            f" Signatures: {bot_detection['matched_signatures']}"
                        )

            # Detect user agent for device type
            parsed_user_agent = user_agents.parse(user_agent_string)

        # Mapping for more specific device types
        device_type_map = {
            'iphone': 'Mobile',
            'android': 'Mobile',
            'windows phone': 'Mobile',
            'ipad': 'Tablet',
            'kindle': 'Tablet',
            'windows': 'Desktop',
            'mac': 'Desktop',
            'linux': 'Desktop'
        }

        # Fallback device type detection
        device_family = parsed_user_agent.device.family.lower()
        device_type = next(
            (device_type_map[key] for key in device_type_map if key in device_family),
            'Other'
        )

        # Fallback to browser-based detection if needed
        if device_type == 'Other':
            if parsed_user_agent.is_mobile:
                device_type = 'Mobile'
            elif parsed_user_agent.is_tablet:
                device_type = 'Tablet'
            elif parsed_user_agent.is_pc:
                device_type = 'Desktop'

        browser = f"{parsed_user_agent.browser.family} {parsed_user_agent.browser.version_string}"

        # Get country from IP
        country = get_geolocation(ip_address)

        # Calculate processing time
        processing_time = time.time() - start_time if start_time else None

        # Always log the visit immediately to ensure it's recorded
        analytics_entry = Analytics.log_visit(
            sessionid=session_id or 'unknown',
            url=request.full_path.rstrip('?'),
            referrer=request.referrer,
            ip_address=ip_address,
            country=country,
            device_type=device_type,
            browser=browser,
            status_code=response.status_code,
            processing_time=processing_time,
            # Bot detection parameters
            is_bot=is_bot,
            bot_detection_method=', '.join(detection_methods) if detection_methods else None,
            bot_user_agent=user_agent_string,
            # Browser capability score
            browser_capability=browser_capability_score
        )
        
        # Log session information for debugging
        if hasattr(session, 'sid'):
            current_app.logger.warning(
                f"SESSION INFO (log_page_view): sid={session.sid}, has_logged_first_visit={session.get('has_logged_first_visit', False)}"
            )
        else:
            current_app.logger.warning("SESSION NOT INITIALIZED (log_page_view): No session ID available")
            
        # Check if this is a visit without browser capability data and we need to track it for later update
        if analytics_entry and browser_capability_score is None and hasattr(session, 'sid'):
            # Initialize pending_analytics if it doesn't exist
            if not hasattr(session, 'pending_analytics'):
                session['pending_analytics'] = []
            
            # Add this entry to the pending list
            pending_list = session.get('pending_analytics', [])
            
            # Only add if not already in the list
            if analytics_entry.id not in pending_list:
                pending_list.append(analytics_entry.id)
                session['pending_analytics'] = pending_list
                
                # Make sure the session is saved
                session.modified = True
                
                current_app.logger.warning(
                    f"SESSION UPDATED: pending_analytics={session.get('pending_analytics')}"
                )
                
                # Check if this is the first visit
                if not session.get('has_logged_first_visit', False):
                    session['has_logged_first_visit'] = True
                    current_app.logger.warning(
                        f"FIRST VISIT LOGGED: Entry {analytics_entry.id} added to pending updates for browser capability"
                    )
                else:
                    current_app.logger.warning(
                        f"VISIT WITHOUT CAPABILITY LOGGED: Entry {analytics_entry.id} added to pending updates"
                    )
        else:
            current_app.logger.warning(
                f"VISIT LOGGED: Entry with browser_capability={browser_capability_score}"
            )

    except Exception as e:
        current_app.logger.error(f"Page view logging error: {e}")

    return response

def init_analytics(app):
    """
    Initialize enhanced analytics tracking

    Args:
        app (Flask): Flask application instance
    """
    # Register before_request handler to store start time
    @app.before_request
    def before_request():
        g.start_time = time.time()
        
        # Store the first request time in the session if not already set
        # This helps track how long it's been since a user first visited the site
        if hasattr(session, 'sid') and 'first_request_time' not in session:
            session['first_request_time'] = time.time()
            current_app.logger.debug(f"First request time set for session: {request.remote_addr}")

    # Register after_request handler for analytics logging
    @app.after_request
    def after_request(response):
        return log_page_view(response)

    # Register browser capability blueprint with /api prefix
    app.register_blueprint(browser_capability_bp, url_prefix='/api')
    app.logger.info("Registered browser capability blueprint with /api prefix")

    # Add route for browser capability test page
    @app.route('/browser-capability-test')
    def browser_capability_test():
        from flask import render_template
        return render_template('browser-capability-test.html')

    # Add route for checking bot status
    @app.route('/browser-status-check')
    def browser_status_check():
        from flask import jsonify

        # Get IP address
        ip_address = request.remote_addr

        # Check if IP is in bot database
        is_bot = False
        detection_method = None

        # Check if session is marked as bot
        if hasattr(session, 'is_bot') and session.get('is_bot'):
            is_bot = True
            detection_method = session.get('bot_detection_method', 'Unknown')

        # Check browser capability cache
        browser_capability_score = None
        browser_capability_details = None
        if not is_bot:
            score, details = get_cached_browser_capability(ip_address)
            if score is not None:
                browser_capability_score = score
                browser_capability_details = details
                if score < 70:
                    is_bot = True
                    detection_method = "Browser Capability"

        # Check bot database
        if not is_bot:
            db_session = get_database_session()
            if db_session is not None:
                bot_ips = get_bot_ips(db_session)
                if ip_address in bot_ips:
                    is_bot = True
                    detection_method = "IP in Bot Database"

        return jsonify({
            'ip_address': ip_address,
            'is_bot': is_bot,
            'detection_method': detection_method,
            'browser_capability_score': browser_capability_score,
            'browser_capability_details': browser_capability_details,
            'session_id': session.sid if hasattr(session, 'sid') else None,
            'session_data': dict(session) if hasattr(session, 'items') else None
        })

    # Add debug route for browser capability cache
    @app.route('/debug/browser-capability-cache')
    def debug_browser_capability_cache():
        from flask import jsonify

        # Only allow in development mode
        if not app.debug and not app.config.get('TESTING'):
            return jsonify({'error': 'Debug endpoints only available in development mode'}), 403

        # Get all browser capability cache entries
        cache_data = {}
        for ip, data in BROWSER_CAPABILITY_CACHE.items():
            score, timestamp, details = data
            cache_data[ip] = {
                'score': score,
                'timestamp': timestamp,
                'age_seconds': time.time() - timestamp,
                'details': details
            }

        return jsonify({
            'cache_size': len(BROWSER_CAPABILITY_CACHE),
            'cache_ttl_seconds': BROWSER_CAPABILITY_TTL,
            'cache_data': cache_data
        })
        
    @app.route('/debug/session-analytics/<session_id>')
    def debug_session_analytics(session_id):
        from flask import jsonify
        
        # Only allow in development mode
        if not app.debug and not app.config.get('TESTING'):
            return jsonify({'error': 'Debug endpoints only available in development mode'}), 403
            
        try:
            db_session = get_database_session()
            if db_session is None:
                return jsonify({'error': 'No database session available'}), 500
                
            # Get all analytics entries for this session
            entries = db_session.query(Analytics).filter(
                Analytics.sessionid == session_id
            ).order_by(Analytics.timestamp.desc()).all()
            
            result = []
            for entry in entries:
                result.append({
                    'id': entry.id,
                    'timestamp': entry.timestamp.isoformat() if entry.timestamp else None,
                    'url': entry.url,
                    'browser_capability': entry.browser_capability,
                    'is_bot': entry.is_bot,
                    'bot_detection_method': entry.bot_detection_method
                })
                
            return jsonify({
                'session_id': session_id,
                'entry_count': len(result),
                'entries': result
            })
        except Exception as e:
            current_app.logger.error(f"Error in debug_session_analytics: {e}")
            return jsonify({'error': str(e)}), 500

    return app

def requires_auth(f):
    """
    Authentication decorator using Flask-Login
    Provides user-friendly responses for both API and browser requests
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        from flask_login import current_user
        if not current_user.is_authenticated:
            if request.is_json:
                return jsonify({
                    'error': 'Unauthorized',
                    'message': 'You must be logged in to access analytics data'
                }), 401
            # Ensure the next URL is properly formatted
            next_url = '/analytics.html'
            if request.args:
                next_url += '?' + request.query_string.decode('utf-8')
            return redirect(url_for('login', next=next_url))
        return f(*args, **kwargs)
    return decorated_function

def parse_date(date_str):
    """
    Safely parse date string with multiple format support

    Args:
        date_str (str or datetime): Date string or datetime to parse

    Returns:
        datetime or None
    """
    # If already a datetime, return it directly
    if isinstance(date_str, datetime):
        return date_str

    # If not a string, log and return None
    if not isinstance(date_str, str):
        current_app.logger.error(f"Invalid date input: {date_str}, Type: {type(date_str)}")
        return None

    # If empty string, return None
    if not date_str:
        return None

    # Explicitly handle different ISO format variations
    try:
        # Try parsing as full ISO format with timezone
        if 'T' in date_str and '+' in date_str:
            # Remove timezone part
            date_str = date_str.split('+')[0]

        # Ensure it's a string
        date_str = str(date_str).strip()

        # Try parsing various ISO format variations
        return datetime.fromisoformat(date_str)
    except Exception as e:
        # Fallback to traditional parsing if ISO format fails
        date_formats = [
            '%Y-%m-%d',  # ISO format
            '%Y-%m-%dT%H:%M:%S',  # ISO datetime
            '%d/%m/%Y',  # DD/MM/YYYY
            '%m/%d/%Y'   # MM/DD/YYYY
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        # Log detailed error information
        current_app.logger.error(
            f"Failed to parse date. "
            f"Input: {date_str}, "
            f"Type: {type(date_str)}, "
            f"Error: {str(e)}"
        )
        return None

def strip_url_parameters(url):
    """Strip parameters from URL while keeping the path"""
    parsed = urlparse(url)
    clean = parsed._replace(query='', fragment='')
    return urlunparse(clean)

def extract_domain(referrer):
    """Extract domain from referrer URL"""
    if not referrer:
        return "direct"
    try:
        # Add http:// prefix if missing
        if not referrer.startswith(('http://', 'https://')):
            referrer = 'http://' + referrer

        logger.debug(f"Processing referrer: {referrer}")
        parsed = urlparse(referrer)
        logger.debug(f"Parsed URL: scheme={parsed.scheme}, netloc={parsed.netloc}, path={parsed.path}")

        # Handle common direct traffic markers
        if not parsed.netloc or parsed.netloc == "":
            return "direct"

        # Clean up domain by removing www. and other prefixes
        domain = parsed.netloc.lower()
        logger.debug(f"Initial domain: {domain}")

        if domain.startswith('www.'):
            domain = domain[4:]  # Remove www.

        # Remove any trailing port numbers (e.g., :443, :80)
        if ':' in domain:
            domain = domain.split(':')[0]

        logger.debug(f"Final cleaned domain: {domain}")
        return domain
    except Exception as e:
        logger.error(f"Error extracting domain from {referrer}: {str(e)}")
        return "invalid"

def extract_os(browser):
    """
    Extract operating system from browser user agent string

    Example user agents:
    - Windows: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 ...
    - macOS: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 ...
    - Linux: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 ...
    - iOS: Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) ...
    - Android: Mozilla/5.0 (Linux; Android 13; SM-S908B) ...
    """
    if not browser:
        return "unknown"

    browser = browser.lower()

    # Order matters - more specific OS checks first
    if "windows" in browser or "windows nt" in browser or "win64" in browser or "win32" in browser:
        return "windows"
    elif "iphone" in browser or "ipad" in browser or "ipod" in browser:
        return "ios"
    elif "android" in browser:
        return "android"
    elif "macintosh" in browser or "mac os x" in browser:
        return "macos"
    elif "linux" in browser and "android" not in browser:  # Exclude Android which often includes Linux
        return "linux"
    elif "cros" in browser:
        return "chrome os"

    return "other"

def update_session_browser_capability(session_id, capability_score):
    """
    Update browser capability score for all entries from a specific session
    
    Args:
        session_id (str): Session ID to update
        capability_score (int): Browser capability score (0-100)
        
    Returns:
        int: Number of updated entries
    """
    try:
        db_session = get_database_session()
        if db_session is None:
            raise Exception("No database session available")
            
        # Find all entries from this session that have no browser capability data
        entries = db_session.query(Analytics).filter(
            Analytics.sessionid == session_id,
            Analytics.browser_capability == None
        ).all()
        
        updated_count = 0
        for entry in entries:
            entry.browser_capability = capability_score
            updated_count += 1
            
        db_session.commit()
        current_app.logger.warning(
            f"MANUAL UPDATE: Updated {updated_count} entries for session {session_id} with score {capability_score}"
        )
        
        return updated_count
    except Exception as e:
        current_app.logger.error(f"Failed to update session browser capability: {e}")
        if 'db_session' in locals() and db_session:
            db_session.rollback()
        return 0

def register_analytics_routes(app):
    """
    Register analytics-related routes with authentication

    Args:
        app (Flask): Flask application instance
    """
    from .analytics_process import get_analytics_data

    @app.route('/analytics/overview', methods=['GET'])
    @requires_auth
    def analytics_overview():
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        return get_analytics_data(start_date, end_date)
        
    @app.route('/analytics/update-capability/<session_id>/<int:score>', methods=['POST'])
    @requires_auth
    def update_capability(session_id, score):
        """Admin endpoint to manually update browser capability for a session"""
        if score < 0 or score > 100:
            return jsonify({"error": "Score must be between 0 and 100"}), 400
            
        updated = update_session_browser_capability(session_id, score)
        return jsonify({
            "success": True,
            "updated_entries": updated,
            "session_id": session_id,
            "score": score
        })

def collect_analytics(event_type, data):
    try:
        # Only log at DEBUG level in debug mode
        if current_app.debug:
            logger.debug(
                f"Collecting analytics for event: {event_type}",
                extra={
                    'event_type': event_type,
                    'data': data
                }
            )

        # Process analytics data
        # Note: These functions are placeholders for future implementation
        try:
            processed = process_analytics_data(data)
            # Store in database
            store_analytics_data(processed)
        except NameError:
            # Functions not implemented yet, just log for debugging
            if current_app.debug:
                logger.debug("Note: process_analytics_data and store_analytics_data functions not implemented yet")

        # Log successful storage at INFO level for significant events only
        if event_type in ['purchase', 'signup', 'important_action']:
            logger.info(
                f"Important analytics event processed: {event_type}",
                extra={
                    'event_type': event_type,
                    'data': data
                }
            )
    except Exception as e:
        # Always log errors
        logger.error(
            f"Failed to collect analytics: {str(e)}",
            extra={
                'event_type': event_type,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
        )
        return False
    return True
