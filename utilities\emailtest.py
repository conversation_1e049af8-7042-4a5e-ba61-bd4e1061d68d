import os
import sys
import logging
from flask import Flask, render_template
from flask_mail import Mail, Message
from dotenv import load_dotenv

# Add parent directory to path so we can import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

# Import the modules we need
from modules.mail import init_mail, send_contact_form_email

# Set up logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_email():
    """Test basic email sending with Flask-Mail"""
    # Create a minimal Flask app for testing
    app = Flask(__name__)
    app.template_folder = os.path.join(parent_dir, 'templates')
    
    # Load environment variables
    load_dotenv()
    
    # Configure server name for URL generation
    app.config['SERVER_NAME'] = os.getenv('SERVER_NAME', 'localhost:5000')
    app.config['PREFERRED_URL_SCHEME'] = 'https'
    app.config['APPLICATION_ROOT'] = '/'
    
    # Initialize mail
    mail = init_mail(app)
    
    # Create a test context
    with app.app_context():
        try:
            # Create a test message
            msg = Message(
                "Test Basic Email",
                sender=app.config['MAIL_DEFAULT_SENDER'],
                recipients=["<EMAIL>"]
            )
            
            msg.body = "This is a basic test email from the Flask application."
            
            # Send the email
            mail.send(msg)
            print(f"Basic email sent <NAME_EMAIL>!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send basic email: {str(e)}")
            print(f"Failed to send basic email. Error: {str(e)}")
            return False

def test_contact_form_email():
    """Test contact form email sending"""
    # Create a minimal Flask app for testing
    app = Flask(__name__)
    app.template_folder = os.path.join(parent_dir, 'templates')
    
    # Load environment variables
    load_dotenv()
    
    # Configure server name for URL generation
    app.config['SERVER_NAME'] = os.getenv('SERVER_NAME', 'localhost:5000')
    app.config['PREFERRED_URL_SCHEME'] = 'https'
    app.config['APPLICATION_ROOT'] = '/'
    
    # Initialize mail
    mail = init_mail(app)
    
    # Create a test context
    with app.app_context():
        try:
            # Test parameters
            name = "Test User"
            email = "<EMAIL>"
            message = "This is a test message from the contact form."
            client_ip = "127.0.0.1"
            
            # Send contact form email
            send_contact_form_email(
                mail, 
                name, 
                email, 
                message, 
                client_ip=client_ip
            )
            
            print(f"Contact form email sent successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send contact form email: {str(e)}")
            print(f"Failed to send contact form email. Error: {str(e)}")
            return False

def test_order_confirmation_email():
    """Test order confirmation email sending"""
    # Create a minimal Flask app for testing
    app = Flask(__name__)
    app.template_folder = os.path.join(parent_dir, 'templates')
    
    # Load environment variables
    load_dotenv()
    
    # Configure server name for URL generation
    app.config['SERVER_NAME'] = os.getenv('SERVER_NAME', 'localhost:5000')
    app.config['PREFERRED_URL_SCHEME'] = 'https'
    app.config['APPLICATION_ROOT'] = '/'
    
    # Initialize mail
    mail = init_mail(app)
    
    # Import the function we need to test
    from modules.payment import send_confirmation_email
    
    # Create a test context
    with app.app_context():
        try:
            # Test parameters
            customer_email = "<EMAIL>"
            flask_session_id = "test-session-id"
            
            # Mock the generate_download_link function directly in the payment module
            # This avoids the URL generation error
            import modules.payment
            
            # Store the original function reference
            original_function = modules.payment.generate_download_link
            
            # Replace with a simple mock that returns a test URL
            modules.payment.generate_download_link = lambda session_id: "https://example.com/download/test-link"
            
            # Send confirmation email
            result = send_confirmation_email(customer_email, flask_session_id)
            
            # Restore the original function
            modules.payment.generate_download_link = original_function
            
            if result:
                print(f"Order confirmation email sent successfully to {customer_email}!")
            else:
                print("Failed to send order confirmation email.")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to send order confirmation email: {str(e)}")
            print(f"Failed to send order confirmation email. Error: {str(e)}")
            return False

if __name__ == "__main__":
    print("Testing basic email sending...")
    test_basic_email()
    
    print("\nTesting contact form email sending...")
    test_contact_form_email()
    
    print("\nTesting order confirmation email sending...")
    test_order_confirmation_email()
