Software License Agreement
==========================

**CKEditor&nbsp;5** (https://github.com/ckeditor/ckeditor5)<br>
Copyright (c) 2003–2024, [CKSource Holding sp. z o.o.](https://cksource.com) All rights reserved.

Licensed under a dual-license model, this software is available under:

* the [GNU General Public License Version 2 or later](https://www.gnu.org/licenses/gpl.html),
* or commercial license terms from CKSource Holding sp. z o.o.

For more information, see: [https://ckeditor.com/legal/ckeditor-licensing-options](https://ckeditor.com/legal/ckeditor-licensing-options).

Sources of Intellectual Property Included in CKEditor
-----------------------------------------------------

Where not otherwise indicated, all CKEditor content is authored by CKSource engineers and consists of CKSource-owned intellectual property. In some specific instances, CKEditor will incorporate work done by developers outside of CKSource with their express permission.

The following libraries are included in CKEditor under the [MIT license](https://opensource.org/licenses/MIT):

* BlurHash - Copyright (c) 2018 Wolt Enterprises (MIT license).
* color-convert - Copyright (c) 2011–2016 Heather Arthur <<EMAIL>>, copyright (c) 2016–2021 Josh Junon <<EMAIL>>.
* color-parse - Copyright (c) 2015 Dmitry Ivanov.
* Lodash - Copyright (c) JS Foundation and other contributors https://js.foundation/. Based on Underscore.js, copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors http://underscorejs.org/.
* Marked - Copyright (c) 2018+, MarkedJS (https://github.com/markedjs/), copyright (c) 2011–2018, Christopher Jeffrey (https://github.com/chjj/).
* Turndown - Copyright (c) 2017+ Dom Christie.
* turndown-plugin-gfm - Copyright (c) 2017+ Dom Christie.
* vanilla-colorful - Copyright (c) 2020 Serhii Kulykov <<EMAIL>>.

Trademarks
----------

**CKEditor** is a trademark of [CKSource Holding sp. z o.o.](https://cksource.com) All other brand and product names are trademarks, registered trademarks or service marks of their respective holders.
