#!/usr/bin/env python3
"""
Test download links functionality
"""
import os
import sys
import requests
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_download_routes():
    """Test if download routes are accessible"""
    print("Testing download route accessibility...")
    
    base_url = 'https://127.0.0.1:8282'
    
    # Test download route with session ID (should return 404 for non-existent session)
    print("\n1. Testing download by session ID:")
    try:
        response = requests.get(
            f'{base_url}/download/test_session_id',
            verify=False,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Test download route with download code (should return 404 for non-existent code)
    print("\n2. Testing download by code:")
    try:
        response = requests.get(
            f'{base_url}/download/code/test_download_code',
            verify=False,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
    except Exception as e:
        print(f"   Error: {str(e)}")

def test_database_download_codes():
    """Test download codes from database"""
    print("\n3. Testing download codes from database:")
    
    try:
        from modules import create_app
        from modules.models import CustomerData
        
        app = create_app()
        with app.app_context():
            # Get recent customer records
            customers = CustomerData.query.order_by(CustomerData.timestamp.desc()).limit(5).all()
            
            if customers:
                print(f"   Found {len(customers)} customer records")
                
                for customer in customers:
                    print(f"\n   Customer: {customer.customer_name}")
                    print(f"   Email: {customer.customer_email}")
                    print(f"   Download code: {customer.download_code}")
                    print(f"   Flask session ID: {customer.flask_session_id}")
                    print(f"   Download count: {customer.download_count}")
                    
                    # Test download by code
                    if customer.download_code:
                        print(f"   Testing download link for {customer.customer_name}...")
                        try:
                            response = requests.get(
                                f'https://127.0.0.1:8282/download/code/{customer.download_code}',
                                verify=False,
                                timeout=10,
                                allow_redirects=False  # Don't follow redirects to see what happens
                            )
                            print(f"   Download response status: {response.status_code}")
                            
                            if response.status_code == 302:
                                print(f"   Redirect location: {response.headers.get('Location', 'Not specified')}")
                            elif response.status_code == 200:
                                content_type = response.headers.get('Content-Type', 'Unknown')
                                content_length = response.headers.get('Content-Length', 'Unknown')
                                print(f"   Content-Type: {content_type}")
                                print(f"   Content-Length: {content_length}")
                                print("   ✅ Download successful!")
                            else:
                                print(f"   Response: {response.text[:200]}...")
                                
                        except Exception as e:
                            print(f"   Download test error: {str(e)}")
                    
                    print("   " + "-" * 50)
            else:
                print("   No customer records found in database")
                
    except Exception as e:
        print(f"   Database error: {str(e)}")

def test_download_file_existence():
    """Check if the download file exists"""
    print("\n4. Checking download file existence:")
    
    # Common locations for download files
    possible_paths = [
        "downloads/keto-no-8x10-cover-front.pdf",
        "assets/downloads/keto-no-8x10-cover-front.pdf",
        "static/downloads/keto-no-8x10-cover-front.pdf",
        "files/keto-no-8x10-cover-front.pdf",
        "keto-no-8x10-cover-front.pdf"
    ]
    
    for path in possible_paths:
        full_path = os.path.join(os.getcwd(), path)
        if os.path.exists(full_path):
            file_size = os.path.getsize(full_path)
            print(f"   ✅ Found: {path} ({file_size} bytes)")
        else:
            print(f"   ❌ Not found: {path}")

def create_test_customer_and_test_download():
    """Create a test customer record and test the download"""
    print("\n5. Creating test customer and testing download:")
    
    try:
        from modules import create_app
        from modules.models import db, CustomerData
        import secrets
        
        app = create_app()
        with app.app_context():
            # Create a test customer record
            test_download_code = f"cs_test_download_{int(datetime.now().timestamp())}"
            test_session_id = f"test_session_{secrets.token_urlsafe(16)}"
            
            customer_data = CustomerData(
                customer_name="Download Test Customer",
                customer_email="<EMAIL>",
                payment_status="paid",
                download_code=test_download_code,
                timestamp=datetime.now(),
                download_count=0,
                email_sent=False,
                flask_session_id=test_session_id
            )
            
            db.session.add(customer_data)
            db.session.commit()
            
            print(f"   ✅ Created test customer with download code: {test_download_code}")
            
            # Test the download link
            print(f"   Testing download link...")
            try:
                response = requests.get(
                    f'https://127.0.0.1:8282/download/code/{test_download_code}',
                    verify=False,
                    timeout=10,
                    allow_redirects=False
                )
                
                print(f"   Download response status: {response.status_code}")
                
                if response.status_code == 302:
                    print(f"   Redirect location: {response.headers.get('Location', 'Not specified')}")
                    print("   ✅ Download link working (redirecting to file)")
                elif response.status_code == 200:
                    content_type = response.headers.get('Content-Type', 'Unknown')
                    print(f"   Content-Type: {content_type}")
                    print("   ✅ Download link working (serving file directly)")
                elif response.status_code == 404:
                    print("   ❌ Download file not found")
                else:
                    print(f"   Response: {response.text[:200]}...")
                
                # Test download by session ID too
                print(f"   Testing download by session ID...")
                response2 = requests.get(
                    f'https://127.0.0.1:8282/download/{test_session_id}',
                    verify=False,
                    timeout=10,
                    allow_redirects=False
                )
                print(f"   Session download response status: {response2.status_code}")
                
            except Exception as e:
                print(f"   Download test error: {str(e)}")
            
            # Clean up - remove test customer
            db.session.delete(customer_data)
            db.session.commit()
            print(f"   ✅ Cleaned up test customer record")
            
    except Exception as e:
        print(f"   Error creating test customer: {str(e)}")

if __name__ == "__main__":
    print("Download Links Test")
    print("=" * 60)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test download routes
    test_download_routes()
    
    # Test database download codes
    test_database_download_codes()
    
    # Check file existence
    test_download_file_existence()
    
    # Create test customer and test download
    create_test_customer_and_test_download()
    
    print("\n" + "=" * 60)
    print("Download test completed!")