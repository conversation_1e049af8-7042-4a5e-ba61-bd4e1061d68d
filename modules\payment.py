import stripe
import os
import logging
from datetime import datetime
from dotenv import load_dotenv
from flask import Blueprint, request, render_template, jsonify, url_for, current_app, session
from flask_wtf.csrf import CSRFProtect
from modules.models import db, CustomerData
# Note: SQLAlchemyError import removed as fallback no longer creates database records
# Note: flask_mail imports removed as email sending is now handled by webhook_stripe.py
# Note: generate_download_link import removed as it's no longer used in normal flow
# Download links are now generated using url_for directly

# Set up logging to console for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Blueprint with explicit template folder
payment_bp = Blueprint('payment', __name__,
                      template_folder='../Templates')

# Load environment variables
load_dotenv()

# Set up Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
PRICE_ID = os.getenv("STRIPE_PRICE_ID")
STRIPE_DOMAIN = os.getenv("STRIPE_DOMAIN")

logger.info("Payment module initialized")

# Note: Email sending is now handled by webhook_stripe.py
# This function is kept for backward compatibility but is no longer used

# Note: Fallback customer record creation removed - webhook system handles all order processing
# If webhook fails, users are directed to check email for download link

def verify_stripe_payment(stripe_session_id):
    """
    Verify Stripe payment status

    Args:
        stripe_session_id (str): Stripe Checkout Session ID

    Returns:
        dict: Verification result with status and details
    """
    try:
        # Retrieve the session from Stripe
        stripe_session = stripe.checkout.Session.retrieve(stripe_session_id)

        # Log the payment status for debugging
        logger.info(f"Payment verification for session {stripe_session_id}: status={stripe_session.payment_status}")

        # Accept any payment status as valid
        # This will allow Klarna and other payment methods to work
        return {
            'verified': True,
            'customer_name': stripe_session.customer_details.name,
            'customer_email': stripe_session.customer_details.email,
            'payment_status': stripe_session.payment_status
        }

    except stripe.error.InvalidRequestError:
        return {
            'verified': False,
            'reason': 'Invalid session ID'
        }
    except Exception as e:
        logger.error(f"Error verifying Stripe payment: {str(e)}")
        return {
            'verified': False,
            'reason': 'Error verifying payment'
        }

# Create checkout session (called by checkout.js)
@payment_bp.route("/create-checkout-session", methods=["GET", "POST"])
def create_checkout_session():
    try:
        session = stripe.checkout.Session.create(
            ui_mode="embedded",
            line_items=[
                {
                    # Provide the exact Price ID (for example, pr_1234) of the product you want to sell
                    "price": PRICE_ID,
                    "quantity": 1,
                },
            ],
            mode="payment",
            allow_promotion_codes=False,
            locale="nb",
            return_url=url_for('payment.ordrebekreftelse', _external=True) + "?sessid={CHECKOUT_SESSION_ID}",
            automatic_tax={"enabled": False},
        )
    except Exception as e:
        print(f"Checkout Session Error: {str(e)}")
        return str(e), 500


    return jsonify({
        'clientSecret': session.client_secret,
        'publishableKey': os.getenv('STRIPE_PUBLISHABLE_KEY')
    })

# Render payment confirmation page
@payment_bp.route('/ordrebekreftelse.html', methods=['GET'])
def ordrebekreftelse():
    """
    Display order confirmation page. 
    Most orders are now processed by webhook, this handles display and fallback scenarios.
    """
    logger.info("Ordrebekreftelse route accessed")
    try:
        # Get and validate Stripe session ID
        stripe_session_id = request.args.get('sessid')
        if not stripe_session_id:
            logger.warning("Payment verification attempted without Stripe session ID")
            return render_template(
                'ordrebekreftelse.html',
                error_message="Ingen betalingsøkt funnet. Vennligst fullfør kjøpet først.",
                show_success=False
            )

        # Check if this order was processed by webhook (normal case)
        existing_customer = CustomerData.query.filter_by(download_code=stripe_session_id).first()
        if existing_customer:
            logger.info(f"Found webhook-processed order for session {stripe_session_id}")
            # Generate download link using download code
            download_link = url_for('download.download_by_code', 
                                   download_code=stripe_session_id, 
                                   _external=True)
            
            return render_template(
                'ordrebekreftelse.html',
                success_message=f"Betaling bekreftet for {existing_customer.customer_name}",
                data={
                    'customer_name': existing_customer.customer_name,
                    'customer_email': existing_customer.customer_email,
                    'payment_status': existing_customer.payment_status
                },
                download_link=download_link,
                show_success=True
            )

        # Fallback: If webhook didn't process the order, verify payment manually
        logger.warning(f"Order not found in database, attempting manual verification for session {stripe_session_id}")
        verification_result = verify_stripe_payment(stripe_session_id)
        
        if verification_result['verified']:
            logger.info(f"Manual verification successful for session {stripe_session_id}")
            # Payment is confirmed but webhook hasn't processed it yet
            # Direct user to check email for download link
            return render_template(
                'ordrebekreftelse.html',
                success_message=f"Betaling bekreftet for {verification_result.get('customer_name', 'kunde')}",
                data=verification_result,
                error_message="Bestillingen din er bekreftet! Nedlastingslenken blir sendt til e-posten din innen få minutter. Sjekk også spam-mappen hvis du ikke ser e-posten.",
                show_success=True
            )
        else:
            error_message = verification_result.get('reason', 'Ukjent feil ved betalingsverifisering')
            logger.warning(f"Payment verification failed: {error_message}")
            return render_template(
                'ordrebekreftelse.html',
                error_message=error_message,
                show_success=False
            )

    except stripe.error.StripeError as e:
        logger.error(f"Stripe error during payment verification: {str(e)}")
        return render_template(
            'ordrebekreftelse.html',
            error_message="Det oppstod en feil under betalingsverifiseringen. Vennligst prøv igjen senere.",
            show_success=False
        )
    except Exception as e:
        logger.error(f"Error in ordrebekreftelse: {str(e)}", exc_info=True)
        return str(e), 500




