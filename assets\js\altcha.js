/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/altcha@1.0.0/dist/altcha.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
var Ft=Object.defineProperty,Dt=(t,e,n)=>e in t?Ft(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,U=(t,e,n)=>Dt(t,"symbol"!=typeof e?e+"":e,n);const ht="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",jt=t=>Uint8Array.from(atob(t),(t=>t.charCodeAt(0))),nt=typeof self<"u"&&self.Blob&&new Blob([jt(ht)],{type:"text/javascript;charset=utf-8"});function Pt(t){let e;try{if(e=nt&&(self.URL||self.webkitURL).createObjectURL(nt),!e)throw"";const n=new Worker(e,{name:null==t?void 0:t.name});return n.addEventListener("error",(()=>{(self.URL||self.webkitURL).revokeObjectURL(e)})),n}catch{return new Worker("data:text/javascript;base64,"+ht,{name:null==t?void 0:t.name})}finally{e&&(self.URL||self.webkitURL).revokeObjectURL(e)}}function Re(){}function Wt(t,e){for(const n in e)t[n]=e[n];return t}function gt(t){return t()}function rt(){return Object.create(null)}function we(t){t.forEach(gt)}function mt(t){return"function"==typeof t}function Ut(t,e){return t!=t?e==e:t!==e||t&&"object"==typeof t||"function"==typeof t}function Kt(t){return 0===Object.keys(t).length}function Mt(t,e,n,o){if(t){const r=bt(t,e,n,o);return t[0](r)}}function bt(t,e,n,o){return t[1]&&o?Wt(n.ctx.slice(),t[1](o(e))):n.ctx}function Bt(t,e,n,o){if(t[2]&&o){const r=t[2](o(n));if(void 0===e.dirty)return r;if("object"==typeof r){const t=[],n=Math.max(e.dirty.length,r.length);for(let o=0;o<n;o+=1)t[o]=e.dirty[o]|r[o];return t}return e.dirty|r}return e.dirty}function Ot(t,e,n,o,r,i){if(r){const a=bt(e,n,o,i);t.p(a,r)}}function Jt(t){if(t.ctx.length>32){const e=[],n=t.ctx.length/32;for(let t=0;t<n;t++)e[t]=-1;return e}return-1}function L(t,e){t.appendChild(e)}function Qt(t,e,n){const o=$t(t);if(!o.getElementById(e)){const t=Z("style");t.id=e,t.textContent=n,qt(o,t)}}function $t(t){if(!t)return document;const e=t.getRootNode?t.getRootNode():t.ownerDocument;return e&&e.host?e:t.ownerDocument}function qt(t,e){return L(t.head||t,e),e.sheet}function X(t,e,n){t.insertBefore(e,n||null)}function Y(t){t.parentNode&&t.parentNode.removeChild(t)}function Z(t){return document.createElement(t)}function $(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function en(t){return document.createTextNode(t)}function Q(){return en(" ")}function Le(t,e,n,o){return t.addEventListener(e,n,o),()=>t.removeEventListener(e,n,o)}function a(t,e,n){null==n?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}function tn(t){return Array.from(t.childNodes)}function it(t,e,n){t.classList.toggle(e,!!n)}function nn(t,e,{bubbles:n=!1,cancelable:o=!1}={}){return new CustomEvent(t,{detail:e,bubbles:n,cancelable:o})}function rn(t){const e={};return t.childNodes.forEach((t=>{e[t.slot||"default"]=!0})),e}let pe;function ve(t){pe=t}function Ae(){if(!pe)throw new Error("Function called outside component initialization");return pe}function on(t){Ae().$$.on_mount.push(t)}function ln(t){Ae().$$.on_destroy.push(t)}function sn(){const t=Ae();return(e,n,{cancelable:o=!1}={})=>{const r=t.$$.callbacks[e];if(r){const i=nn(e,n,{cancelable:o});return r.slice().forEach((e=>{e.call(t,i)})),!i.defaultPrevented}return!0}}const ae=[],Ce=[];let fe=[];const ot=[],yt=Promise.resolve();let ze=!1;function vt(){ze||(ze=!0,yt.then(w))}function cn(){return vt(),yt}function Se(t){fe.push(t)}const Ne=new Set;let ce=0;function w(){if(0!==ce)return;const t=pe;do{try{for(;ce<ae.length;){const t=ae[ce];ce++,ve(t),an(t.$$)}}catch(t){throw ae.length=0,ce=0,t}for(ve(null),ae.length=0,ce=0;Ce.length;)Ce.pop()();for(let t=0;t<fe.length;t+=1){const e=fe[t];Ne.has(e)||(Ne.add(e),e())}fe.length=0}while(ae.length);for(;ot.length;)ot.pop()();ze=!1,Ne.clear(),ve(t)}function an(t){if(null!==t.fragment){t.update(),we(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(Se)}}function fn(t){const e=[],n=[];fe.forEach((o=>-1===t.indexOf(o)?e.push(o):n.push(o))),n.forEach((t=>t())),fe=e}const xe=new Set;let un,wt;function pt(t,e){t&&t.i&&(xe.delete(t),t.i(e))}function dn(t,e,n,o){if(t&&t.o){if(xe.has(t))return;xe.add(t),un.c.push((()=>{xe.delete(t)})),t.o(e)}}function hn(t,e,n){const{fragment:o,after_update:r}=t.$$;o&&o.m(e,n),Se((()=>{const e=t.$$.on_mount.map(gt).filter(mt);t.$$.on_destroy?t.$$.on_destroy.push(...e):we(e),t.$$.on_mount=[]})),r.forEach(Se)}function gn(t,e){const n=t.$$;null!==n.fragment&&(fn(n.after_update),we(n.on_destroy),n.fragment&&n.fragment.d(e),n.on_destroy=n.fragment=null,n.ctx=[])}function mn(t,e){-1===t.$$.dirty[0]&&(ae.push(t),vt(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function bn(t,e,n,o,r,i,a=null,l=[-1]){const s=pe;ve(t);const c=t.$$={fragment:null,ctx:[],props:i,update:Re,not_equal:r,bound:rt(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(s?s.$$.context:[])),callbacks:rt(),dirty:l,skip_bound:!1,root:e.target||s.$$.root};a&&a(c.root);let u=!1;if(c.ctx=n?n(t,e.props||{},((e,n,...o)=>{const i=o.length?o[0]:n;return c.ctx&&r(c.ctx[e],c.ctx[e]=i)&&(!c.skip_bound&&c.bound[e]&&c.bound[e](i),u&&mn(t,e)),n})):[],c.update(),u=!0,we(c.before_update),c.fragment=!!o&&o(c.ctx),e.target){if(e.hydrate){const t=tn(e.target);c.fragment&&c.fragment.l(t),t.forEach(Y)}else c.fragment&&c.fragment.c();e.intro&&pt(t.$$.fragment),hn(t,e.target,e.anchor),w()}ve(s)}function Ee(t,e,n,o){var r;const i=null==(r=n[t])?void 0:r.type;if(e="Boolean"===i&&"boolean"!=typeof e?null!=e:e,!o||!n[t])return e;if("toAttribute"===o)switch(i){case"Object":case"Array":return null==e?null:JSON.stringify(e);case"Boolean":return e?"":null;case"Number":return e??null;default:return e}else switch(i){case"Object":case"Array":return e&&JSON.parse(e);case"Boolean":default:return e;case"Number":return null!=e?+e:e}}function yn(t,e,n,o,r,i){let a=class extends wt{constructor(){super(t,n,r),this.$$p_d=e}static get observedAttributes(){return Object.keys(e).map((t=>(e[t].attribute||t).toLowerCase()))}};return Object.keys(e).forEach((t=>{Object.defineProperty(a.prototype,t,{get(){return this.$$c&&t in this.$$c?this.$$c[t]:this.$$d[t]},set(n){var o;n=Ee(t,n,e),this.$$d[t]=n,null==(o=this.$$c)||o.$set({[t]:n})}})})),o.forEach((t=>{Object.defineProperty(a.prototype,t,{get(){var e;return null==(e=this.$$c)?void 0:e[t]}})})),t.element=a,a}"function"==typeof HTMLElement&&(wt=class extends HTMLElement{constructor(t,e,n){super(),U(this,"$$ctor"),U(this,"$$s"),U(this,"$$c"),U(this,"$$cn",!1),U(this,"$$d",{}),U(this,"$$r",!1),U(this,"$$p_d",{}),U(this,"$$l",{}),U(this,"$$l_u",new Map),this.$$ctor=t,this.$$s=e,n&&this.attachShadow({mode:"open"})}addEventListener(t,e,n){if(this.$$l[t]=this.$$l[t]||[],this.$$l[t].push(e),this.$$c){const n=this.$$c.$on(t,e);this.$$l_u.set(e,n)}super.addEventListener(t,e,n)}removeEventListener(t,e,n){if(super.removeEventListener(t,e,n),this.$$c){const t=this.$$l_u.get(e);t&&(t(),this.$$l_u.delete(e))}}async connectedCallback(){if(this.$$cn=!0,!this.$$c){let t=function(t){return()=>{let e;return{c:function(){e=Z("slot"),"default"!==t&&a(e,"name",t)},m:function(t,n){X(t,e,n)},d:function(t){t&&Y(e)}}}};if(await Promise.resolve(),!this.$$cn||this.$$c)return;const e={},n=rn(this);for(const o of this.$$s)o in n&&(e[o]=[t(o)]);for(const t of this.attributes){const e=this.$$g_p(t.name);e in this.$$d||(this.$$d[e]=Ee(e,t.value,this.$$p_d,"toProp"))}for(const t in this.$$p_d)!(t in this.$$d)&&void 0!==this[t]&&(this.$$d[t]=this[t],delete this[t]);this.$$c=new this.$$ctor({target:this.shadowRoot||this,props:{...this.$$d,$$slots:e,$$scope:{ctx:[]}}});const o=()=>{this.$$r=!0;for(const t in this.$$p_d)if(this.$$d[t]=this.$$c.$$.ctx[this.$$c.$$.props[t]],this.$$p_d[t].reflect){const e=Ee(t,this.$$d[t],this.$$p_d,"toAttribute");null==e?this.removeAttribute(this.$$p_d[t].attribute||t):this.setAttribute(this.$$p_d[t].attribute||t,e)}this.$$r=!1};this.$$c.$$.after_update.push(o),o();for(const t in this.$$l)for(const e of this.$$l[t]){const n=this.$$c.$on(t,e);this.$$l_u.set(e,n)}this.$$l={}}}attributeChangedCallback(t,e,n){var o;this.$$r||(t=this.$$g_p(t),this.$$d[t]=Ee(t,n,this.$$p_d,"toProp"),null==(o=this.$$c)||o.$set({[t]:this.$$d[t]}))}disconnectedCallback(){this.$$cn=!1,Promise.resolve().then((()=>{!this.$$cn&&this.$$c&&(this.$$c.$destroy(),this.$$c=void 0)}))}$$g_p(t){return Object.keys(this.$$p_d).find((e=>this.$$p_d[e].attribute===t||!this.$$p_d[e].attribute&&e.toLowerCase()===t))||t}});class vn{constructor(){U(this,"$$"),U(this,"$$set")}$destroy(){gn(this,1),this.$destroy=Re}$on(t,e){if(!mt(e))return Re;const n=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return n.push(e),()=>{const t=n.indexOf(e);-1!==t&&n.splice(t,1)}}$set(t){this.$$set&&!Kt(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}const pn="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(pn);const _t=new TextEncoder;function wn(t){return[...new Uint8Array(t)].map((t=>t.toString(16).padStart(2,"0"))).join("")}async function _n(t,e="SHA-256",n=1e5){const o=Date.now().toString(16);t||(t=Math.round(Math.random()*n));return{algorithm:e,challenge:await kt(o,t,e),salt:o,signature:""}}async function kt(t,e,n){return wn(await crypto.subtle.digest(n.toUpperCase(),_t.encode(t+e)))}function kn(t,e,n="SHA-256",o=1e6,r=0){const i=new AbortController,a=Date.now();return{promise:(async()=>{for(let l=r;l<=o;l+=1){if(i.signal.aborted)return null;if(await kt(e,l,n)===t)return{number:l,took:Date.now()-a}}return null})(),controller:i}}function xn(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch{}}function En(t){const e=atob(t),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}function Rn(t,e=12){const n=new Uint8Array(e);for(let o=0;o<e;o++)n[o]=t%256,t=Math.floor(t/256);return n}async function Cn(t,e="",n=1e6,o=0){const r="AES-GCM",i=new AbortController,a=Date.now();let l=null,s=null;try{s=En(t);const n=await crypto.subtle.digest("SHA-256",_t.encode(e));l=await crypto.subtle.importKey("raw",n,r,!1,["decrypt"])}catch{return{promise:Promise.reject(),controller:i}}return{promise:(async()=>{for(let t=o;t<=n;t+=1){if(i.signal.aborted||!l||!s)return null;try{const e=await crypto.subtle.decrypt({name:r,iv:Rn(t)},l,s);if(e)return{clearText:(new TextDecoder).decode(e),took:Date.now()-a}}catch{}}return null})(),controller:i}}var m=(t=>(t.ERROR="error",t.VERIFIED="verified",t.VERIFYING="verifying",t.UNVERIFIED="unverified",t.EXPIRED="expired",t))(m||{});function In(t){Qt(t,"svelte-ddsc3z",'.altcha.svelte-ddsc3z.svelte-ddsc3z{background:var(--altcha-color-base, transparent);border:var(--altcha-border-width, 1px) solid var(--altcha-color-border, #a0a0a0);border-radius:var(--altcha-border-radius, 3px);color:var(--altcha-color-text, currentColor);display:flex;flex-direction:column;max-width:var(--altcha-max-width, 260px);position:relative;text-align:left}.altcha.svelte-ddsc3z.svelte-ddsc3z:focus-within{border-color:var(--altcha-color-border-focus, currentColor)}.altcha[data-floating].svelte-ddsc3z.svelte-ddsc3z{background:var(--altcha-color-base, white);display:none;filter:drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.2));left:-100%;position:fixed;top:-100%;width:var(--altcha-max-width, 260px);z-index:999999}.altcha[data-floating=top].svelte-ddsc3z .altcha-anchor-arrow.svelte-ddsc3z{border-bottom-color:transparent;border-top-color:var(--altcha-color-border, #a0a0a0);bottom:-12px;top:auto}.altcha[data-floating=bottom].svelte-ddsc3z.svelte-ddsc3z:focus-within::after{border-bottom-color:var(--altcha-color-border-focus, currentColor)}.altcha[data-floating=top].svelte-ddsc3z.svelte-ddsc3z:focus-within::after{border-top-color:var(--altcha-color-border-focus, currentColor)}.altcha[data-floating].svelte-ddsc3z.svelte-ddsc3z:not([data-state=unverified]){display:block}.altcha-anchor-arrow.svelte-ddsc3z.svelte-ddsc3z{border:6px solid transparent;border-bottom-color:var(--altcha-color-border, #a0a0a0);content:"";height:0;left:12px;position:absolute;top:-12px;width:0}.altcha-main.svelte-ddsc3z.svelte-ddsc3z{align-items:center;display:flex;gap:0.4rem;padding:0.7rem}.altcha-label.svelte-ddsc3z.svelte-ddsc3z{flex-grow:1}.altcha-label.svelte-ddsc3z label.svelte-ddsc3z{cursor:pointer}.altcha-logo.svelte-ddsc3z.svelte-ddsc3z{color:currentColor;opacity:0.3}.altcha-logo.svelte-ddsc3z.svelte-ddsc3z:hover{opacity:1}.altcha-error.svelte-ddsc3z.svelte-ddsc3z{color:var(--altcha-color-error-text, #f23939);display:flex;font-size:0.85rem;gap:0.3rem;padding:0 0.7rem 0.7rem}.altcha-footer.svelte-ddsc3z.svelte-ddsc3z{align-items:center;background-color:var(--altcha-color-footer-bg, transparent);display:flex;font-size:0.75rem;opacity:0.4;padding:0.2rem 0.7rem;text-align:right}.altcha-footer.svelte-ddsc3z.svelte-ddsc3z:hover{opacity:1}.altcha-footer.svelte-ddsc3z>.svelte-ddsc3z:first-child{flex-grow:1}.altcha-footer.svelte-ddsc3z a{color:currentColor}.altcha-checkbox.svelte-ddsc3z.svelte-ddsc3z{display:flex;align-items:center;height:24px;width:24px}.altcha-checkbox.svelte-ddsc3z input.svelte-ddsc3z{width:18px;height:18px;margin:0}.altcha-hidden.svelte-ddsc3z.svelte-ddsc3z{display:none}.altcha-spinner.svelte-ddsc3z.svelte-ddsc3z{animation:svelte-ddsc3z-altcha-spinner 0.75s infinite linear;transform-origin:center}@keyframes svelte-ddsc3z-altcha-spinner{100%{transform:rotate(360deg)}}')}function lt(t){let e,n,o;return{c(){e=$("svg"),n=$("path"),o=$("path"),a(n,"d","M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z"),a(n,"fill","currentColor"),a(n,"opacity",".25"),a(o,"d","M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"),a(o,"fill","currentColor"),a(o,"class","altcha-spinner svelte-ddsc3z"),a(e,"width","24"),a(e,"height","24"),a(e,"viewBox","0 0 24 24"),a(e,"xmlns","http://www.w3.org/2000/svg")},m(t,r){X(t,e,r),L(e,n),L(e,o)},d(t){t&&Y(e)}}}function Ln(t){let e,n,o=t[11].label+"";return{c(){e=Z("label"),a(e,"for",n=t[4]+"_checkbox"),a(e,"class","svelte-ddsc3z")},m(t,n){X(t,e,n),e.innerHTML=o},p(t,r){2048&r[0]&&o!==(o=t[11].label+"")&&(e.innerHTML=o),16&r[0]&&n!==(n=t[4]+"_checkbox")&&a(e,"for",n)},d(t){t&&Y(e)}}}function Nn(t){let e,n=t[11].verifying+"";return{c(){e=Z("span")},m(t,o){X(t,e,o),e.innerHTML=n},p(t,o){2048&o[0]&&n!==(n=t[11].verifying+"")&&(e.innerHTML=n)},d(t){t&&Y(e)}}}function zn(t){let e,n,o,r=t[11].verified+"";return{c(){e=Z("span"),n=Q(),o=Z("input"),a(o,"type","hidden"),a(o,"name",t[4]),o.value=t[6]},m(t,i){X(t,e,i),e.innerHTML=r,X(t,n,i),X(t,o,i)},p(t,n){2048&n[0]&&r!==(r=t[11].verified+"")&&(e.innerHTML=r),16&n[0]&&a(o,"name",t[4]),64&n[0]&&(o.value=t[6])},d(t){t&&(Y(e),Y(n),Y(o))}}}function st(t){let e,n,o,r,i,l,s;return{c(){e=Z("div"),n=Z("a"),o=$("svg"),r=$("path"),i=$("path"),l=$("path"),a(r,"d","M2.33955 16.4279C5.88954 20.6586 12.1971 21.2105 16.4279 17.6604C18.4699 15.947 19.6548 13.5911 19.9352 11.1365L17.9886 10.4279C17.8738 12.5624 16.909 14.6459 15.1423 16.1284C11.7577 18.9684 6.71167 18.5269 3.87164 15.1423C1.03163 11.7577 1.4731 6.71166 4.8577 3.87164C8.24231 1.03162 13.2883 1.4731 16.1284 4.8577C16.9767 5.86872 17.5322 7.02798 17.804 8.2324L19.9522 9.01429C19.7622 7.07737 19.0059 5.17558 17.6604 3.57212C14.1104 -0.658624 7.80283 -1.21043 3.57212 2.33956C-0.658625 5.88958 -1.21046 12.1971 2.33955 16.4279Z"),a(r,"fill","currentColor"),a(i,"d","M3.57212 2.33956C1.65755 3.94607 0.496389 6.11731 0.12782 8.40523L2.04639 9.13961C2.26047 7.15832 3.21057 5.25375 4.8577 3.87164C8.24231 1.03162 13.2883 1.4731 16.1284 4.8577L13.8302 6.78606L19.9633 9.13364C19.7929 7.15555 19.0335 5.20847 17.6604 3.57212C14.1104 -0.658624 7.80283 -1.21043 3.57212 2.33956Z"),a(i,"fill","currentColor"),a(l,"d","M7 10H5C5 12.7614 7.23858 15 10 15C12.7614 15 15 12.7614 15 10H13C13 11.6569 11.6569 13 10 13C8.3431 13 7 11.6569 7 10Z"),a(l,"fill","currentColor"),a(o,"width","22"),a(o,"height","22"),a(o,"viewBox","0 0 20 20"),a(o,"fill","none"),a(o,"xmlns","http://www.w3.org/2000/svg"),a(n,"href",xt),a(n,"target","_blank"),a(n,"class","altcha-logo svelte-ddsc3z"),a(n,"aria-label",s=t[11].ariaLinkLabel)},m(t,a){X(t,e,a),L(e,n),L(n,o),L(o,r),L(o,i),L(o,l)},p(t,e){2048&e[0]&&s!==(s=t[11].ariaLinkLabel)&&a(n,"aria-label",s)},d(t){t&&Y(e)}}}function ct(t){let e,n,o,r;function i(t,e){return t[7]===m.EXPIRED?An:Sn}let l=i(t),s=l(t);return{c(){e=Z("div"),n=$("svg"),o=$("path"),r=Q(),s.c(),a(o,"stroke-linecap","round"),a(o,"stroke-linejoin","round"),a(o,"d","M6 18L18 6M6 6l12 12"),a(n,"width","14"),a(n,"height","14"),a(n,"xmlns","http://www.w3.org/2000/svg"),a(n,"fill","none"),a(n,"viewBox","0 0 24 24"),a(n,"stroke-width","1.5"),a(n,"stroke","currentColor"),a(e,"class","altcha-error svelte-ddsc3z")},m(t,i){X(t,e,i),L(e,n),L(n,o),L(e,r),s.m(e,null)},p(t,n){l===(l=i(t))&&s?s.p(t,n):(s.d(1),s=l(t),s&&(s.c(),s.m(e,null)))},d(t){t&&Y(e),s.d()}}}function Sn(t){let e,n=t[11].error+"";return{c(){e=Z("div"),a(e,"title",t[5])},m(t,o){X(t,e,o),e.innerHTML=n},p(t,o){2048&o[0]&&n!==(n=t[11].error+"")&&(e.innerHTML=n),32&o[0]&&a(e,"title",t[5])},d(t){t&&Y(e)}}}function An(t){let e,n=t[11].expired+"";return{c(){e=Z("div"),a(e,"title",t[5])},m(t,o){X(t,e,o),e.innerHTML=n},p(t,o){2048&o[0]&&n!==(n=t[11].expired+"")&&(e.innerHTML=n),32&o[0]&&a(e,"title",t[5])},d(t){t&&Y(e)}}}function at(t){let e,n,o=t[11].footer+"";return{c(){e=Z("div"),n=Z("div"),a(n,"class","svelte-ddsc3z"),a(e,"class","altcha-footer svelte-ddsc3z")},m(t,r){X(t,e,r),L(e,n),n.innerHTML=o},p(t,e){2048&e[0]&&o!==(o=t[11].footer+"")&&(n.innerHTML=o)},d(t){t&&Y(e)}}}function ft(t){let e;return{c(){e=Z("div"),a(e,"class","altcha-anchor-arrow svelte-ddsc3z")},m(n,o){X(n,e,o),t[48](e)},p:Re,d(n){n&&Y(e),t[48](null)}}}function Tn(t){let e,n,o,r,i,l,s,c,u,d,f,h,p,g,$,b,v;const y=t[46].default,w=Mt(y,t,t[45],null);let x=t[7]===m.VERIFYING&&lt();function k(t,e){return t[7]===m.VERIFIED?zn:t[7]===m.VERIFYING?Nn:Ln}let E=k(t),R=E(t),C=(!0!==t[3]||t[12])&&st(t),I=(t[5]||t[7]===m.EXPIRED)&&ct(t),N=t[11].footer&&(!0!==t[2]||t[12])&&at(t),z=t[1]&&ft(t);return{c(){w&&w.c(),e=Q(),n=Z("div"),o=Z("div"),x&&x.c(),r=Q(),i=Z("div"),l=Z("input"),u=Q(),d=Z("div"),R.c(),f=Q(),C&&C.c(),h=Q(),I&&I.c(),p=Q(),N&&N.c(),g=Q(),z&&z.c(),a(l,"type","checkbox"),a(l,"id",s=t[4]+"_checkbox"),l.required=c="onsubmit"!==t[0]&&(!t[1]||"off"!==t[0]),a(l,"class","svelte-ddsc3z"),a(i,"class","altcha-checkbox svelte-ddsc3z"),it(i,"altcha-hidden",t[7]===m.VERIFYING),a(d,"class","altcha-label svelte-ddsc3z"),a(o,"class","altcha-main svelte-ddsc3z"),a(n,"class","altcha svelte-ddsc3z"),a(n,"data-state",t[7]),a(n,"data-floating",t[1])},m(a,s){w&&w.m(a,s),X(a,e,s),X(a,n,s),L(n,o),x&&x.m(o,null),L(o,r),L(o,i),L(i,l),l.checked=t[8],L(o,u),L(o,d),R.m(d,null),L(o,f),C&&C.m(o,null),L(n,h),I&&I.m(n,null),L(n,p),N&&N.m(n,null),L(n,g),z&&z.m(n,null),t[49](n),$=!0,b||(v=[Le(l,"change",t[47]),Le(l,"change",t[13]),Le(l,"invalid",t[14])],b=!0)},p(t,e){w&&w.p&&(!$||16384&e[1])&&Ot(w,y,t,t[45],$?Bt(y,t[45],e,null):Jt(t[45]),null),t[7]===m.VERIFYING?x||(x=lt(),x.c(),x.m(o,r)):x&&(x.d(1),x=null),(!$||16&e[0]&&s!==(s=t[4]+"_checkbox"))&&a(l,"id",s),(!$||3&e[0]&&c!==(c="onsubmit"!==t[0]&&(!t[1]||"off"!==t[0])))&&(l.required=c),256&e[0]&&(l.checked=t[8]),(!$||128&e[0])&&it(i,"altcha-hidden",t[7]===m.VERIFYING),E===(E=k(t))&&R?R.p(t,e):(R.d(1),R=E(t),R&&(R.c(),R.m(d,null))),!0!==t[3]||t[12]?C?C.p(t,e):(C=st(t),C.c(),C.m(o,null)):C&&(C.d(1),C=null),t[5]||t[7]===m.EXPIRED?I?I.p(t,e):(I=ct(t),I.c(),I.m(n,p)):I&&(I.d(1),I=null),t[11].footer&&(!0!==t[2]||t[12])?N?N.p(t,e):(N=at(t),N.c(),N.m(n,g)):N&&(N.d(1),N=null),t[1]?z?z.p(t,e):(z=ft(t),z.c(),z.m(n,null)):z&&(z.d(1),z=null),(!$||128&e[0])&&a(n,"data-state",t[7]),(!$||2&e[0])&&a(n,"data-floating",t[1])},i(t){$||(pt(w,t),$=!0)},o(t){dn(w,t),$=!1},d(o){o&&(Y(e),Y(n)),w&&w.d(o),x&&x.d(),R.d(),C&&C.d(),I&&I.d(),N&&N.d(),z&&z.d(),t[49](null),b=!1,we(v)}}}const ut="Visit Altcha.org",xt="https://altcha.org/";function dt(t){return JSON.parse(t)}function Zn(t,e,n){var o,r;let i,a,l,s,{$$slots:c={},$$scope:u}=e,{auto:d}=e,{blockspam:f}=e,{challengeurl:h}=e,{challengejson:p}=e,{debug:g=!1}=e,{delay:$=0}=e,{expire:b}=e,{floating:v}=e,{floatinganchor:y}=e,{floatingoffset:w}=e,{hidefooter:x=!1}=e,{hidelogo:k=!1}=e,{name:E="altcha"}=e,{maxnumber:L=1e6}=e,{mockerror:R=!1}=e,{obfuscated:C}=e,{plugins:I}=e,{refetchonexpire:Z=!0}=e,{spamfilter:N=!1}=e,{strings:z}=e,{test:Y=!1}=e,{verifyurl:X}=e,{workers:S=Math.min(16,navigator.hardwareConcurrency||8)}=e,{workerurl:A}=e;const V=sn(),G=["SHA-256","SHA-384","SHA-512"],T=null==(r=null==(o=document.documentElement.lang)?void 0:o.split("-"))?void 0:r[0];let H,U=!1,F=null,j=null,_=null,K=null,M=null,D=null,W=[],P=m.UNVERIFIED;function B(t,e){return btoa(JSON.stringify({algorithm:t.algorithm,challenge:t.challenge,number:e.number,salt:t.salt,signature:t.signature,test:!!Y||void 0,took:e.took}))}function O(){h&&Z&&P===m.VERIFIED?wt():bt(m.EXPIRED,s.expired)}function Q(...t){(g||t.some((t=>t instanceof Error)))&&console[t[0]instanceof Error?"error":"log"]("ALTCHA",`[name=${E}]`,...t)}function J(t){const e=t.target;v&&e&&!H.contains(e)&&(P===m.VERIFIED||"off"===d&&P===m.UNVERIFIED)&&n(9,H.style.display="none",H)}function q(){v&&P!==m.UNVERIFIED&&it()}function tt(t){P===m.UNVERIFIED&&wt()}function et(t){_&&"onsubmit"===d?P===m.UNVERIFIED?(t.preventDefault(),t.stopPropagation(),wt().then((()=>{null==_||_.requestSubmit()}))):P!==m.VERIFIED&&(t.preventDefault(),t.stopPropagation(),P===m.VERIFYING&&ot()):_&&v&&"off"===d&&P===m.UNVERIFIED&&(t.preventDefault(),t.stopPropagation(),n(9,H.style.display="block",H),it())}function nt(){bt()}function ot(){P===m.VERIFYING&&s.waitAlert&&alert(s.waitAlert)}function rt(){v&&it()}function it(t=20){if(H)if(j||(j=(y?document.querySelector(y):null==_?void 0:_.querySelector('input[type="submit"], button[type="submit"], button:not([type="button"]):not([type="reset"])'))||_),j){const e=parseInt(w,10)||12,o=j.getBoundingClientRect(),r=H.getBoundingClientRect(),i=document.documentElement.clientHeight,a=document.documentElement.clientWidth,l="auto"===v?o.bottom+r.height+e+t>i:"top"===v,s=Math.max(t,Math.min(a-t-r.width,o.left+o.width/2-r.width/2));if(n(9,H.style.top=l?o.top-(r.height+e)+"px":`${o.bottom+e}px`,H),n(9,H.style.left=`${s}px`,H),H.setAttribute("data-floating",l?"top":"bottom"),F){const t=F.getBoundingClientRect();n(10,F.style.left=o.left-s+o.width/2-t.width/2+"px",F)}}else Q("unable to find floating anchor element")}async function at(t){if(!X)throw new Error("Attribute verifyurl not set.");Q("requesting server verification from",X);const e={payload:t};if(N){const{blockedCountries:t,classifier:n,disableRules:o,email:r,expectedLanguages:i,expectedCountries:a,fields:l,ipAddress:s,text:c,timeZone:u}="ipAddress"===N?{blockedCountries:void 0,classifier:void 0,disableRules:void 0,email:!1,expectedCountries:void 0,expectedLanguages:void 0,fields:!1,ipAddress:void 0,text:void 0,timeZone:void 0}:"object"==typeof N?N:{blockedCountries:void 0,classifier:void 0,disableRules:void 0,email:void 0,expectedCountries:void 0,expectedLanguages:void 0,fields:void 0,ipAddress:void 0,text:void 0,timeZone:void 0};e.blockedCountries=t,e.classifier=n,e.disableRules=o,e.email=!1===r?void 0:function(t){var e;const n=null==_?void 0:_.querySelector("string"==typeof t?`input[name="${t}"]`:'input[type="email"]:not([data-no-spamfilter])');return(null==(e=null==n?void 0:n.value)?void 0:e.slice(n.value.indexOf("@")))||void 0}(r),e.expectedCountries=a,e.expectedLanguages=i||(T?[T]:void 0),e.fields=!1===l?void 0:function(t){return[...(null==_?void 0:_.querySelectorAll(null!=t&&t.length?t.map((t=>`input[name="${t}"]`)).join(", "):'input[type="text"]:not([data-no-spamfilter]), textarea:not([data-no-spamfilter])'))||[]].reduce(((t,e)=>{const n=e.name,o=e.value;return n&&o&&(t[n]=/\n/.test(o)?o.replace(new RegExp("(?<!\\r)\\n","g"),"\r\n"):o),t}),{})}(l),e.ipAddress=!1===s?void 0:s||"auto",e.text=c,e.timeZone=!1===u?void 0:u||xn()}const o=await fetch(X,{body:JSON.stringify(e),headers:{"content-type":"application/json"},method:"POST"});if(200!==o.status)throw new Error(`Server responded with ${o.status}.`);const r=await o.json();if(null!=r&&r.payload&&n(6,D=r.payload),V("serververification",r),f&&"BAD"===r.classification)throw new Error("SpamFilter returned negative classification.")}function lt(t){Q("expire",t),M&&(clearTimeout(M),M=null),t<1?O():M=setTimeout(O,t)}function st(t){Q("floating",t),v!==t&&(n(9,H.style.left="",H),n(9,H.style.top="",H)),n(1,v=!0===t||""===t?"auto":!1===t||"false"===t?void 0:v),v?(d||n(0,d="onsubmit"),document.addEventListener("scroll",q),document.addEventListener("click",J),window.addEventListener("resize",rt)):"onsubmit"===d&&n(0,d=void 0)}function ct(t){if(!t.algorithm)throw new Error("Invalid challenge. Property algorithm is missing.");if(void 0===t.signature)throw new Error("Invalid challenge. Property signature is missing.");if(!G.includes(t.algorithm.toUpperCase()))throw new Error(`Unknown algorithm value. Allowed values: ${G.join(", ")}`);if(!t.challenge||t.challenge.length<40)throw new Error("Challenge is too short. Min. 40 chars.");if(!t.salt||t.salt.length<10)throw new Error("Salt is too short. Min. 10 chars.")}async function ft(t){let e=null;if("Worker"in window){try{e=await async function(t,e=("number"==typeof Y?Y:L),n=Math.ceil(S)){const o=[];n=Math.min(16,Math.max(1,n));for(let t=0;t<n;t++)o.push(altchaCreateWorker(A));const r=Math.ceil(e/n),i=await Promise.all(o.map(((e,n)=>{const i=n*r;return new Promise((n=>{e.addEventListener("message",(t=>{if(t.data)for(const t of o)t!==e&&t.postMessage({type:"abort"});n(t.data)})),e.postMessage({payload:t,max:i+r,start:i,type:"work"})}))})));for(const t of o)t.terminate();return i.find((t=>!!t))||null}(t,t.maxnumber)}catch(t){Q(t)}if(void 0!==(null==e?void 0:e.number)||"obfuscated"in t)return{data:t,solution:e}}if("obfuscated"in t){const e=await Cn(t.obfuscated,t.key,t.maxnumber);return{data:t,solution:await e.promise}}return{data:t,solution:await kn(t.challenge,t.salt,t.algorithm,t.maxnumber||L).promise}}async function ht(){if(!C)return void n(7,P=m.ERROR);const t=W.find((t=>"obfuscation"===t.constructor.pluginName));return t&&"clarify"in t?"clarify"in t&&"function"==typeof t.clarify?t.clarify():void 0:(n(7,P=m.ERROR),void Q("Plugin `obfuscation` not found. Import `altcha/plugins/obfuscation` to load it."))}function pt(t){void 0!==t.obfuscated&&n(24,C=t.obfuscated),void 0!==t.auto&&(n(0,d=t.auto),"onload"===d&&(C?ht():wt())),void 0!==t.blockspam&&n(16,f=!!t.blockspam),void 0!==t.floatinganchor&&n(20,y=t.floatinganchor),void 0!==t.delay&&n(18,$=t.delay),void 0!==t.floatingoffset&&n(21,w=t.floatingoffset),void 0!==t.floating&&st(t.floating),void 0!==t.expire&&(lt(t.expire),n(19,b=t.expire)),t.challenge&&(ct(t.challenge),a=t.challenge),void 0!==t.challengeurl&&n(15,h=t.challengeurl),void 0!==t.debug&&n(17,g=!!t.debug),void 0!==t.hidefooter&&n(2,x=!!t.hidefooter),void 0!==t.hidelogo&&n(3,k=!!t.hidelogo),void 0!==t.maxnumber&&n(22,L=+t.maxnumber),void 0!==t.mockerror&&n(23,R=!!t.mockerror),void 0!==t.name&&n(4,E=t.name),void 0!==t.refetchonexpire&&n(25,Z=!!t.refetchonexpire),void 0!==t.spamfilter&&n(26,N="object"==typeof t.spamfilter?t.spamfilter:!!t.spamfilter),t.strings&&n(44,l=t.strings),void 0!==t.test&&n(27,Y="number"==typeof t.test?t.test:!!t.test),void 0!==t.verifyurl&&n(28,X=t.verifyurl),void 0!==t.workers&&n(29,S=+t.workers),void 0!==t.workerurl&&n(30,A=t.workerurl)}function gt(){return{auto:d,blockspam:f,challengeurl:h,debug:g,delay:$,expire:b,floating:v,floatinganchor:y,floatingoffset:w,hidefooter:x,hidelogo:k,name:E,maxnumber:L,mockerror:R,obfuscated:C,refetchonexpire:Z,spamfilter:N,strings:s,test:Y,verifyurl:X,workers:S,workerurl:A}}function mt(){return j}function $t(){return P}function bt(t=m.UNVERIFIED,e=null){M&&(clearTimeout(M),M=null),n(8,U=!1),n(5,K=e),n(6,D=null),n(7,P=t)}function vt(t){j=t}function yt(t,e=null){n(7,P=t),n(5,K=e)}async function wt(){return bt(m.VERIFYING),await new Promise((t=>setTimeout(t,$||0))),async function(){var t;if(R)throw Q("mocking error"),new Error("Mocked error.");if(a)return Q("using provided json data"),a;if(Y)return Q("generating test challenge",{test:Y}),_n("boolean"!=typeof Y?+Y:void 0);{if(!h&&_){const t=_.getAttribute("action");null!=t&&t.includes("/form/")&&n(15,h=t+"/altcha")}if(!h)throw new Error("Attribute challengeurl not set.");Q("fetching challenge from",h);const e=await fetch(h,{headers:N?{"x-altcha-spam-filter":"1"}:{}});if(200!==e.status)throw new Error(`Server responded with ${e.status}.`);const o=e.headers.get("Expires"),r=e.headers.get("X-Altcha-Config"),i=await e.json(),a=new URLSearchParams(null==(t=i.salt.split("?"))?void 0:t[1]),l=a.get("expires")||a.get("expire");if(l){const t=new Date(1e3*+l),e=isNaN(t.getTime())?0:t.getTime()-Date.now();e>0&&lt(e)}if(r)try{const t=JSON.parse(r);t&&"object"==typeof t&&(t.verifyurl&&(t.verifyurl=new URL(t.verifyurl,new URL(h)).toString()),pt(t))}catch(t){Q("unable to configure from X-Altcha-Config",t)}if(!b&&null!=o&&o.length){const t=Date.parse(o);if(t){const e=t-Date.now();e>0&&lt(e)}}return i}}().then((t=>(ct(t),Q("challenge",t),ft(t)))).then((({data:t,solution:e})=>{if(Q("solution",e),"challenge"in t&&e&&!("clearText"in e)){if(void 0===(null==e?void 0:e.number))throw Q("Unable to find a solution. Ensure that the 'maxnumber' attribute is greater than the randomly generated number."),new Error("Unexpected result returned.");if(X)return at(B(t,e));n(6,D=B(t,e)),Q("payload",D)}})).then((()=>{cn().then((()=>{n(7,P=m.VERIFIED),Q("verified"),V("verified",{payload:D})}))})).catch((t=>{Q(t),n(7,P=m.ERROR),n(5,K=t.message)}))}return ln((()=>{(function(){for(const t of W)t.destroy()})(),_&&(_.removeEventListener("submit",et),_.removeEventListener("reset",nt),_.removeEventListener("focusin",tt),_=null),M&&(clearTimeout(M),M=null),document.removeEventListener("click",J),document.removeEventListener("scroll",q),window.removeEventListener("resize",rt)})),on((()=>{Q("mounted","1.0.0"),Q("workers",S),function(){const t=void 0!==I?I.split(","):void 0;for(const e of globalThis.altchaPlugins)(!t||t.includes(e.pluginName))&&W.push(new e({el:H,clarify:ht,dispatch:V,getConfiguration:gt,getFloatingAnchor:mt,getState:$t,log:Q,reset:bt,solve:ft,setState:yt,setFloatingAnchor:vt,verify:wt}))}(),Q("plugins",W.length?W.map((t=>t.constructor.pluginName)).join(", "):"none"),Y&&Q("using test mode"),b&&lt(b),void 0!==d&&Q("auto",d),void 0!==v&&st(v),_=H.closest("form"),_&&(_.addEventListener("submit",et,{capture:!0}),_.addEventListener("reset",nt),"onfocus"===d&&_.addEventListener("focusin",tt)),"onload"===d&&(C?ht():wt()),i&&(x||k)&&Q("Attributes hidefooter and hidelogo ignored because usage with free API Keys requires attribution."),requestAnimationFrame((()=>{V("load")}))})),t.$$set=t=>{"auto"in t&&n(0,d=t.auto),"blockspam"in t&&n(16,f=t.blockspam),"challengeurl"in t&&n(15,h=t.challengeurl),"challengejson"in t&&n(31,p=t.challengejson),"debug"in t&&n(17,g=t.debug),"delay"in t&&n(18,$=t.delay),"expire"in t&&n(19,b=t.expire),"floating"in t&&n(1,v=t.floating),"floatinganchor"in t&&n(20,y=t.floatinganchor),"floatingoffset"in t&&n(21,w=t.floatingoffset),"hidefooter"in t&&n(2,x=t.hidefooter),"hidelogo"in t&&n(3,k=t.hidelogo),"name"in t&&n(4,E=t.name),"maxnumber"in t&&n(22,L=t.maxnumber),"mockerror"in t&&n(23,R=t.mockerror),"obfuscated"in t&&n(24,C=t.obfuscated),"plugins"in t&&n(32,I=t.plugins),"refetchonexpire"in t&&n(25,Z=t.refetchonexpire),"spamfilter"in t&&n(26,N=t.spamfilter),"strings"in t&&n(33,z=t.strings),"test"in t&&n(27,Y=t.test),"verifyurl"in t&&n(28,X=t.verifyurl),"workers"in t&&n(29,S=t.workers),"workerurl"in t&&n(30,A=t.workerurl),"$$scope"in t&&n(45,u=t.$$scope)},t.$$.update=()=>{32768&t.$$.dirty[0]&&n(12,i=!(null==h||!h.includes(".altcha.org")||null==h||!h.includes("apiKey=ckey_"))),1&t.$$.dirty[1]&&(a=p?dt(p):void 0),4&t.$$.dirty[1]&&n(44,l=z?dt(z):{}),8192&t.$$.dirty[1]&&n(11,s={ariaLinkLabel:ut,error:"Verification failed. Try again later.",expired:"Verification expired. Try again.",footer:`Protected by <a href="${xt}" target="_blank" aria-label="${l.ariaLinkLabel||ut}">ALTCHA</a>`,label:"I'm not a robot",verified:"Verified",verifying:"Verifying...",waitAlert:"Verifying... please wait.",...l}),192&t.$$.dirty[0]&&V("statechange",{payload:D,state:P}),32&t.$$.dirty[0]&&function(t){for(const t of W)"function"==typeof t.onErrorChange&&t.onErrorChange(K)}(),128&t.$$.dirty[0]&&function(t){for(const t of W)"function"==typeof t.onStateChange&&t.onStateChange(P);v&&P!==m.UNVERIFIED&&requestAnimationFrame((()=>{it()})),n(8,U=P===m.VERIFIED)}()},[d,v,x,k,E,K,D,P,U,H,F,s,i,function(){[m.UNVERIFIED,m.ERROR,m.EXPIRED].includes(P)?N&&!1===(null==_?void 0:_.reportValidity())?n(8,U=!1):C?ht():wt():n(8,U=!0)},ot,h,f,g,$,b,y,w,L,R,C,Z,N,Y,X,S,A,p,I,z,ht,pt,gt,mt,function(t){return W.find((e=>e.constructor.pluginName===t))},$t,bt,vt,yt,wt,l,u,c,function(){U=this.checked,n(8,U)},function(t){Ce[t?"unshift":"push"]((()=>{F=t,n(10,F)}))},function(t){Ce[t?"unshift":"push"]((()=>{H=t,n(9,H)}))}]}class Vn extends vn{constructor(t){super(),bn(this,t,Zn,Tn,Ut,{auto:0,blockspam:16,challengeurl:15,challengejson:31,debug:17,delay:18,expire:19,floating:1,floatinganchor:20,floatingoffset:21,hidefooter:2,hidelogo:3,name:4,maxnumber:22,mockerror:23,obfuscated:24,plugins:32,refetchonexpire:25,spamfilter:26,strings:33,test:27,verifyurl:28,workers:29,workerurl:30,clarify:34,configure:35,getConfiguration:36,getFloatingAnchor:37,getPlugin:38,getState:39,reset:40,setFloatingAnchor:41,setState:42,verify:43},In,[-1,-1,-1])}get auto(){return this.$$.ctx[0]}set auto(t){this.$$set({auto:t}),w()}get blockspam(){return this.$$.ctx[16]}set blockspam(t){this.$$set({blockspam:t}),w()}get challengeurl(){return this.$$.ctx[15]}set challengeurl(t){this.$$set({challengeurl:t}),w()}get challengejson(){return this.$$.ctx[31]}set challengejson(t){this.$$set({challengejson:t}),w()}get debug(){return this.$$.ctx[17]}set debug(t){this.$$set({debug:t}),w()}get delay(){return this.$$.ctx[18]}set delay(t){this.$$set({delay:t}),w()}get expire(){return this.$$.ctx[19]}set expire(t){this.$$set({expire:t}),w()}get floating(){return this.$$.ctx[1]}set floating(t){this.$$set({floating:t}),w()}get floatinganchor(){return this.$$.ctx[20]}set floatinganchor(t){this.$$set({floatinganchor:t}),w()}get floatingoffset(){return this.$$.ctx[21]}set floatingoffset(t){this.$$set({floatingoffset:t}),w()}get hidefooter(){return this.$$.ctx[2]}set hidefooter(t){this.$$set({hidefooter:t}),w()}get hidelogo(){return this.$$.ctx[3]}set hidelogo(t){this.$$set({hidelogo:t}),w()}get name(){return this.$$.ctx[4]}set name(t){this.$$set({name:t}),w()}get maxnumber(){return this.$$.ctx[22]}set maxnumber(t){this.$$set({maxnumber:t}),w()}get mockerror(){return this.$$.ctx[23]}set mockerror(t){this.$$set({mockerror:t}),w()}get obfuscated(){return this.$$.ctx[24]}set obfuscated(t){this.$$set({obfuscated:t}),w()}get plugins(){return this.$$.ctx[32]}set plugins(t){this.$$set({plugins:t}),w()}get refetchonexpire(){return this.$$.ctx[25]}set refetchonexpire(t){this.$$set({refetchonexpire:t}),w()}get spamfilter(){return this.$$.ctx[26]}set spamfilter(t){this.$$set({spamfilter:t}),w()}get strings(){return this.$$.ctx[33]}set strings(t){this.$$set({strings:t}),w()}get test(){return this.$$.ctx[27]}set test(t){this.$$set({test:t}),w()}get verifyurl(){return this.$$.ctx[28]}set verifyurl(t){this.$$set({verifyurl:t}),w()}get workers(){return this.$$.ctx[29]}set workers(t){this.$$set({workers:t}),w()}get workerurl(){return this.$$.ctx[30]}set workerurl(t){this.$$set({workerurl:t}),w()}get clarify(){return this.$$.ctx[34]}get configure(){return this.$$.ctx[35]}get getConfiguration(){return this.$$.ctx[36]}get getFloatingAnchor(){return this.$$.ctx[37]}get getPlugin(){return this.$$.ctx[38]}get getState(){return this.$$.ctx[39]}get reset(){return this.$$.ctx[40]}get setFloatingAnchor(){return this.$$.ctx[41]}get setState(){return this.$$.ctx[42]}get verify(){return this.$$.ctx[43]}}customElements.define("altcha-widget",yn(Vn,{auto:{},blockspam:{},challengeurl:{},challengejson:{},debug:{type:"Boolean"},delay:{},expire:{},floating:{},floatinganchor:{},floatingoffset:{},hidefooter:{type:"Boolean"},hidelogo:{type:"Boolean"},name:{},maxnumber:{},mockerror:{type:"Boolean"},obfuscated:{},plugins:{},refetchonexpire:{type:"Boolean"},spamfilter:{type:"Boolean"},strings:{},test:{type:"Boolean"},verifyurl:{},workers:{},workerurl:{}},["default"],["clarify","configure","getConfiguration","getFloatingAnchor","getPlugin","getState","reset","setFloatingAnchor","setState","verify"],!1)),globalThis.altchaCreateWorker=t=>t?new Worker(new URL(t)):new Pt,globalThis.altchaPlugins=globalThis.altchaPlugins||[];export{Vn as Altcha};
