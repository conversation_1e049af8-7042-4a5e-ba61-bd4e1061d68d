{% extends "cms-base.html" %}

{% block meta_description %}Les de nyeste artiklene og oppskriftene fra KETOLABBEN om kosthold, vektnedgang, og sunn livsstil.{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <nav class="post-filter" aria-label="Post filter">
                <a href="#" class="active" data-filter="all">
                    <i class="bi bi-grid"></i>
                    <span>Alle</span>
                </a>
                <a href="#" data-filter="oppskrift">
                    <i class="bi bi-journal-check"></i>
                    <span>Oppskrifter</span>
                </a>
                <a href="#" data-filter="blogg">
                    <i class="bi bi-journal-text"></i>
                    <span>Blogg</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Posts Grid -->
    <div class="row row-cols-1 row-cols-md-2 row-cols-xl-3 g-4" id="posts-container">
        {% for post in posts %}
        <div class="col">
            <div class="card h-100 shadow-sm" data-post-type="{{ post.post_type }}" data-post-id="{{ post.id }}">
                {% if post.featured_image %}
                <a href="/{{ post.url_slug }}" class="text-decoration-none card-img-container">
                    <img 
                        src="/api/placeholder/{{ post.featured_image.split('/')[-1] }}"
                        data-src="{{ post.featured_image }}"
                        alt="{{ post.title }}" 
                        class="card-img-top" 
                        width="100%" 
                        height="auto"
                        style="opacity: 0.5; transition: opacity 0.3s ease-in;"
                        {% if loop.index0 < 3 %}loading="eager" fetchpriority="high"{% endif %}
                    >
                </a>
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">
                        <a href="/{{ post.url_slug }}" class="text-decoration-none">{{ post.title }}</a>
                    </h5>
                    <p class="card-text text-muted small">
                        {% if post.created %}
                        <i class="bi bi-calendar3"></i> {{ post.created.strftime('%d.%m.%Y') }}
                        {% endif %}
                        {% if post.post_type %}
                        <span class="mx-2">|</span>
                        <i class="bi bi-bookmark"></i> {{ post.post_type|title }}
                        {% endif %}
                    </p>
                    <p class="card-text">{{ post.description or '' }}</p>
                    {% if current_user.is_authenticated %}
                    <div class="mt-3 d-flex gap-2">
                        <a href="/cms-edit-{{ post.id }}.html" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil"></i> Rediger
                        </a>
                        <button onclick="deletePost({{ post.id }})" class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-trash"></i> Slett
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Loading Spinner (hidden by default) -->
    <div class="text-center mt-5 d-none" id="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Laster...</span>
        </div>
    </div>
</div>

<script>
    const postsContainer = document.getElementById('posts-container');
    let currentFilter = 'all';

    // Get filter from URL parameter
    function getFilterFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        const filter = urlParams.get('filter');
        if (filter === 'blogg' || filter === 'oppskrift') {
            return filter;
        }
        return 'all';
    }

    // Update URL with current filter
    function updateUrlWithFilter(filter) {
        const url = new URL(window.location);
        if (filter === 'all') {
            url.searchParams.delete('filter');
        } else {
            url.searchParams.set('filter', filter);
        }
        window.history.pushState({}, '', url);
    }

    // Filter posts based on type
    function filterPosts(filter) {
        const cards = document.querySelectorAll('#posts-container .col');
        let visibleCount = 0;
        
        cards.forEach(card => {
            const postType = card.querySelector('.card').dataset.postType;
            const img = card.querySelector('img');
            
            if (filter === 'all' || postType === filter) {
                card.classList.remove('d-none');
                // Set first three visible posts' images to eager loading
                if (visibleCount < 3 && img) {
                    img.setAttribute('loading', 'eager');
                    img.setAttribute('fetchpriority', 'high');
                    // Trigger immediate optimization
                    const event = new Event('optimizeImage');
                    img.dispatchEvent(event);
                }
                visibleCount++;
            } else {
                card.classList.add('d-none');
            }
        });
    }

    // Add click handlers to filter buttons
    document.querySelectorAll('.post-filter a').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const filter = this.dataset.filter;
            
            // Update active state
            document.querySelectorAll('.post-filter a').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update filter and URL
            currentFilter = filter;
            updateUrlWithFilter(filter);
            filterPosts(filter);
        });
    });

    // Delete post function
    async function deletePost(postId) {
        if (!confirm('Er du sikker på at du vil slette dette innlegget?')) {
            return;
        }

        try {
            const response = await fetch(`/cms-delete-${postId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Remove the post card from the DOM
            const card = document.querySelector(`[data-post-id="${postId}"]`).closest('.col');
            card.remove();

        } catch (error) {
            console.error('Error:', error);
            alert('Det oppstod en feil under sletting. Vennligst prøv igjen.');
        }
    }

    // Initialize filter from URL
    const initialFilter = getFilterFromUrl();
    if (initialFilter !== 'all') {
        const filterButton = document.querySelector(`[data-filter="${initialFilter}"]`);
        if (filterButton) {
            filterButton.classList.add('active');
            document.querySelector('[data-filter="all"]').classList.remove('active');
            filterPosts(initialFilter);
        }
    }
</script>
{% endblock %}
