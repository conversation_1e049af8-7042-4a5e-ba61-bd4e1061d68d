<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Capability Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .test-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .test-name {
            font-weight: bold;
            display: inline-block;
            width: 200px;
        }
        .test-result {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
        }
        .pass {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .fail {
            background-color: #f2dede;
            color: #a94442;
        }
        .score {
            font-size: 1.2em;
            font-weight: bold;
            margin-top: 20px;
            padding: 10px;
            text-align: center;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        #mouse-area {
            height: 100px;
            background-color: #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .api-response {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .button-container {
            margin-top: 20px;
            text-align: center;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <h1>Browser Capability Testing</h1>
    <p>This page tests various browser capabilities to determine if your browser is behaving like a normal browser or a bot.</p>
    
    <div id="mouse-area">
        Move your mouse here to test mouse movement detection
    </div>
    
    <div class="test-container">
        <h2>Test Results</h2>
        <div id="results">
            <div class="test-item">
                <span class="test-name">Canvas Fingerprinting:</span>
                <span class="test-result" id="canvas-result">Testing...</span>
            </div>
            <div class="test-item">
                <span class="test-name">WebGL Support:</span>
                <span class="test-result" id="webgl-result">Testing...</span>
            </div>
            <div class="test-item">
                <span class="test-name">Audio Context:</span>
                <span class="test-result" id="audio-result">Testing...</span>
            </div>
            <div class="test-item">
                <span class="test-name">CSS Features:</span>
                <span class="test-result" id="css-result">Testing...</span>
            </div>
            <div class="test-item">
                <span class="test-name">Storage Access:</span>
                <span class="test-result" id="storage-result">Testing...</span>
            </div>
            <div class="test-item">
                <span class="test-name">Mouse Movement:</span>
                <span class="test-result" id="mouse-result">Testing...</span>
            </div>
        </div>
        <div class="score" id="score">Calculating score...</div>
    </div>
    
    <div class="button-container">
        <button id="send-test">Send Test Results</button>
        <button id="check-status">Check Bot Status</button>
    </div>
    
    <div class="api-response" id="api-response">API response will appear here...</div>
    
    <script>
        // This is just for the test page display
        function updateTestResult(id, passed) {
            const element = document.getElementById(id);
            element.textContent = passed ? 'PASS' : 'FAIL';
            element.className = 'test-result ' + (passed ? 'pass' : 'fail');
        }
        
        function updateScore(score) {
            const scoreElement = document.getElementById('score');
            scoreElement.textContent = `Overall Score: ${score}%`;
            
            if (score >= 70) {
                scoreElement.style.backgroundColor = '#dff0d8';
                scoreElement.style.color = '#3c763d';
            } else {
                scoreElement.style.backgroundColor = '#f2dede';
                scoreElement.style.color = '#a94442';
            }
        }
        
        // Run tests and update UI
        async function runTests() {
            // Test canvas fingerprinting
            const canvasResult = testCanvasFingerprinting();
            updateTestResult('canvas-result', canvasResult);
            
            // Test WebGL support
            const webglResult = testWebGLSupport();
            updateTestResult('webgl-result', webglResult);
            
            // Test Audio Context
            const audioResult = testAudioContext();
            updateTestResult('audio-result', audioResult);
            
            // Test CSS support
            const cssResult = testCSSSupport();
            updateTestResult('css-result', cssResult);
            
            // Test storage access
            const storageResult = testStorageAccess();
            updateTestResult('storage-result', storageResult);
            
            // Test mouse movement (wait for result)
            const mouseResult = await testMouseMovement();
            updateTestResult('mouse-result', mouseResult);
            
            // Calculate score
            const results = [canvasResult, webglResult, audioResult, cssResult, storageResult, mouseResult];
            const passedTests = results.filter(Boolean).length;
            const score = Math.round((passedTests / results.length) * 100);
            
            updateScore(score);
            
            return {
                score: score,
                details: {
                    canvas: canvasResult,
                    webgl: webglResult,
                    audio: audioResult,
                    css: cssResult,
                    storage: storageResult,
                    mouseMovement: mouseResult
                }
            };
        }
        
        // Copy test functions from bot-detection.js
        function testCanvasFingerprinting() {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                if (!ctx) {
                    return false;
                }
                
                canvas.width = 200;
                canvas.height = 50;
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#123456';
                ctx.fillText('Canvas Test 👍', 2, 2);
                
                ctx.fillStyle = '#654321';
                ctx.fillRect(100, 25, 50, 10);
                
                const dataURL = canvas.toDataURL();
                
                return dataURL.length > 50 && dataURL.indexOf('data:image/png') === 0;
            } catch (e) {
                console.log('Canvas test failed:', e);
                return false;
            }
        }
        
        function testWebGLSupport() {
            try {
                const canvas = document.createElement('canvas');
                return !!(
                    window.WebGLRenderingContext && 
                    (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
                );
            } catch (e) {
                return false;
            }
        }
        
        function testAudioContext() {
            try {
                return !!(window.AudioContext || window.webkitAudioContext);
            } catch (e) {
                return false;
            }
        }
        
        function testCSSSupport() {
            const div = document.createElement('div');
            
            if ('flexBasis' in div.style || 'webkitFlexBasis' in div.style || 'mozFlexBasis' in div.style) {
                return true;
            }
            
            if ('grid' in div.style || 'msGrid' in div.style) {
                return true;
            }
            
            if ('transform' in div.style || 'webkitTransform' in div.style || 'mozTransform' in div.style) {
                return true;
            }
            
            return false;
        }
        
        function testStorageAccess() {
            try {
                const testKey = '_bot_detection_test_';
                localStorage.setItem(testKey, 'test');
                const result = localStorage.getItem(testKey) === 'test';
                localStorage.removeItem(testKey);
                return result;
            } catch (e) {
                return false;
            }
        }
        
        function testMouseMovement(timeoutMs = 3000) {
            return new Promise(resolve => {
                let moved = false;
                
                function handleMouseMove() {
                    moved = true;
                    document.removeEventListener('mousemove', handleMouseMove);
                    resolve(true);
                }
                
                document.addEventListener('mousemove', handleMouseMove);
                
                setTimeout(() => {
                    document.removeEventListener('mousemove', handleMouseMove);
                    if (!moved) {
                        resolve(false);
                    }
                }, timeoutMs);
            });
        }
        
        // Add event listeners for buttons
        document.getElementById('send-test').addEventListener('click', async () => {
            const results = await runTests();
            const responseElement = document.getElementById('api-response');
            responseElement.textContent = 'Sending results to server...';
            
            try {
                const response = await fetch('/api/browser-capabilities', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(results),
                    credentials: 'same-origin'
                });
                
                const data = await response.json();
                responseElement.textContent = 'Server response:\n' + JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = 'Error sending data to server:\n' + error.message;
            }
        });
        
        document.getElementById('check-status').addEventListener('click', async () => {
            const responseElement = document.getElementById('api-response');
            responseElement.textContent = 'Checking bot status...';
            
            try {
                // Make a request to a page that will run bot detection
                const response = await fetch('/browser-status-check', {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                const data = await response.text();
                responseElement.textContent = 'Bot status check response:\n' + data;
            } catch (error) {
                responseElement.textContent = 'Error checking bot status:\n' + error.message;
            }
        });
        
        // Run tests when page loads
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
