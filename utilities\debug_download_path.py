#!/usr/bin/env python3
"""
Debug the download file path
"""
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_download_path():
    """Debug the download file path construction"""
    print("Debugging download file path...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Get the download file name
    download_file_name = os.getenv('DOWNLOAD_FILE_NAME')
    print(f"DOWNLOAD_FILE_NAME: {download_file_name}")
    
    # Construct the path as done in download.py
    current_file_dir = os.path.dirname(__file__)  # This script's directory
    parent_dir = os.path.dirname(current_file_dir)  # Parent directory
    file_path = os.path.join(parent_dir, 'assets', 'downloads', download_file_name)
    
    print(f"Current script directory: {current_file_dir}")
    print(f"Parent directory: {parent_dir}")
    print(f"Constructed file path: {file_path}")
    print(f"File exists: {os.path.exists(file_path)}")
    
    if os.path.exists(file_path):
        file_size = os.path.getsize(file_path)
        print(f"File size: {file_size} bytes ({file_size / (1024*1024):.1f} MB)")
    
    # Let's also check the path as constructed in the actual download.py
    # The download.py uses os.path.dirname(os.path.dirname(__file__))
    # which would be from modules/download.py perspective
    
    modules_dir = os.path.join(current_file_dir, 'modules')
    download_py_parent = os.path.dirname(modules_dir)  # This should be the project root
    actual_file_path = os.path.join(download_py_parent, 'assets', 'downloads', download_file_name)
    
    print(f"\nActual path from download.py perspective:")
    print(f"Modules directory: {modules_dir}")
    print(f"Download.py parent: {download_py_parent}")
    print(f"Actual file path: {actual_file_path}")
    print(f"Actual file exists: {os.path.exists(actual_file_path)}")
    
    if os.path.exists(actual_file_path):
        file_size = os.path.getsize(actual_file_path)
        print(f"Actual file size: {file_size} bytes ({file_size / (1024*1024):.1f} MB)")
    
    # Search for any PDF files in the project
    print(f"\nSearching for PDF files in project...")
    for root, dirs, files in os.walk(current_file_dir):
        for file in files:
            if file.endswith('.pdf'):
                full_path = os.path.join(root, file)
                file_size = os.path.getsize(full_path)
                print(f"Found PDF: {full_path} ({file_size} bytes)")

if __name__ == "__main__":
    debug_download_path()