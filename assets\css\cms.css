/* Common CMS Styles */
.content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2c3e50;
}

.content p {
    margin-bottom: 1.5rem;
}

.content h2 {
    font-size: 1.8rem;
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.content h3 {
    font-size: 1.5rem;
    margin-top: 2.5rem;
    margin-bottom: 1.2rem;
    color: #2c3e50;
}

.content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 2rem 0;
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.content ul, .content ol {
    margin-bottom: 1.5rem;
    padding-left: 1.2rem;
}

.content li {
    margin-bottom: 0.8rem;
}

.content a {
    color: var(--bs-primary);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s;
}

.content a:hover {
    border-bottom-color: var(--bs-primary);
}

/* Page Header */
.display-5 {
    font-weight: 300;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

/* Post Card Styles */
.post-card {
    border: none;
    transition: all 0.3s ease;
    background: #fff;
    height: 100%;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.04);
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.post-card a {
    color: inherit;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.post-card .card-img-container {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
    background-color: #f8f9fa;
}

.post-card .card-img-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.5s ease;
}

.post-card:hover .card-img-top {
    transform: scale(1.05);
}

.post-card .post-type-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    border-radius: 2rem;
    font-size: 0.8rem;
    color: #2c3e50;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.post-card:hover .post-type-badge {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.post-card .card-body {
    padding: 1.75rem;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
}

.post-card .card-title {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    color: #2c3e50;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-weight: 500;
}

.post-card .card-text {
    color: #4a5568;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.25rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    opacity: 1;
}

.post-meta {
    font-size: 0.85rem;
    color: #4a5568;
    margin-top: auto;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.post-meta i {
    font-size: 0.9em;
    opacity: 0.7;
}

/* Post Filter */
.post-filter {
    display: flex;
    gap: 2.5rem;
    align-items: center;
    margin-bottom: 2rem;
}

.post-filter a {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.85rem;
    padding: 0.25rem 0;
    transition: color 0.2s ease;
}

.post-filter a:hover {
    color: #2c3e50;
}

.post-filter a.active {
    color: #2c3e50;
}

.post-filter i {
    font-size: 0.9rem;
}

.post-filter span {
    position: relative;
}

.post-filter a.active span::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: currentColor;
    opacity: 0.3;
}

/* Recipe Details */
.recipe-details {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.recipe-meta-item {
    text-align: center;
    padding: 1rem;
}

.recipe-meta-item i {
    font-size: 1.5rem;
    color: var(--bs-primary);
    margin-bottom: 0.5rem;
}

/* Ingredients List */
.ingredients-list {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.ingredient-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.ingredient-item:last-child {
    border-bottom: none;
}

/* CMS Edit Form */
.cms-form {
    max-width: 800px;
    margin: 0 auto;
}

.cms-form .form-control {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    border-radius: 0.375rem;
}

.cms-form .form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.cms-form label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Tips Section */
.tips {
    font-size: 1rem;
    line-height: 1.6;
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.tips h3 {
    color: var(--bs-primary);
    margin-bottom: 1rem;
}

/* Loading Spinner */
#loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #95a5a6 !important;
    opacity: 0.5;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .content {
        font-size: 1rem;
    }
    
    .content h2 {
        font-size: 1.5rem;
        margin-top: 2rem;
    }
    
    .content h3 {
        font-size: 1.3rem;
        margin-top: 1.8rem;
    }

    .post-card .card-title {
        font-size: 1.1rem;
    }
    
    .post-card .card-text {
        font-size: 0.9rem;
    }

    .recipe-meta-item {
        margin-bottom: 1rem;
    }
}

/* Draft List Styles */
.draft-list .draft-item {
    transition: transform 0.2s, box-shadow 0.2s;
    border: none;
    margin-bottom: 1rem;
}

.draft-list .draft-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.draft-list .draft-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Common Card Styles */
.card {
    border: none;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.bg-light {
    background-color: #f8f9fa !important;
}

.border-bottom {
    border-color: #e9ecef !important;
}

.border-bottom:last-child {
    border-bottom: none !important;
}

/* Card Header Styles with improved contrast */
.card-header.bg-primary {
    background-color: #1a202c !important; /* Dark gray for better contrast and design consistency */
}

.card-header.bg-primary.text-white {
    color: #ffffff !important;
}

/* Ensure headings in card headers have sufficient contrast */
.card-header h2.h5 {
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* AI Toolbar Styles */
.ai-toolbar {
    background: var(--ck-color-base-background);
    border: 1px solid var(--ck-color-base-border);
    border-radius: 4px;
    padding: 4px;
    margin-bottom: 8px;
}

.ai-toolbar .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.ai-toolbar .btn {
    background: var(--ck-color-button-default-background);
    border: 1px solid var(--ck-color-base-border);
    color: var(--ck-color-text);
    padding: 4px 10px;
    font-size: 0.9em;
    line-height: 1.5;
    border-radius: 2px;
    transition: all 0.15s ease;
}

.ai-toolbar .btn:hover {
    background: var(--ck-color-button-default-hover-background);
}

.ai-toolbar .btn:active {
    background: var(--ck-color-button-default-active-background);
}

.ai-toolbar .btn i {
    font-size: 0.9em;
}

#ai-spinner {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin-top: 4px;
    background: var(--ck-color-base-background);
    border: 1px solid var(--ck-color-base-border);
    border-radius: 2px;
    font-size: 0.9em;
    color: var(--ck-color-text);
}

#ai-spinner .spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 2px;
}

/* Font Definition */
@font-face {
    font-family: 'Shadows Into Light Two';
    src: url('/assets/fonts/Shadows Into Light Two-30857b58eeeb70ebfa0e112f6c7482db.woff2') format('woff2'),
         url('/assets/fonts/Shadows Into Light Two-9316d4323b58ad42db218fd7263d35ff.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Social Share Section */
.social-share-section {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem 0;
}

.share-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.social-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background-color: #fff;
    color: #666;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    padding: 0;
}

@media (max-width: 576px) {
    .share-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .share-text {
        margin-bottom: 0.5rem;
    }
    
    .social-btn {
        width: 36px;
        height: 36px;
    }
}

.share-text {
    font-family: 'Shadows Into Light Two', cursive;
    font-size: 20px;
    color: #ff4444;
    position: relative;
    text-shadow: 1px 1px 0 rgba(255,255,255,0.8);
    margin: 0;
    white-space: nowrap;
}

.share-text::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255,68,68,0.2) 20%, 
        rgba(255,68,68,0.5) 50%, 
        rgba(255,68,68,0.2) 80%, 
        transparent 100%
    );
}

.share-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 0.7rem 1.2rem;
    background: linear-gradient(145deg, #ffffff, #f5f5f5);
    border-radius: 10px;
    box-shadow: 
        -6px -6px 10px rgba(255,255,255,0.8),
        6px 6px 10px rgba(0,0,0,0.03),
        inset 2px 2px 4px rgba(255,255,255,0.9),
        inset -2px -2px 4px rgba(0,0,0,0.05);
    border: 1px solid rgba(0,0,0,0.05);
}

.social-btn {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: none;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-btn i {
    position: relative;
    z-index: 2;
}

.social-btn::before {
    content: '';
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255,255,255,0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-btn:hover::before {
    opacity: 1;
}

.btn-facebook {
    background-color: #4267B2;
    box-shadow: 0 3px 10px rgba(66,103,178,0.2);
}

.btn-instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    box-shadow: 0 3px 10px rgba(225,48,108,0.2);
}

.btn-snapchat {
    background-color: #FFFC00;
    color: #000;
    box-shadow: 0 3px 10px rgba(255,252,0,0.2);
}

.social-btn:hover {
    transform: translateY(-2px);
}

.btn-facebook:hover {
    background-color: #4267B2;
    color: white !important;
    box-shadow: 0 5px 15px rgba(66,103,178,0.3);
}

.btn-instagram:hover {
    color: white !important;
    box-shadow: 0 5px 15px rgba(225,48,108,0.3);
}

.btn-snapchat:hover {
    color: #000 !important;
    box-shadow: 0 5px 15px rgba(255,252,0,0.3);
}

/* Analytics Dashboard */
.stat-card {
    transition: transform 0.2s;
}
.stat-card:hover {
    transform: translateY(-5px);
}
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1.5rem;
}
.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}
.daterange-picker {
    width: 300px;
}

.chart-container {
    position: relative;
    height: 300px;  /* Fixed height */
    width: 100%;    /* Full width of parent */
    margin-bottom: 20px;
}

.metric-box {
    padding: 1rem;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}

.metric-box h6 {
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.metric-box p {
    margin-bottom: 0;
    font-size: 1.25rem;
    font-weight: bold;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-title {
    color: #495057;
    font-weight: 600;
}

/* Card styles 
.card {
    display: flex;
    flex-direction: column;
}

.card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-text {
    flex-grow: 1;
}

/* Image container styles */
.card-img-container {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
    background-color: #f8f9fa;
}

.featured-image-container {
    position: relative;
    aspect-ratio: 21/9;
    overflow: hidden;
    background-color: #f8f9fa;
    margin-bottom: 2rem;
}

.post-content .image-container {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
    background-color: #f8f9fa;
    margin: 1rem 0;
}

/* Image styles */
.card-img-container img,
.featured-image-container img,
.post-content .image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.5;
    transition: opacity 0.3s ease-in;
}

.card-img-container img.loaded,
.featured-image-container img.loaded,
.post-content .image-container img.loaded {
    opacity: 1;
}