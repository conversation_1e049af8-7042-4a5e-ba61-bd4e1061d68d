#!/usr/bin/env python3
"""
Test if the webhook route is accessible and what methods are allowed
"""
import requests
import json

def test_webhook_route_methods():
    """Test different HTTP methods on the webhook route"""
    print("Testing webhook route accessibility...")
    
    base_url = 'https://127.0.0.1:8282/webhook'
    
    # Test GET request (should fail since webhook only accepts POST)
    print("\n1. Testing GET request (should return 405 Method Not Allowed):")
    try:
        response = requests.get(base_url, verify=False, timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Test POST with empty body
    print("\n2. Testing POST with empty body:")
    try:
        response = requests.post(base_url, verify=False, timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Test POST with invalid JSON
    print("\n3. Testing POST with invalid JSON:")
    try:
        response = requests.post(
            base_url, 
            data="invalid json{", 
            headers={'Content-Type': 'application/json'},
            verify=False, 
            timeout=5
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Test POST with valid JSON but no signature
    print("\n4. Testing POST with valid JSON (no signature):")
    try:
        test_data = {"test": "data", "type": "test.event"}
        response = requests.post(
            base_url,
            data=json.dumps(test_data),
            headers={'Content-Type': 'application/json'},
            verify=False,
            timeout=5
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {str(e)}")

def test_webhook_with_minimal_stripe_event():
    """Test webhook with minimal Stripe-like event structure"""
    print("\n5. Testing POST with minimal Stripe event structure:")
    
    minimal_event = {
        "id": "evt_test_minimal",
        "object": "event",
        "type": "test.event",
        "data": {
            "object": {
                "id": "test_object"
            }
        }
    }
    
    try:
        response = requests.post(
            'https://127.0.0.1:8282/webhook',
            data=json.dumps(minimal_event),
            headers={'Content-Type': 'application/json'},
            verify=False,
            timeout=5
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        # Check if debug file was created
        import os
        debug_dir = "instance/webhook_debug"
        if os.path.exists(debug_dir):
            debug_files = [f for f in os.listdir(debug_dir) if f.endswith('.json')]
            if debug_files:
                print(f"   Debug files created: {len(debug_files)}")
                # Show the latest file
                latest_file = max(debug_files, key=lambda f: os.path.getctime(os.path.join(debug_dir, f)))
                print(f"   Latest debug file: {latest_file}")
            else:
                print("   No debug files created")
        else:
            print("   Debug directory doesn't exist")
            
    except Exception as e:
        print(f"   Error: {str(e)}")

if __name__ == "__main__":
    print("Webhook Route Test")
    print("=" * 50)
    
    test_webhook_route_methods()
    test_webhook_with_minimal_stripe_event()
    
    print("\n" + "=" * 50)
    print("Test completed!")