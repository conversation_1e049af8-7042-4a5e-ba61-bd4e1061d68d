from flask import (
    render_template,
    request,
    redirect,
    url_for,
    flash,
    session
)
from functools import wraps
from .security import (
    sanitize_input, 
    validate_altcha, 
    get_client_ip, 
    is_potential_spam
)
from .models import Auth, get_db, db
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_login import login_user, logout_user, login_required, current_user
from .forms import LoginForm
from .logger import get_security_logger
import time
import logging
import os
from datetime import datetime

# Configure logging for login activities
security_logger = get_security_logger()

# Create logs directory if it doesn't exist
log_dir = os.path.join(os.path.dirname(__file__), '..', 'logs')
os.makedirs(log_dir, exist_ok=True)

class LoginManager:
    def __init__(self):
        self.spam_block_duration = 30 * 60  # 30 minutes block time
        self.spam_ips = {}  # Track blocked IPs
        self.failed_login_attempts = {}  # Track failed login attempts per IP

    def log_suspicious_activity(self, client_ip, activity_type, details=None):
        """
        Log suspicious login-related activities
        
        Args:
            client_ip (str): IP address of the client
            activity_type (str): Type of suspicious activity
            details (dict, optional): Additional details about the activity
        """
        log_message = f"Suspicious {activity_type} detected"
        
        if details:
            log_message += f" - {details}"
        
        security_logger.warning(log_message, extra={'client_ip': client_ip})

    def login_required(self, f):
        """
        Decorator to require login for specific routes
        """
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not session.get('logged_in'):
                flash('Du må logge inn først.', 'error')
                return redirect(url_for('login'))
            return f(*args, **kwargs)
        return decorated_function

    def handle_login(self):
        """
        Handle login route logic with input sanitization and security checks
        """
        if current_user.is_authenticated:
            return redirect(url_for('cms.cms_edit'))  # Redirect to cms-edit.html if already logged in

        # Get client IP
        client_ip = get_client_ip(request)

        # Track and limit failed login attempts
        if client_ip not in self.failed_login_attempts:
            self.failed_login_attempts[client_ip] = {
                'count': 0,
                'last_attempt': datetime.now()
            }
        
        current_attempts = self.failed_login_attempts[client_ip]
        time_since_last_attempt = datetime.now() - current_attempts['last_attempt']
        
        # Reset attempts if more than 1 hour has passed
        if time_since_last_attempt.total_seconds() > 3600:
            current_attempts['count'] = 0
        
        # Check if IP is currently blocked
        if client_ip in self.spam_ips:
            block_time = self.spam_ips[client_ip]
            if block_time > time.time():
                self.log_suspicious_activity(client_ip, 'blocked_ip_attempt')
                flash('For mange forsøk. Prøv igjen senere.', 'error')
                return render_template('login.html', form=LoginForm())

        if request.method == 'POST':
            form = LoginForm(request.form)
            if not form.validate():
                flash('Brukernavn og passord er påkrevd.', 'error')
                return render_template('login.html', form=form)
                
            # Check honeypot field - if it's checked, it's likely a bot
            if form.checkbox.data:
                self.log_suspicious_activity(client_ip, 'bot_detected', {'reason': 'honeypot_checked'})
                flash('Ugyldig forespørsel. Prøv igjen.', 'error')
                return render_template('login.html', form=form)

            # Get and sanitize input
            username = sanitize_input(
                form.username.data,
                input_type=str,
                max_length=120,  # Match the database field length
                allow_html=False,  # Never allow HTML in usernames
                default=None  # Return None if invalid to trigger validation error
            )
            if not username:
                self.log_suspicious_activity(client_ip, 'invalid_username', {'reason': 'sanitization_failed'})
                flash('Ugyldig brukernavn format.', 'error')
                return render_template('login.html', form=form)

            # For password, we only check length but don't sanitize to preserve exact characters
            if not form.password.data or len(form.password.data) > 255:  # Match the database field length
                self.log_suspicious_activity(client_ip, 'invalid_password', {'reason': 'length_validation_failed'})
                flash('Ugyldig passord format.', 'error')
                return render_template('login.html', form=form)
            
            password = form.password.data
            
            # Check for potential spam/abuse
            is_spam, score, error = is_potential_spam(client_ip)
            if is_spam:
                self.log_suspicious_activity(client_ip, 'potential_spam', {'score': score, 'error': error})
                flash('Ugyldig forespørsel. Prøv igjen.', 'error')
                return render_template('login.html', form=form)

            # Verify credentials using database
            db = get_db()
            try:
                user = Auth.query.filter_by(username=username).first()
                if user and Auth.verify_password(db, username, password):
                    # Log in the user with Flask-Login
                    login_user(user)
                    session.permanent = True  # Use permanent session with lifetime from config
                    session['username'] = username
                    session['logged_in'] = True
                    security_logger.info(f"Successful login for user: {username}", 
                                        extra={'event_type': 'login_success'})
                    flash(f'Innlogging vellykket for {username}!', 'success')
                    
                    # Redirect to the next page or index
                    next_page = request.args.get('next')
                    # Validate next parameter to prevent open redirect vulnerability
                    if next_page and next_page.startswith('/') and '//' not in next_page:
                        # Handle analytics dashboard URL
                        if next_page.startswith('/analytics.html'):
                            return redirect(next_page)
                    # Default redirect
                    return redirect(url_for('cms.cms_edit'))
                else:
                    # Increment failed attempts
                    current_attempts['count'] += 1
                    current_attempts['last_attempt'] = datetime.now()
                    
                    # Log failed login attempt
                    self.log_suspicious_activity(
                        client_ip, 
                        'failed_login_attempt', 
                        {
                            'username': username, 
                            'attempt_count': current_attempts['count']
                        }
                    )
                    security_logger.warning(
                        f"Failed login attempt for username: {username}",
                        extra={
                            'event_type': 'login_failure',
                            'failure_reason': 'invalid_credentials'
                        }
                    )
                    
                    # Block IP after multiple failed attempts
                    if current_attempts['count'] >= 5:
                        self.spam_ips[client_ip] = time.time() + self.spam_block_duration
                        self.log_suspicious_activity(
                            client_ip, 
                            'ip_blocked_multiple_failed_attempts', 
                            {'block_duration': self.spam_block_duration}
                        )
                    
                    flash('Ugyldig innlogging. Prøv igjen.', 'error')
            except Exception as e:
                security_logger.error(f"Login error: {e}")
                flash('En feil oppstod under innlogging. Prøv igjen senere.', 'error')
            finally:
                db.close()

        return render_template('login.html', form=LoginForm())

    def logout(self):
        """
        Handle logout route logic
        """
        if current_user.is_authenticated:
            username = session.get('username')
            if username:
                security_logger.info(f"User logged out: {username}")
            # Clear all session data
            session.clear()
            logout_user()  # Flask-Login logout
            # Ensure no new session is created
            if session:
                session.clear()
                session.permanent = False
            flash('Du har blitt logget ut.', 'info')
            return redirect('/login.html')  # Use direct URL to avoid session creation
        return redirect('/login.html')

def init_login_routes(app):
    """
    Initialize login-related routes for the Flask app
    """
    login_manager = LoginManager()

    # Initialize Flask-Limiter with memory storage
    limiter = Limiter(
        key_func=get_remote_address,
        app=app,
        default_limits=[]  # Remove default limits
    )

    # Login routes - support login.html
    @app.route('/login.html', methods=['GET', 'POST'])
    @limiter.limit("5 per minute")  # Limit to 5 login attempts per minute
    def login():
        return login_manager.handle_login()

    # Logout route
    @app.route('/logout.html')
    @login_manager.login_required
    def logout():
        return login_manager.logout()

    return login_manager
